<template>
    <div class="post">
        <template v-if="options && options.post_as_story">
            <div class="attachment-container position-relative d-flex align-items-center justify-content-center mt-4 overflow-hidden" v-if="attachment">
                <div class="w-100">

                    <video class="w-100" :title="attachment.name" controls v-if="attachment.type.includes('video')">
                        <source :src="attachment.url" />
                    </video>
                    <img class="w-100" :src="attachment.url" :title="attachment.name" v-else>
                    <div class="position-absolute text-white w-100" style="top:0px; left: 0px; z-index: 1">
                        
                        <div class="range-element mx-3 border-bottom"></div>
                        <div class="position-absolute overlay-element bg-white mx-3 w-50"></div>
                        <div class="d-flex align-items-center justify-content-between story-info">
                            <div class="d-flex align-items-center">
                                <img class="rounded-circle"  alt="avatar" :src="account.image"/>
                                <div class="mb-0 user-name">
                                    <span class="font-weight-600">
                                        {{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }} 
                                    </span>
                                    <span class="small-2">Just now </span>
                                </div>
                            </div>
                            <div>
                                <i class="ph ph-dots-three ph-bold ph-lg mr-4"></i>
                                <i class="cross-icon ph ph-x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="d-flex justify-content-between pl-3 pr-2 pt-4 mb-2">
                <div class="d-flex align-items-end">
                    <img class="facebook-avatar rounded-circle"  alt="avatar" :src="account.image"/>
                    <div class="ml-2">
                        <div class="mb-0 fullname">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}</div>
                        <div class="d-flex font-weight-400 timestamp">
                            <span class="mr-1">Just now •</span><i class="ph ph-globe-hemisphere-west ph-fill"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <i class="ph ph-dots-three ph-bold ph-lg"></i>
                </div>
            </div>

            <RichText :value="content" :readonly="true" class="text px-3 mr-2 mb-0" :marks="richTextMarksSchema" />

        
            <div class="overflow-hidden mt-2" v-if="attachments.length">
                <div class="gallery gallery-4-type-2" v-if="attachments.length > 1">
                    <div v-for="(attachment, index) in attachments" class="gallery-item" :key="index">
                        <video controls :title="attachment.name"  v-if="attachment.type.includes('video')">
                            <source :src="attachment.url" />
                        </video>
                        <img
                            :src="attachment.url" :alt="attachment.name" :title="attachment.name"
                            v-else
                        />
                    </div>
                </div>
                <div class="w-100 text-center" v-else-if="attachments.length === 1">
                    <video class="w-100" controls :title="attachments[0].name"  v-if="attachments[0].type.includes('video')">
                        <source :src="attachments[0].url" />
                    </video>
                    <img class="w-100" :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name"
                        v-else/>
                </div>
            </div>
        
        
            <LinkPreview
                v-if="link && attachments.length === 0"
                :url="options.link || link"
                type="facebook" />
        
            <div class="post-footer">
                <div class="post-footer-option d-flex justify-content-between h-100">
                        <div class="d-flex align-items-center">
                            <i class="ph ph-thumbs-up ph-lg"></i>
                            <div class="action">Like</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ph ph-chat-circle ph-lg"></i>
                            <div class="action">Comment</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ph ph-whatsapp-logo ph-lg"></i>
                            <div class="action">Send</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ph ph-share-fat ph-lg"></i>
                            <div class="action">Share</div>
                        </div>
                </div>
            </div>

        </template>

    </div>
</template>

<script>
import _ from "lodash";
import LinkPreview from "../LinkPreview.vue";
import { sanitizeHtml } from "../../../../components";
import RichText from "../../common/RichText.vue";

export default {
    name: "FacebookPost",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    data() {
        return {
            showFull: false
        };
    },
    computed: {
        content() {
            let text = this.text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            
            return sanitizeHtml(this.showFull ? text : text.length >= 110 ? (`${text.substring(0, 110)}<span class="_see_all cursor-pointer" style="color:#63686B;">...see more</span>`) : text);

        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        },
        attachment(){
            return this.attachments && this.attachments[0];
        },
    },
    methods: {
        showMore() {
            this.showFull = true;
            return false;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>

<style lang="scss" scoped>
.post {
    font-family: Roboto;
}
.facebook-avatar{
    width: 44px;
    height: 44px;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    font-weight: 400;
    font-size: 17px;
    line-height: 22.1px;
    color: #080808;
    a {
        color: #0065CF !important;
        font-weight: 600 !important;
    }
}
.fullname {
    color: #080808;
    font-weight: 600;
    font-size: 18px;
    line-height: 23.4px;
    &:hover {
        text-decoration: none;
    }
}
.timestamp {
    font-size: 14px;
    color: #63686B;
    line-height: 18.2px;
}
.cross-icon{
    font-size: 30px;
}
.gallery-item{
    width: 210px !important;
    height: 210px !important;
}
.post-footer {
    color: #63686B;
    .post-footer-option{
        padding: 12px 16px 20px 18px;
        i{
            margin-right: 6px;
        }
    }
    @media (max-width: 480px) {
        .post-footer-option > div {
            margin-right: 10px;
        }
    }
    .action {
        font-weight: 500;
        font-size: 14px;
        line-height: 18.2px;
    }
}
.attachment-container{
    height: 754px;
    background: #000000;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;
    .range-element{
        border-color: gray !important;
        height: 2px;
        margin-top: 10px;
        background: gray;
        border-radius: 20px;
    }
    .overlay-element{
        top: 10px;
        height: 2px;
        border-radius: 20px;
    }
    .story-info{
        margin: 9px 12px 8px 8px !important;
        img{
            width: 38px;
            height: 38px;
        }
        .user-name{
            margin-left: 10px;
            margin-right: 6px;
            line-height: 23.4px;
            span{
                line-height: 18.2px;
            }
        }
    }
}
</style>
