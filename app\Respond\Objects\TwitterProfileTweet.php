<?php

namespace App\Respond\Objects;

use App\Respond\BaseObject;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class TwitterPro<PERSON>leTweet extends BaseObject
{
    // this generates a reply to a conversation (tweet)
    public function performReply(array $data){
        // validate: text, attachments, etc. using validator
        try {
            \Validator::make($data, [
                'text' => 'string|max:280|required_without:attachments',
                'attachments' => 'array|nullable',
                'attachments.*' => 'file|mimes:jpg,jpeg,png,gif,mp4|max:5120', // 5MB max
            ])->validate();
        } catch (ValidationException $e) {
            throw new \InvalidArgumentException(Arr::first($e->errors()));
        }

        // todo: implement reply using Twitter API
    }
}
