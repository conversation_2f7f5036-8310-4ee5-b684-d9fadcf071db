@extends('layout.default')

@php
    $title = 'Snapchat Username Ideas Generator for Unique Names';
    $description = 'Discover unique Snapchat names with our Snapchat username ideas generator. Make your Snapchat profile stand out.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/generate-username/snapchat';

    $form = new \App\Generate\DynamicForm\Form('username_generator');
    $fields = $form->getFields();

    $fields = array_filter($fields, function ($field) {
        return $field['id'] !== 'network';
    });

@endphp
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Usernames',
                    type: 'username_generator',
                    splitDataBy:',',
                    inputData:{
                        network: 'snapchat'
                    },
                });
            });
        });
    </script>
@endpush
@section('content')
    <main class="main-content">
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12 text-center mb-5">
                        <h2 class="d-md-block d-none display-4 mb-3 pb-0">
                            Snapchat Username Ideas Generator
                        </h2>
                        <h4 class="d-md-none display-4 mb-3 pb-0">
                            Snapchat Username Ideas Generator
                        </h4>
                        <p class="mb-6">
                            Unique Snapchat username ideas in seconds - perfect for girls, boys, and brands!
                        </p>
                    </div>
                    <div class="col-md-8 mx-auto w-100 mh-350">
                        <div id="generate-tool-output">
                            <div class="text-center">
                                <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>


         <section>    
            <div class="container-fluid d-md-block d-none p-0 pt-7">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username.webp" alt="Generate username">
            </div>
            <div class="container-fluid d-md-none d-block p-0 pt-6">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username-mobile.webp" alt="Generate username">
            </div>
        </section>
        
         <section class="section rounded-xl p-xxl-5 px-20">
            <div class="container d-flex flex-column align-items-center text-center bg-primary text-white rounded-xl rounded-4 p-6 p-md-5 p-lg-8">
                <h2 class="display-2 fw-bold mb-6">Got your perfect username,<br>why stop there?</h2>
                <p class="lead-2 mb-6">Run your whole Social Media with Automations.</p>
                <a href="#" class="lead-2 btn bg-white btn-light text-primary d-flex align-items-center gap-2 px-4 py-2 ">
                    Automate your YouTube →
                </a>
            </div>
        </section>

         <section class="section">
            <div class="container px-20">
                <div class="row align-items-stretch">
                    <div class="col-12 px-20">
                        <h4 class="d-md-block d-none display-3 mb-2" >Create Your Perfect Snap Handle in Seconds</h4>
                        <h2 class="d-md-none display-6 mb-4 pb-1">Create Your Perfect Snap Handle in Seconds</h2>
                        <p class="pt-1 mb-6">
                            No more frustration or endless searches, enter a few details and get personalized suggestions instantly. It's simple, fast, and fun! Here's what you'll do:
                        </p>
                    </div>
                    <div class="col-md-4 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">1. Drop Your Keywords</h6>
                                <p class="mb-5">Think about what makes you you. Love music? Into gaming? Running a business? Add a few words separated by commas like "music, creative, bold" to get unique Snapchat username ideas – try your name for a personal touch (e.g., Snapchat username ideas with your name)</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">2. Add a Short Description</h6>
                                <p class="mb-0">Tell us what you're aiming for. Do you want a username for your personal account, a throwaway account, or something professional for your brand?</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">3. Pick Your Tone & Category</h6>
                                <p class="mb-0">Choose a vibe—formal, casual, or fun—and a category like business, gaming, or personal. Looking for Snapchat username ideas for boys or girls? Try casual or fun tones!</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">4. Customize Your Username</h6>
                                <p class="mb-0">Choose how long you want your username to be (8 characters, 10, or more) and whether you want numbers or special characters to make it stand out.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">5. Hit Generate & Repeat</h6>
                                <p class="mb-0">In seconds, you'll receive 12 unique username ideas you can tweak, copy, and use on Snapchat. If you don't like the options, generate again.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-5">
                        <div class="card bg-light rounded-xl h-100 d-flex flex-column">
                            <div class="pt-md-6 pb-md-6 pt-5 pb-6 px-5 d-flex flex-column align-items-center justify-content-between">
                                <h6 class="display-5">Why don't you try for yourself?</h6>
                                <a href="#top" class="btn btn-primary mt-6">Generated Usernames</a>
                            </div>
                        </div>
                    </div>                                           
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="text-left">
                    <h4 class="display-4 text-left mb-4">
                        What’s the Best Snapchat Username Generator?
                    </h4>
                    <p class="pt-1 mb-0">
                        SocialBu's Snapchat Username Generator creates fresh, memorable usernames fast for influencers, gamers, and businesses.
                    </p>
                </div>
                <div class="mt-md-8 mt-4 pt-md-0 pt-1">
                    <div class="row">
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-primary-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Enter Keywords</h6>
                                    <p class="mb-0">Add words, describe your vibe.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-success-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Choose Style</h6>
                                    <p class="mb-0">Select tone and category easily.</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-danger-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Customize Options</h6>
                                    <p class="mb-0">Set length and characters quickly.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-warning-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Pick Favorite</h6>
                                    <p class="mb-0">Choose and use your handle.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        @include('common.internal.cta-image')
        
        <section>
            <div class="container">
                <div class="row gap-y justify-content-center">
                    <div class="col-md-9">
                        <h6 class="d-md-block d-none display-4 mb-3 mx-3">FAQs</h6>
                        <h2 class="d-md-none display-6 mb-4">FAQs</h2>
                        <div class="accordion accordion-arrow-right border border-light rounded-2xl px-5 py-2" id="frequent-questions">
                            @foreach([
                            ['id' => '1', 'question' => 'Is the Snapchat username generator free to use?', 'answer' => 'Yes, our Snapchat Username Generator is completely free. Generate unlimited unique usernames instantly, helping you find the perfect handle without any cost.'],
                            ['id' => '2', 'question' => 'Can I get Snapchat username ideas for girls or boys?', 'answer' => 'Yes! Whether you’re after Snapchat username for girls with a chic vibe or Snapchat username for boys with a bold edge, pick the “Personal” category and tweak the tone to match your style.'],
                            ['id' => '3', 'question' => 'How do I create Snapchat username ideas for girls with an aesthetic twist?', 'answer' => 'Enter keywords like “dreamy, floral, vibe,” choose a fun tone, and enable special characters for Snapchat username ideas girl aesthetic that pop with style.'],
                            ['id' => '4', 'question' => 'How to make a username with my name?', 'answer' => 'Simply enter your name as a keyword, choose your tone and category, and our tool will generate Snapchat username ideas with your name – perfect for Snapchat username ideas girl with your name or boys wanting a custom vibe.'],
                            ['id' => '5', 'question' => 'What if I want to change my Snapchat username?', 'answer' => 'While our tool generates fresh Snapchat username ideas, you can use them to inspire a Snapchat username change. Check Snapchat’s settings to update your handle!'],
                            ['id' => '6', 'question' => 'Can I create a username for my small business account?', 'answer' => 'Absolutely. Use the “Business” category and formal tone to create professional Snapchat usernames that align with your brand identity.'],
                            ['id' => '7', 'question' => 'What to do if my username is already taken?', 'answer' => 'Our tool generates fresh, unique suggestions. If needed, you can tweak keywords or customize them with numbers or special characters to indicate availability.'],
                            ['id' => '8', 'question' => 'Will the generator suggest famous Snapchat username ideas for my brand?', 'answer' => 'Yes, if you select the business category and formal tone, you’ll get famous Snapchat username ideas that elevate your brand’s presence with professional, memorable usernames.'],
                            ['id' => '9', 'question' => 'Are the usernames generated truly unique Snapchat username ideas?', 'answer' => 'Our tool uses advanced algorithms to create unique Snapchat username ideas, but we recommend checking Snapchat’s availability to ensure your chosen username isn’t taken.'],
                            ['id' => '10', 'question' => 'Can I get username ideas for gaming or startup?', 'answer' => 'Yes, you can create usernames that match your interests or style by picking the “Gaming,” “Business,” or “Personal” categories and fun/casual tones.'],
                            ] as $index => $faq)
                            <div class="card shadow-none mb-2">
                                <div class="card-header px-0 pt-5 mb-1 {{ !$loop->first && !$loop->last ? 'border-bottom pb-5' : 'pb-2' }}" data-toggle="collapse" href="#collapse-{{ $faq['id'] }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    <div class="card-title d-flex align-items-center justify-content-between">
                                        <h6 class="mb-0 display-5 font-weight-600">{{ $faq['question'] }}</h6>
                                        <i class="ph {{ $index === 0 ? 'ph-caret-up' : 'ph-caret-down' }} ph-md"></i>
                                    </div>
                                </div>
                                <div id="collapse-{{ $faq['id'] }}" class="collapse {{ $index === 0 ? 'show' : '' }}" data-parent="#frequent-questions">
                                    <div class="card-body pb-3 px-0 pt-0 {{  !$loop->last ? 'border-bottom' : '' }}">
                                        <p class="mb-3">{!! $faq['answer'] !!}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
