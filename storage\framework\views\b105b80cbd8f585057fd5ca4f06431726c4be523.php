<!DOCTYPE html>
<html lang="en">
<head>
    <?php echo $__env->make('layout.includes.user.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body>
<?php echo $__env->make('layout.includes.common_body', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<div id="fb-root"></div>
<script>
    (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId=<?php echo e(config('services.facebook.client_id')); ?>&autoLogAppEvents=1';
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>
<div id="app">
    <?php echo $__env->make('layout.partials.user.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div class="container-fluid app-container">
        <div class="row">

            <div class="sidebar border-secondary pr-1 pb-0 pl-3 bg-light" id="sidebar_menu_wrapper">
                <?php echo $__env->make('layout.partials.user.sidebar_left', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <main role="main" class="col main px-0">
                <div class="container-fluid px-md-6 px-4 py-md-6 py-20 pt-6 bg-white rounded-lg main-content">
                    <div class="row">
                        <div class="col-12">

                            <?php if(user()->hasIncompletePayment('default') && !isActiveRoute('settings')): ?>
                                <?php ($latestPayment = user()->subscription('default')->latestPayment()); ?>
                                <?php if($latestPayment): ?>
                                    <div class="alert alert-warning">
                                        Your subscription is not active yet.

                                        Please <a class="alert-link border-bottom border-info" href="<?php echo e(route('cashier.payment', [$latestPayment->id, 'redirect' => route('settings', ['#billing'])])); ?>">complete the payment</a> to activate your subscription.
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if(user() && !(new \App\Http\Controllers\User\OnboardingController())->isOnboardingCompleted()): ?>
                                <!-- onboarding alert -->
                                <div class="alert alert-info d-flex align-items-center">
                                    <div class="ph-3xl mr-4 pr-4">
                                        <?php echo e(\Illuminate\Support\Arr::random(['🤗', '😍', '🥳', '🤩', '🤞', '😻'])); ?>

                                    </div>
                                    <div class="row w-100">
                                        <div class="col-12 col-lg-8">
                                            <h4 class="alert-heading">Welcome to <?php echo e(config('app.name')); ?></h4>
                                            <p>
                                                We're glad you're here. Before you can start, you need to complete your <?php echo e(config('app.name')); ?> profile.
                                            </p>
                                        </div>
                                        <div class="col-12 col-lg-4">
                                            <div class="d-lg-flex align-items-center justify-content-end h-100">
                                                <a href="<?php echo e(route('onboarding', ['from' => request()->fullUrl()])); ?>" class="alert-link">Get Started Now &rarr;</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php echo $__env->make('layout.partials.flash', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php echo $__env->make('layout.partials.errors', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php echo $__env->yieldContent('content'); ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>

    </div>

</div>
<?php echo $__env->make('common.internal.install-extension-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('layout.partials.user.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('layout.includes.user.footer_html', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->yieldPushContent('footer_html'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/user.blade.php ENDPATH**/ ?>