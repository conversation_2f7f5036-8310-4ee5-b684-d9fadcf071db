<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key' => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => 'us-east-1',
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],

    'facebook' => [
        'client_id' => env('FACEBOOK_APP_ID'),
        'client_secret' => env('FACEBOOK_APP_SECRET'),
        'redirect' => env('APP_URL') . '/auth/facebook/callback',
        'default_graph_version' => 'v22.0',
        'verification_token' => env('FACEBOOK_WEBHOOK_VERIFYTOKEN', 'tch tch tch'),
        'required_scopes' => [
            /*
             * // this perms are deprecated
            'manage_pages', // for getting access to pages
            'read_page_mailboxes', // accessing page's inbox
            'publish_pages', // posting on behalf of page
            */
            'business_management',

            'pages_manage_ads', // ads, we already approved for this so why not

            'pages_manage_metadata', // webhook and related page data

            'pages_manage_posts',

            'pages_messaging',

            'pages_read_engagement', // read data related to posts/engagement
            'pages_manage_engagement', // do engagement

            'pages_read_user_content', // read user-created posts and messages

            'read_insights', // for page insights

            // instagram business graph api
            'instagram_basic', // for seeing username/id etc
            'instagram_content_publish', // for publishing posts
            'instagram_manage_comments', // for comments
            'instagram_manage_insights', // for insights
        ],
        'page_perms' => [ // required page permissions (editor and admin)
            //'MANAGE', // for admin
            'CREATE_CONTENT', // for posts, comments/messages, and insights
            'MODERATE', // for comments/messages
            'ANALYZE', // for insights
        ]
    ],

    'threads' => [
        'client_id' => env('THREADS_APP_ID'),
        'client_secret' => env('THREADS_APP_SECRET'),
        'redirect' => env('APP_URL') . '/auth/threads/callback',
        'required_scopes' => [
            'threads_basic',
            'threads_content_publish',
            'threads_manage_replies',
            'threads_read_replies',
            'threads_manage_insights',
        ],
    ],

    'linkedin' => [
        'client_id' => env('LINKEDIN_CLIENT_ID'),
        'client_secret' => env('LINKEDIN_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . '/auth/linkedin/callback',
        'required_scopes' => [
            'r_basicprofile',
            'w_member_social', //old Marketing Developer Platform (MDP) api scope
            'r_organization_social', //old MDP api scope
            'w_organization_social', //old MDP api scope
            'rw_organization_admin', // managing organization
            'w_member_social_feed', // for posting on behalf of user
            'r_organization_social_feed', // read data
            'w_organization_social_feed', // to post on behalf of organization/brand
        ],
    ],

    'twitter' => [
        'client_id' => env('TWITTER_CLIENT_ID'),
        'client_secret' => env('TWITTER_CLIENT_SECRET'),
        'consumer_key' => env('TWITTER_CONSUMER_KEY'),
        'consumer_secret' => env('TWITTER_CONSUMER_SECRET'),
        'redirect' => env('APP_URL') . '/auth/twitter/callback',
        'app_hosts' => [ // needed to identify 'tweets' coming from our own app
            env('SESSION_DOMAIN'),
            'socialtool.glaxosoft.com',
        ],
        'required_scopes' => [
            'users.read',
            'tweet.read',
            'tweet.write',
            'media.write',
            'like.read',
            'like.write', 
            'offline.access', // for long-term access
            'tweet.moderate.write', // for hiding/unhiding replies
            'follows.read',
            'follows.write',
            'mute.write',
            'block.read',
            'mute.read',
            'dm.read',
            'dm.write',
            'users.email'
        ]
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . '/auth/google/callback',
        'required_scopes' => [
            'gmb' => [
                'https://www.googleapis.com/auth/business.manage', // to manage gmb
            ],
            'youtube' => [
                'https://www.googleapis.com/auth/youtube', // to manage YouTube
            ],
        ],
    ],
    'cloudflare' => [
        'api_token' => env('CLOUDFLARE_API_TOKEN'), 
        'account_id' => env('CLOUDFLARE_ACCOUNT_ID')
    ],

    'instagram' => [
        'authenticate_server' => env('INSTAGRAM_AUTHENTICATE_SERVER', 'http://127.0.0.1:3601/'),
        'server' => env('INSTAGRAM_SERVER', 'http://127.0.0.1:3602/'),
        'client_id' => env('INSTAGRAM_CLIENT_ID'),
        'client_secret' => env('INSTAGRAM_CLIENT_SECRET'),
        'default_graph_version' => 'v22.0',
        'redirect' => env('APP_URL') . '/auth/instagram/callback',
        'required_scopes' => [
            'instagram_business_basic',
            'instagram_business_content_publish',
            'instagram_business_manage_messages',
            'instagram_business_manage_comments'
        ]
    ],

    'bitly' => [
        'client_id' => env('BITLY_CLIENT_ID'),
        'client_secret' => env('BITLY_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . '/app/link_shorteners/bitly/callback',
    ],

    'linkmngr' => [
        'client_id' => env('LINKMNGR_CLIENT_ID'),
        'client_secret' => env('LINKMNGR_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . '/app/link_shorteners/linkmngr/callback',
    ],

    'mastodon' => [
        'domain'        => '', // domain of the mastodon instance with https:// at start; is set at runtime
        'client_id'     => '', // is set at runtime
        'client_secret' => '', // is set at runtime
        'redirect' => env('APP_URL') . '/auth/mastodon/callback',
        'required_scopes'         => ['read', 'write'],
    ],

    'tiktok' => [
        'client_id' => env('TIKTOK_CLIENT_ID'),
        'client_key' => env('TIKTOK_CLIENT_KEY'),
        'client_secret' => env('TIKTOK_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . '/auth/tiktok/callback',
        'required_scopes' => [
            'user.info.basic',
            'video.list',
            'video.upload',
            'video.publish', // for direct posting
        ],
    ],
    'pinterest' => [
        'client_id' => env('PINTEREST_CLIENT_ID'),
        'client_secret' => env('PINTEREST_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . 'auth/pinterest/callback',
        'required_scopes' => [
            'boards:read',
            'boards:read_secret',
            'boards:write',
            'boards:write_secret',
            'pins:read',
            'pins:read_secret',
            'pins:write',
            'pins:write_secret',
            'user_accounts:read'
        ]
    ],
    'reddit' => [
        'client_id' => env('REDDIT_CLIENT_ID'),
        'client_secret' => env('REDDIT_CLIENT_SECRET'),
        'redirect' => env('REDDIT_REDIRECT_URI', env('APP_URL') . '/auth/reddit/callback'),
        'required_scopes' => [
            'identity', // for getting user info
            'read', // for reading posts
            'submit', // for submitting posts
            'mysubreddits', // for getting list of subreddits
            'privatemessages', // for getting messages
            'modposts', // for moderating posts
            'vote', // for voting on posts/comments
            'flair', // needed for selecting flair
        ],
        // user agent should be: <platform>:<app ID>:<version string> (by /u/<reddit username>)
        // example: android:com.example.myredditapp:v1.2.3 (by /u/kemitche)
        'platform' => 'web',
        'app_id' => 'socialbu.com',
        'version' => 'v1.0 (by /u/usamaejazch)',
    ],

    'openai' => [
        'secret' => env('OPENAI_SECRET'),
    ],

    'huggingface' => [
        'secret' => env('HUGGINGFACE_SECRET'),
    ],

    'bing' => [
        'key' => env('BING_KEY'),
    ],
    'dream_studio' => [
        'key' => env('DREAMSTUDIO_API_KEY')
    ],

    'forefront' => [
        'key' => env('FOREFRONT_KEY'),
    ],

    'recaptcha' => [
        'key' => env('RECAPTCHA_KEY'),
        'secret' => env('RECAPTCHA_SECRET'),
    ],

    'rapidapi' => [
        'key' => env('RAPIDAPI_KEY'),
    ],
];
