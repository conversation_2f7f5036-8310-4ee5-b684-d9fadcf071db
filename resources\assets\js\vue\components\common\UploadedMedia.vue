<template>
    <div>
        <div class="drop_container border border-primary p-2 bg-socialbu-light drag_inactive" ref="drop_container"
             @dragover="onDragDrop('start', $event)"
             @dragenter="onDragDrop('start', $event)"
             @dragleave="onDragDrop('end', $event)"
             @dragend="onDragDrop('end', $event)"
             @drop="onDragDrop('drop', $event)"
             @click="openFilePicker"
             v-if="!name">

            <input type="file" ref="file_input" class="d-none" @change="uploadFile($event.target.files[0])" />

            <div class="text-center">
                <i class="ph ph-upload-simple ph-2xl"></i>
            </div>

            <div class="text-center" v-if="!loading">
                Drag and drop file here<br/>
                or <span class="text-primary">click to upload</span>
            </div>
            <div v-html="spinnerHtml" v-else></div>

        </div>
        <div
             v-else>
            <!-- we have the file details, so a file should be rendered -->
             <div class="position-relative main-element" v-if="!fileUploadingError">

                <template v-if="media && media.mimeType">
                    <img class="attachment lozad w-100 h-100 border border-secondary rounded" :src="media.url" alt="preview" v-if="media.mimeType.includes('image')">
                    <video class="attachment lozad w-100 h-100 border border-secondary rounded" preload="metadata" :src="media.url" v-else-if="media.mimeType.includes('video')"></video>
                    <div class="attachment d-flex flex-column align-items-center justify-content-center lozad w-100 h-100 border border-secondary rounded" v-else-if="['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'].indexOf(media.mimeType) > -1">
        
                        <!-- PDF Icon -->
                        <i v-if="media.mimeType.includes('pdf')" class="ph ph-file-pdf text-danger mr-2 doc-icon"></i>

                        <!--Word Icon-->
                        <i v-else-if="media.mimeType.includes('msword') || media.mimeType.includes('vnd.openxmlformats-officedocument.wordprocessingml.document')" 
                         class="ph ph-microsoft-word-logo text-primary mr-2 doc-icon"></i>

                        <!-- PowerPoint Icon-->
                        <i v-else-if="media.mimeType.includes('vnd.ms-powerpoint') || media.mimeType.includes('vnd.openxmlformats-officedocument.presentationml.presentation')" 
                         class="ph ph-file-ppt text-warning mr-2 doc-icon"></i>
                        <span class="text-center font-weight-bold small mt-1">{{ truncateFileName(media.name, 10) }}</span>

                    </div>
                </template>

                <div class="overlay-background position-absolute w-100 h-100 rounded" v-if="loading || deleting || uploadProgress !== null" ></div>
     
                <div class="progress position-absolute ml-2" v-if="uploadProgress !== null">
                    <div 
                        class="progress-bar progress-bar-striped progress-bar-animated" 
                        role="progressbar"
                        :aria-valuenow="uploadProgress" 
                        aria-valuemin="0" 
                        aria-valuemax="100"
                        :style="{ width: uploadProgress + '%' }">
                    </div>
                </div>

                <div class="loading position-absolute" v-if="(loading || deleting) && (uploadProgress === null )">
                    <i class="spinner-border spinner-border-sm text-primary"></i>
                </div>

                <div class="overlay position-absolute w-100 h-100 rounded d-none"></div>

                <div class="icons d-none">
                    <i class="remove-icon position-absolute cursor-pointer ph ph-x text-white p-1" title="Remove" v-tooltip @click="deleteFile"
                        v-if="!viewOnly">
                    </i>
                    <i class="edit-icon position-absolute cursor-pointer ph ph-pencil-simple-line p-1 text-white"></i>
                </div>

            </div>
            <div class="error-component position-relative border border-danger rounded-md p-0" v-tooltip :title="fileUploadingError" v-else>
                <i class="remove-icon position-absolute cursor-pointer ph ph-x text-white p-1" title="Remove" v-tooltip @click="deleteFile"
                    v-if="!viewOnly">
                </i>
                <div v-if="!deleting" class="reload-component position-absolute d-flex align-items-center cursor-pointer rounded bg-primary p-1" @click="uploadFile(fileToUpload)" title="Retry upload">
                    <i class="ph ph-arrow-clockwise text-white"></i>
                </div> 
                <div class="position-absolute small-2 text-danger error-message">
                    {{ fileUploadingError.length > 40 ? fileUploadingError.slice(0, 40) + '...' : fileUploadingError }}
                </div>
                <div class="loading position-absolute" v-if="deleting">
                    <i class="spinner-border spinner-border-sm text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { axios, axiosErrorHandler, spinnerHtml } from "../../../components";
import size from "filesize";
import { truncate } from "lodash";

export default {
    name: "UploadedMedia",
    props: {
        media: {
            // object of an existing already stored attachment (for rendering)
            type: Object,
            required: false
        },
        file: {
            // this is the file object to be uploaded if provided
            type: File,
            required: false
        },
        viewOnly: {
            type: Boolean,
            required: false,
            default: false
        },
        showPreview: {
            // not yet implemented; should show preview of image/video, etc
            type: Boolean,
            required: false,
            default: true
        }
    },
    data() {
        return {
            key: null, // s3/spaces key
            temporary: false, // is this a temporary file (not yet attached to a resource)

            name: null, // file name
            mimeType: null, // mime type
            size: null, // size in bytes
            extension: null, // file extension
            _metaData: {}, // additional meta data

            url: null, // file download/view url
            secureKey: null, // encrypted key that will be used to delete/edit or verify the file

            loading: false, // is the file being uploaded
            uploadProgress: null, // upload progress
            uploadAbortController: null, // the instance from AbortController()

            fileUploadingError: null, // did the upload fail, we show retry button if not null

            fileToUpload: null, // the file object to be uploaded
            deleting: false // is the file being deleted
        };
    },
    watch: {
        file(newFile) {
            if (newFile) {
                this.uploadFile(newFile);
            }
        },
        media(newMedia) {
            if (newMedia) {
                this.setMedia(newMedia);
            }
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
    },
    methods: {
        truncate,
        setMedia(media) {
            this.temporary = false;
            for (let k in media) {
                this[k] = media[k];
            }
        },
        openFilePicker() {
            if (this.viewOnly) {
                return;
            }
            this.$refs.file_input.click();
        },
        onDragDrop(type, e) {
            e.preventDefault();
            if (type === "drop") {
                this.uploadFile(e.dataTransfer.files[0]);
            } else if (this.$refs.drop_container) {
                if (type === "start") {
                    this.$refs.drop_container.classList.remove("drag_inactive");
                    this.$refs.drop_container.classList.add("drag_active");
                } else {
                    this.$refs.drop_container.classList.remove("drag_active");
                    this.$refs.drop_container.classList.add("drag_inactive");
                }
            }
        },
        async uploadFile(file) {
            // file should always be a File object
            if (!file) {
                return;
            }
            this.fileToUpload = file;
            this.temporary = true;
            this.name = file.name;
            this.mimeType = file.type;
            this.size = file.size;
            this.extension = file.name.split(".").pop();
            this._metaData = file._metaData || {};
            this.loading = true;
            this.uploadProgress = 0;
            this.fileUploadingError = null;
            const formData = new FormData();
            formData.append("name", file.name);
            formData.append("size", file.size);
            formData.append("mime_type", file.type);
            try {
                let { data } = await axios.post("/api/v1/files/temporary/create", formData);
                this.key = data.key;
                this.url = data.url;
                this.secureKey = data.secure_key;

                const controller = new AbortController();
                this.uploadAbortController = controller;

                await axios.put(data.signed_url, file, {
                    headers: { "Content-Type": file.type, "x-amz-acl": "private" },
                    onUploadProgress: progressEvent => {
                        this.uploadProgress = Math.round(progressEvent.loaded / progressEvent.total * 100);
                        if(this.uploadProgress > 0 ){
                            this.$emit('upload-started');
                        }
                    },
                    signal: controller.signal
                });

                // file was uploaded
                this.uploadProgress = 100;

                const media = {
                    uploadedMedia: true // so we know its coming from this component
                };
                ["name", "size", "mimeType", "extension", "key", "url", "secureKey", "temporary", "_metaData"].forEach(k => (media[k] = this[k]));
                this.$emit("uploaded", media);
            } catch (e) {
                if (axios.isCancel(e)) {
                    console.log("Upload request canceled", e.message);
                } else {
                    this.fileUploadingError = axiosErrorHandler(e, true);
                }
            }
            this.loading = false;
            this.uploadProgress = null;
        },
        cancelUpload() {
            if (this.uploadAbortController) {
                this.uploadAbortController.abort();
            }
        },
        async deleteFile() {
            if (this.deleting) {
                return;
            }
            this.deleting = true;
            if (this.loading) {
                this.cancelUpload();
            }
            if (this.secureKey && this.temporary) {
                try {
                    await axios.post("/api/v1/files/temporary/delete", {
                        keys: [this.secureKey]
                    });
                } catch (e) {
                    console.error(e);
                }
            }
            this.deleting = false;
            this.$emit("removed");
            Object.assign(this.$data, this.$options.data());
        },
        truncateFileName(filename, maxLength = 10) {
            let parts = filename.split(".");
            if (parts.length < 2) return filename; 

            let ext = parts.pop();
            let name = parts.join(".");

            let truncatedName = truncate(name, {
                length: maxLength,
                omission: "..."
            });

            return truncatedName + "." + ext;
        },
    },
    mounted() {
        if (this.media) {
            this.setMedia(this.media);
        } else if (this.file) {
            this.uploadFile(this.file);
        }
    }
};
</script>

<style lang="scss" scoped>
.drag_inactive {
    border-style: dashed !important;
}
.drag_active {
    border-style: solid !important;
}

.main-element{
    height: 120px;
    width: 120px;
    max-height: 120px;
    &:hover{
        .overlay{
            display: block !important;
            background: #131519;
            opacity: 0.4;
            transition: opacity 0.8s;
            top: 0;

        }
        .icons{
            display: block !important;
        }
    }
}
.error-component{
    width: 120px;
    height: 120px;
    background: #FEE3D4;
}
.overlay-background{
    background: #131519;
    opacity: 0.4;
    transition: opacity 0.8s;
    top: 0;

}
.attachment{
    object-fit: cover;

}
.remove-icon{
    top: 4px;
    left: 4px;
    border-radius: 40px;
    background: rgba(16, 25, 41, 0.40);

}
.edit-icon{
    top: 4px;
    right: 4px;
    border-radius: 40px;
    background: rgba(16, 25, 41, 0.40);
}
.progress{
    height: 4px !important;
    top: 50%;
    width: 86%;
}
.loading{
    top: 40%;
    left: 40%;
}
.reload-component{
    border-radius: 30px !important;
    top: 4px;
    right: 4px;
}
.error-message{
    top: 44%;
    bottom: 8px;
    left: 8px;
    right: 8px;
}
.spinner-border{
    width: 24px !important;
    height: 24px !important;
}
.doc-icon {
    font-size: 2rem;
    margin-bottom: 4px;
}
</style>
