<?php

namespace App\Notifications;

use App\PublishQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PublishQueueFailure extends Notification implements ShouldQueue
{
    use Queueable;

    private $pQueue;

    private $error;

    private $accountInfo;

    /**
     * Create a new notification instance.
     *
     * @param PublishQueue $queue
     * @param string $error
     * @param string|null $accountInfo
     */
    public function __construct(PublishQueue $queue, string $error, string $accountInfo = null)
    {
        $this->pQueue = $queue;
        $this->error = $error;
        $this->accountInfo = $accountInfo;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Couldn\'t publish from your queue "' . $this->pQueue->name . '"')
            ->error()
            ->line('One of your posts could not be published' . ($this->accountInfo ? ' to ' .e($this->accountInfo) : '') . ' from your queue ' . e($this->pQueue->name) .' because of the following reason.' )
            ->line(e($this->error))
            ->action('See Queue', url('app/publish/queues/' . $this->pQueue->id))
            ->line('Make sure your posts are valid for the accounts you are posting to. If you think this should not happen, please contact us.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'queue_id' => $this->pQueue->id,
            'queue_name' => $this->pQueue->name,
            'reason' => $this->error,
            'account_info' => $this->accountInfo,
            '_level' => 'danger',
        ];
    }
}
