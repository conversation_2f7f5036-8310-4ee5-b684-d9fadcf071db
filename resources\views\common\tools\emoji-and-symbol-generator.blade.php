@extends('layout.default')
@php

    $title = 'Free Emoji and Symbol Generator';
    $description = 'Relevant emojis and symbols to enhance post visual appeal and emotion.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/emoji-and-symbol-generator';
    $form = new \App\Generate\DynamicForm\Form('emoji_and_symbol_generator');
    $fields = $form->getFields();
@endphp
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />
    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush

@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Output',
                    type: 'emoji_and_symbol_generator',
                    splitDataBy:'',
                });
            });
        });
        
    </script>
@endpush
@section('content')
    <header class="header">
        <div class="container">
            <div class="row align-items-center h-100">
                <div class="col-md-12 text-center">
                    <h4 class="d-md-block d-none display-4 mb-3 pb-0">
                        Emoji and Symbol Generator 
                    </h4>
                    <h2 class="d-md-none display-6">
                        Emoji and Symbol Generator 
                    </h2>
                    <p class="mb-6 pb-2">Relevant emojis and symbols to enhance post visual appeal and emotion.</p>
                </div>
                <div class="col-md-8 mx-auto w-100 mh-350">
                    <div id="generate-tool-output">
                        <div class="text-center">
                            <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <div class="container-fluid bg-dark-1">
            <section class="section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2 class="text-white display-3 mb-4 pb-2">
                                Enhance Posts with Emojis & Symbols in<br class="d-md-block d-none"> 3 Simple Steps
                            </h2>
                        </div>
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-100 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">1</h2>
                                    <h5 class="text-white mb-3">Enter Your Post</h5>
                                    <p class="text-white opacity-75 mb-0">
                                        Paste the text you want to enhance with emojis and symbols.
                                    </p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-100 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">2</h2>
                                    <h5 class="text-white mb-3">Select Your Tone</h5>
                                    <p class="text-white opacity-75 mb-0">
                                        Choose the mood or vibe for your post — from fun and quirky to sleek and professional.
                                    </p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-100 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">3</h2>
                                    <h5 class="text-white mb-3">Generate & Copy</h5>
                                    <p class="text-white opacity-75 mb-0">
                                        Instantly get your enhanced caption — loaded with the perfect emojis and symbols. Copy and use anywhere.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>
@endsection