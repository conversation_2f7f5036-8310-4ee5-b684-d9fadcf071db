<?php

namespace App\Http\Controllers\User;

use App\Imports\BulkImport;
use App\Jobs\ProcessBulkImport;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Validation\ValidationException;

class BulkUploadController extends Controller
{
    public function index(){
        return view('user.publish.bulk_upload');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function import(Request $request){

        $this->validate($request, [
            'file' => 'required|file|mimes:csv,txt|max:' . config('app.max_file_size'),
        ]);

        if($this->getStatus() === 'started'){
            abort(400, 'An import is in-progress and not yet completed. Please wait a few minutes...');
        }

        // first we store the uploaded file
        $path = $request->file('file')->store('attachments');

        if(!$path){
            abort(500, 'Unable to store the file. Please try again.');
        }

        // mark as started
        user()->setOption('bulk_import', [
            'status' => 'started',
        ]);


        // will be completed by queue worker because big files can result in request timeout if we process here
        if(app()->environment('production')) {
            dispatch( (new ProcessBulkImport(user(), $path))->onQueue('default_long')->onConnection('redis_long') );
        } else {
            dispatch( (new ProcessBulkImport(user(), $path)) );
        }


        // if all good
        return response()->json([
            'success' => true,
            'status' => 'started',
        ]);
    }

    /**
     * Prepare for another import
     * @param Request $request
     * @return JsonResponse
     */
    public function reset(Request $request){

        $status = $this->getStatus();

        // if there is a current or pending import
        if($status){

            if($status === 'started') {
                // is in progress
                abort(400, 'An import is in-progress and not yet completed. Please wait a few minutes...');
            }

            // either error or done, remove status
            user()->removeOption('bulk_import');
        }

        // reset ok, now user can do another import
        return response()->json([
            'success' => true,
        ]);
    }

    /**
     * Get import progress
     * @param Request $request
     * @return array
     */
    public function getProgress(Request $request){
        return $this->getStatus(true);
    }

    /**
     * Return current import status if any or null if none
     * @param bool $full
     * @return string|null|array
     */
    private function getStatus(bool $full = false){
        $data = user()->getOption('bulk_import');
        if(!$data) return null;

        if(isset($data['job_processing'])){

            // handle race conditions
            $lastTimestamp = $data['job_processing_started_at'] ?? null;

            if(!$lastTimestamp) {
                // set current timestamp
                $lastTimestamp = time();
                $data['job_processing_started_at'] = $lastTimestamp;
                user()->setOption('bulk_import', $data);
            }

            // check if job is running for more than 30 minutes
            if(time() - $lastTimestamp > 30 * 60){
                $data = [
                    'status' => 'error',
                    'error' => 'Import was running for more than 30 minutes. Please try again.',
                ];
                user()->setOption('bulk_import', $data);
            }

        }

        if(!$full) return array_get($data, 'status', 'started');
        return $data;
    }
}
