<?php

namespace App;

use App\Helpers\EmailListHelper;
use App\Helpers\InstagramVideo;
use App\Helpers\LinkedInClient;
use App\Helpers\LinkShortenerHelper;
use App\Helpers\TwitterOauthCustom;
use App\Jobs\OptimizeVideo;
use App\Jobs\PublishPost;
use App\Jobs\SendHTTPRequest;
use App\Notifications\PostApprovalRejected;
use App\Notifications\PostFailure;
use App\Traits\HasOptions;
use Carbon\Carbon;
use Facebook\Facebook;
use FFMpeg\FFMpeg;
use FFMpeg\FFProbe;
use FFMpeg\Filters\Audio\SimpleFilter;
use FFMpeg\Format\Video\X264;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\UriResolver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\File;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use InstagramAPI\Media\InstagramMedia;
use App\Helpers\InstagramPhoto;
use LinkedIn\Http\Method;
use Mimey\MimeTypes;
use Revolution\Mastodon\MastodonClient;
use Twitter\Text\Extractor;

/**
 * App\Post
 *
 * @property int $id
 * @property string|null $content
 * @property string $post_hash
 * @property string|null $external_id
 * @property int $account_id
 * @property int $user_id
 * @property string $type
 * @property string|null $source
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $publish_at
 * @property \Illuminate\Support\Carbon|null $published_at
 * @property array|null $result
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool $draft
 * @property bool $approved
 * @property \Illuminate\Support\Carbon|null $insights_fetched_at
 * @property-read \App\Account $account
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post awaitingApproval()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post draft()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post ofUser($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post pending()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post published()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereApproved($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereDraft($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereInsightsFetchedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post wherePostHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post wherePublishAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post wherePublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereResult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Post whereUserId($value)
 * @mixin \Eloquent
 * @property int|null $has_result
 * @method static \Illuminate\Database\Eloquent\Builder|Post whereHasResult($value)
 */
class Post extends Model
{

    use HasOptions;

    private $fetchedInsights = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'account_id', 'content', 'type', 'source', 'publish_at', 'post_hash', 'draft', 'approved', 'result', 'insights_fetched_at',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'publish_at',
        'published_at',
        'insights_fetched_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'result' => 'array',
        'draft' => 'boolean',
        'approved' => 'boolean',
    ];

    /** @var Team[]|Collection $joinedTeams */
    private static $joinedTeams = null;


    /**
     * @param Post $post
     * @param null $insightsAll
     * @return Collection
     */
    public static function transform(Post $post, $insightsAll = null)
    {

        $error = $post->getError();

        return collect([
            'id' => (int)$post->id,
            'content' => (string) $post->content,
            'post_hash' => $post->post_hash,
            'external_id' => $post->external_id,
            'account_id' => (int)$post->account_id,
            'account_type' => $post->account->getType(),
            'type' => $post->type,
            'attachments' => $post->getAttachments(),
            'post_options' => (object) self::processPostOptions($post->account, $post->options, false), // always get freshly processed data so spaces urls can work and not be expired
            'shortened_links' => (array) $post->getOption('shortened_links', []),
            'error' => $error,
            'source' => $post->source,
            'user_id' => (int)$post->user_id,
            'user_name' => $post->user ? $post->user->name : 'A user',
            'publish_at' => $post->publish_at->toDateTimeString(),
            'created_at' => $post->created_at->toDateTimeString(),
            'updated_at' => $post->updated_at->toDateTimeString(),
            'published_at' => $post->published_at ? $post->published_at->toDateTimeString() : null,
            'published' => $post->published_at ? true : false,
            'permalink' => $post->getPermalink(),
            'draft' => $post->draft,
            'approved' => $post->approved,
            'reject_reason' => $post->draft && $post->getOption('reject_reason') ? $post->getOption('reject_reason') : null,
            'insights' => $post->published_at ? $post->getMetrics($insightsAll) : null,
            'can_edit' => $post ->canBeEditedBy(user()),
            'can_approve' => $post->canBeApprovedBy(user()),
        ]);
    }

    /**
     * @param Account $account
     * @param array $options
     * @param bool $noValidate
     * @return array
     */
    public static function processPostOptions(Account $account, array $options = [], bool $noValidate = false){

        // allowed options if any
        $allowed_options = [];

        if($account->type === 'instagram.direct'){
            $allowed_options = [
                'comment' => $account->getPostLength(), // first comment
            ];
        } else if($account->type === 'instagram.api'){
            $allowed_options = [
                'comment' => $account->getCommentLength(), // first comment
                'location' => 500, //json
                'post_as_reel' => 'boolean', // if user wants to post video as reel
                'post_as_story' => 'boolean', 
                'thumbnail' => 1000, //json
                'media_alt_text' => 1000,
                'share_reel_to_feed' => 'boolean', // to also post the reel to main feed
                'tags' => 200,
                'videoThumbnailCurrTime' => 200,
                'collaborators' => 200
            ];
        } else if($account->type === 'facebook.page'){
            $allowed_options = [
                'video_title' => 140, // video title, only if video selected
                'link' => 500, // custom post link to attach, in case of text content having multiple links
                'media_alt_text' => 200,
                'comment' =>$account->getCommentLength(), //first comment
                'post_as_story' => 'boolean',
                'post_as_reel' => 'boolean',
                'thumbnail' => 1000, //json
                'location' => 500 //json
            ];
        } else if(Str::contains($account->type, 'linkedin.')){
            $allowed_options = [
                'link' => 500, // custom post link to attach, in case of text content having multiple links
                'trim_link_from_content' => 'boolean',
                'customize_link' => 'boolean',
                'thumbnail' => 1000, //json
                'link_title' => 400,
                'link_description' => 4086,
                'comment' => $account->getCommentLength(),
                'media_alt_text' => 200,
                'document_title' => 200
            ];
        } else if($account->type === 'twitter.profile'){
            $allowed_options = [
                'threaded_replies' => 240 * 30,
                'media_alt_text' => 200,
            ];
        } else if($account->type === 'mastodon.profile'){
            $allowed_options = [
                'mark_sensitive' => 'boolean',
                'spoiler' => 140,
                'media_alt_text' => 200,
                'threaded_replies' => 500 * 10,
            ];
        } else if ($account->type === 'pinterest.profile'){
            $allowed_options = [
                'thumbnail' => 1000, //json
                'pin_title' => 100,
                'board_name' => 200,
                'media_alt_text' => 200,
                'pin_link' => 500,
                'pin_note' => 500,
            ];
        }else if($account->type === 'google.location'){
            $allowed_options = [
                'topic_type' => 50,

                // topic: event,offer
                'event_title' => 255,
                'event_start' => 100,
                'event_end' => 100,

                // topic: offer
                'offer_coupon' => 255,
                'offer_link' => 500,
                'offer_terms' => 500,

                // cta
                'call_to_action' => 50,
                'call_to_action_url' => 500,

                // save photos in gmb gallery
                'save_media_to_gallery' => 'boolean',
            ];
        } else if($account->type === 'google.youtube'){
            $allowed_options = [
                'video_title' => 100,

                'video_tags' => 500,

                'category_id' => 10,

                'privacy_status' => 10,

                'post_as_short' => 'boolean',
                'thumbnail' => 1000, //json
                'made_for_kids' => 'boolean',
            ];
        } else if(in_array($account->type, ['reddit.profile', 'reddit.subreddit'])){
            $allowed_options = [
                'title' => 300,
                'is_spoiler' => 'boolean',
                'is_nsfw' => 'boolean',
                'flair_id' => 200,
                'comment' => $account->getCommentLength()
            ];
        } else if($account->type === 'tiktok.profile'){
            $allowed_options = [

                // deprecated
                'comment' => 'boolean',
                'duet' => 'boolean',
                'stitch' => 'boolean',

                // proper
                'allow_comment' => 'boolean',
                'allow_duet' => 'boolean',
                'allow_stitch' => 'boolean',
                'auto_add_music' => 'boolean',

                'thumbnail' => 1000, //json

                'privacy_status' => 100,
                'title' => 90,
                'disclose_content' => 'boolean',
                'branded_content' => 'boolean',
                'own_brand' => 'boolean'
            ];
        } else if($account->type === 'threads.profile'){
            $allowed_options = [
                'reply_control' => 30,
                'threaded_replies' => 500 * 10,
            ];
        } else if($account->type === 'bluesky.profile'){
            $allowed_options = [
                'media_alt_text' => 200,
                'threaded_replies' => 240 * 30,
            ];
        }

        // postback_url allowed for all
        $allowed_options['postback_url'] = 500;
        $allowed_options['post_source_name'] = 100;

        //backward compatibility: thumbnail used be link_thumbnail for LinkedIn
        if(Str::contains($account->type, 'linkedin.') && isset($options['link_thumbnail'])){
            $options['thumbnail'] = $options['link_thumbnail'];
        }
        
        //backward compatibility:: used to be image_alt_text
        if(isset($options['image_alt_text'])){
            $options['media_alt_text'] = $options['image_alt_text'];
        }

        // mark the keys which will contain native object or json of that object
        $objects_or_arrays = [
            'location',
            'threaded_replies',
            'link_thumbnail',
            'image_alt_text',
            'tags',
            'thumbnail',
            'media_alt_text'
        ];
        if($noValidate){
            return collect(array_keys($options))->filter(function ($key) use($allowed_options){
                return isset($allowed_options[$key]);
            })->mapWithKeys(function ($k) use($options, &$objects_or_arrays){
                // temp fix
                if( in_array($k, $objects_or_arrays) && !is_array($options[$k])){
                    $options[$k] = @json_decode($options[$k], true);
                }
                return [
                    $k => $options[$k]
                ];
            })->filter()->sort()->toArray();
        }

        $modifiedOptions = $options;
        // validation
        if(!empty($options)){
            foreach($options as $key => $opt){
                if(!isset($allowed_options[$key])) {
                    unset($modifiedOptions[$key]);
                } else {
                    $val = $opt;
                    if(!is_bool($val) && ($allowed_options[$key] === 'boolean' || is_int($allowed_options[$key]))){
                        // should be string
                        if(!is_string($val)){
                            if(!in_array($key, $objects_or_arrays)){
                                // unset this non-string when it should be string
                                unset($modifiedOptions[$key]);
                            }
                        } else {
                            if($allowed_options[$key] === 'boolean'){

                                // if it was supposed to be boolean but is not
                                if(strtolower($opt) === 'true'){
                                    $modifiedOptions[$key] = true;
                                }else{
                                    unset($modifiedOptions[$key]);
                                }

                            } else if(is_int($allowed_options[$key])) {
                                // all good for this string
                                // max-length
                                $modifiedOptions[$key] = str_limit($val, $allowed_options[$key] - 3);
                            }
                        }
                    }else if($allowed_options[$key] === 'boolean' && !$val){
                        unset($modifiedOptions[$key]); //unset false values
                    }
                }
            }

        }

        // important to accept non-string and non-bool props as strings (JSON) and also as native objects (array/object)
        foreach($objects_or_arrays as $key){
            if(isset($modifiedOptions[$key])){
                // if its in json form
                if(!is_array($modifiedOptions[$key] ))
                    $modifiedOptions[$key] = @json_decode($modifiedOptions[$key], true);

                // if its invalid
                if(empty($modifiedOptions[$key])){
                    unset($modifiedOptions[$key]);
                }
            }
        }

        // special case for threaded_replies
        if(isset($modifiedOptions['threaded_replies'])){
            foreach($modifiedOptions['threaded_replies'] as &$reply){
                if(isset($reply['media'])){
                    if(is_array($reply['media'])){
                        $attachments = $reply['media'];
                    }else {
                        $attachments = json_decode($reply['media'], true);
                    }
                    foreach($attachments as &$media){
                        // we generate url fresh
                        $media['url']= \Storage::cloud()->temporaryUrl($media['key'], now()->addDays(2));
                    }
                    $reply['media'] = $attachments;
                }
                
                // backward compatibility
                if(isset($reply['tweet'])){
                    $reply['text'] = $reply['tweet'];
                    unset($reply['tweet']);
                }

            };
            $modifiedOptions['threaded_replies'] = array_filter($modifiedOptions['threaded_replies']);
            // make sure threaded_replies is an array and not an object
            $modifiedOptions['threaded_replies'] = array_values($modifiedOptions['threaded_replies']);
        }

        // options that are file uploads
        $fileUploadOptions = [
            'thumbnail',
        ];

        foreach($fileUploadOptions as $key){
            if(isset($modifiedOptions[$key])){
                $path = null;
                if(isset($modifiedOptions[$key]['key'])){
                    $path = $modifiedOptions[$key]['key'];
                } else if(isset($modifiedOptions[$key]['path'])){
                    $path = $modifiedOptions[$key]['path'];
                }

                if($path && Str::startsWith($path, 'temporary/')){
                    // is temporary file, so copy and update key
                    try {
                        $newKey = save_temporary_upload($path);

                        // update the key
                        $modifiedOptions[$key]['key'] = $newKey;

                    } catch (\Exception $e) {
                        // if failed to save, remove the option
                        unset($modifiedOptions[$key]);
                    }
                }

                // add public url
                if(isset($modifiedOptions[$key]['key'])) {
                    $modifiedOptions[$key]['url'] = \Storage::cloud()->temporaryUrl($modifiedOptions[$key]['key'], now()->addDays(2));
                }

            }
        }

        // sort the keys
        ksort($modifiedOptions);

        return $modifiedOptions;
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if (!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Scope a query to fetch pending posts.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('external_id', NULL)
            ->where('approved', true)
            ->where('draft', false)
            ->where('published_at', NULL);
    }

    /**
     * Scope a query to fetch published posts.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('published_at', '<>', NULL);
    }

    /**
     * Scope a query to fetch draft posts.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDraft($query)
    {
        return $query->where('external_id', NULL)
            ->where('approved', true)
            ->where('draft', true);
    }

    /**
     * Scope a query to fetch awaiting approval posts.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAwaitingApproval($query)
    {
        return $query->where('external_id', NULL)
            ->where('approved', false)
            ->where('draft', false);
    }

    /**
     * Get the user that added this object.
     */
    public function user()
    {
        return $this->belongsTo('App\User');
    }

    /**
     * Get the account associated with this object.
     */
    public function account()
    {
        return $this->belongsTo('App\Account');
    }

    /**
     * @return array|null - array has message and timestamp
     */
    public function getError(){
        $error = null;
        if (isset($this->result['_success']) && !$this->result['_success']) {
            $error = [
                'message' => $this->result['_errorMsg'],
                'timestamp' => $this->result['_timestamp'],
            ];
        }
        return $error;
    }

    // cache for getTeam method
    private static $getTeamCache = [];

    /**
     * @param User|null $user
     * @return Team|null
     */
    public function getTeam(User $user = null){

        if(!$user){
            $user = $this->user;
        }

        $ret = null;

        $cacheKey = $user->id . '_' . $this->account_id;

        // if already in cache, return from cache
        if(isset(self::$getTeamCache[$cacheKey])){
            return self::$getTeamCache[$cacheKey];
        }

        // get team for the related account if needed
        /** @var Team[]|Collection $teamsHavingAccount */
        $teamsHavingAccount = $user->joinedTeams()->whereHas('accounts', function($q){
            return $q->where('id', $this->account_id);
        })->get();

        // set for all posts, may not be set in case of old posts
        $teamId = $this->getOption('team_id');

        if($teamsHavingAccount->count() > 0){ // we have team for this account
            if(
                ($teamId && !$teamsHavingAccount->where('id', $teamId)->first()) // if team id is set but the team not found in joined teams
                || $teamId === null // or if team id not set and user has a joined team with this account
            ) {
                // if not set, find a team
                $teamId = $teamsHavingAccount->first()->id;
            }
        }

        if($teamId > 0){
            // if the post user has a team through which he has access to post accounts
            $ret = $teamsHavingAccount->where('id', $teamId)->first();
        }

        // save in cache
        self::$getTeamCache[$cacheKey] = $ret;

        // make sure there are max. 100 items in cache
        if(count(self::$getTeamCache) > 100){
            self::$getTeamCache = array_slice(self::$getTeamCache, -100, 100, true);
        }

        return $ret;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function canBeEditedBy(User $user){

        // should not happen, will happen if account/user is deleted or something, still strange and rare
        if(!$this->account || !$user) {
            return false;
        }

        // published post cannot be edited lol
        if ($this->published_at) return false;

        if ($this->user_id === $user->id && $this->getError()) {
            // has error and is mine, i can edit
            return true;
        } else if ($this->user_id === $user->id && !$this->approved) {
            // my post is not yet approved, so i can still edit
            return true;
        } else if ($this->user_id === $user->id && $this->draft) {
            // my post which is draft
            return true;
        }

        // see if we have someone who should approve this post
        $team = $this->getTeam($user);
        if($team) {

            if($this->user_id === $user->id && !$team->requiresContentApproval()){
                // is mine and content approval is not required, so i can edit the post anytime
                return true;
            }

            return $team->hasPermission($user, 'posts.edit');
        }

        // if we are here, the post is probably ours
        return true;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function canBeApprovedBy(User $user){

        if (!$this->account || !$user) {
            // strange; shouldn't happen
            return false;
        } else if ($this->published_at){
            // how can you approve a published post lol
            return false;
        } else if ($this->draft){
            // cant approve a draft huh
            return false;
        } else if ($this->approved){
            // already approved
            return false;
        }

        // see if we have someone who should approve this post
        $team = $this->getTeam($user);
        if($team && $team->requiresContentApproval()){

            if (!$team->hasPermission($user, 'approvals.approve')) {
                // i am not one of the approvers
                return false;
            }

        }

        // default
        return true;
    }

    /**
     * @param bool $approved
     * @param string $reason
     * @throws \Exception
     */
    public function approvePost($approved = false, $reason = ''){
        if($this->approved || $this->draft || !user()){
            return;
        }

        // post was approved or rejected
        if (!$approved) {
            // so the user rejected the post
            $reason = trim($reason);
            if ($reason === '') {
                throw new \Exception('Reason is required');
            }
            $this->approved = true; // draft is approved
            $this->draft = true; // convert to draft
            $this->save();
            $this->setOption('reject_reason', $reason); // set reject reason

            // was rejected so notify
            $this->user->notify(new PostApprovalRejected($this, user()));

            try {
                get_insights_db()->table('misc_metrics')->insert([
                    'user_id' => $this->user_id,
                    'account_id' => $this->account_id,
                    'object' => 'post:' . $this->id,
                    'action' => 'post_rejected',
                ]);
            } catch (\Exception $e) { }

        } else {
            // approved
            $this->approved = true;

            if($this->getOption('reject_reason')){
                // if it was rejected, remove the msg
                $this->removeOption('reject_reason');
            }

            $this->save();

            try {
                get_insights_db()->table('misc_metrics')->insert([
                    'user_id' => $this->user_id,
                    'account_id' => $this->account_id,
                    'object' => 'post:' . $this->id,
                    'action' => 'post_approved',
                ]);
            } catch (\Exception $e) { }
        }
    }

    public function getLinksInContent(){
        $extractor = new Extractor();
        return $extractor->extractURLs($this->content);
    }

    public function shortenLinksIfNeeded(){
        $extractor = new Extractor();

        // where to replace links in options other than `content` of post
        $optionKeys = [
            'comment' => 'string',
            'link' => 'string',
            'offer_link' => 'string',
            'call_to_action_url' => 'string',
            'threaded_replies' => 'array', // special case
        ];

        $allLinks = $this->getLinksInContent(); // array

        foreach ($optionKeys as $key => $type){
            $val = $this->getOption($key);
            if($val){
                if($type === 'string') {
                    foreach ($extractor->extractURLs($val) as $URL) {
                        $allLinks[] = $URL;
                    }
                } else if($key === 'threaded_replies') {
                    // mainly for threaded_replies
                    foreach ($val as $tweet) {
                        //doing this for old tweets
                        $content = '';
                        if(is_array($tweet)){
                            if(isset($tweet['tweet'])){
                                $tweet['text'] = $tweet['tweet'];
                            }
                            $content = isset($tweet['text']) ? $tweet['text'] : '';
                        } else {
                            $content = $tweet;
                        }
                        foreach ($extractor->extractURLs($content) as $URL) {
                            $allLinks[] = $URL;
                        }
                    }
                }
            }
        }

        $allLinks = array_filter(array_unique($allLinks));
        if(empty($allLinks)) return;

        $shortenedLinks = collect($this->getOption('shortened_links', []));
        foreach ($allLinks as $link){
            // shorten the link if needed
            if(!$link) continue;

            if($shortenedLinks->firstWhere('target', $link)) {
                // this link was already shortened
                continue;
            }

            if($shortenedLinks->firstWhere('link', $link)) {
                // this is a short link; already shortened
                continue;
            }

            $short = LinkShortenerHelper::shortenLinkForAccount($this->account, $link, [
                'network' => $this->account->getType(),
                'socialbu_id' => $this->id . '|' . $this->created_at->toFormattedDateString(),
            ]);

            if(!$short || $short == $link){
                // not shortened or not needed to be shortened
                continue;
            }

            $shortenedLinks->push([
                'target' => $link,
                'link' => $short,
            ]);

        }

        // all links are shortened here
        if($shortenedLinks->count() === 0){
            return;
        }

        $linksMap = $shortenedLinks->mapWithKeys(function($r){
            return [
                $r['target'] => $r['link'],
            ];
        })->toArray();

        // first, content
        $this->content = strtr($this->content, $linksMap);
        $this->save();
        $this->refresh();

        // then options
        foreach ($optionKeys as $key => $type){
            $val = $this->getOption($key);
            if($val){
                if($type === 'string') {
                    $this->setOption($key, strtr($val, $linksMap));
                } else if($key === 'threaded_replies') {
                    // mainly for threaded_replies
                    $newArr = [];
                    foreach ($val as $tweet) {
                        if(is_array($tweet) && isset($tweet['tweet'])){ //old format support
                            $tweet['text'] = $tweet['tweet'];
                        }
                        if(is_array($tweet) && !isset($tweet['text'])){
                            $tweet['text'] = '';
                        }
                        $txt = is_array($tweet) ? $tweet['text'] : $tweet;
                        $newText = strtr($txt, $linksMap);

                        if(is_array($tweet)){
                            $tweet['text'] = $newText;
                        } else {
                            $tweet = $newText;
                        }

                        $newArr[] = $tweet;
                    }
                    $this->setOption($key, $newArr);
                }
            }
        }

        // shortened links have been created and replaced in content; save them
        $this->refresh();
        $this->setOption('shortened_links', $shortenedLinks->toArray());
    }

    /**
     * Get any link found in this post (only first link) or null if no link found
     * @return string|null
     */
    public function getLink()
    {

        if($this->getOption('link')){
            // if there is a custom link set already, we use that
            // this is primarily for fb post when there are two links in fb post and the user wants to set second link as primary
            if(filter_var($this->getOption('link'), FILTER_VALIDATE_URL)) {
                // only return if its valid
                return $this->getOption('link');
            }
        }

        //        // a more readably-formatted version of the pattern is on http://daringfireball.net/2010/07/improved_regex_for_matching_urls
        //        $pattern = '(?i)\b((?:https?:\/\/|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:\'\".,<>?«»“”‘’]))';
        //
        //        if (preg_match('/' . $pattern . '/', $this->content, $matches)) {
        //            $lnk = $matches[0];
        //            $lnk = (function($url, $scheme = 'http://') {
        //                return parse_url($url, PHP_URL_SCHEME) === null ? $scheme . $url : $url;
        //            })($lnk);
        //            return $lnk;
        //        }

        $linksFound =  $this->getLinksInContent();

        if(count($linksFound) > 0){
            // return the first link
            return $linksFound[0];
        }

        return null;
    }

    /**
     * Make sure the video is ready to post
     * @param bool $actuallyPrepare
     * @return boolean false if already converting, true if ready, null if it should be waited for now
     * @throws \Exception
     */
    public function prepareVideo(bool $actuallyPrepare = true){

        $failPost = function($msg){

            // remove the started flag
            $this->account->removeOption('video_convert_started');

            // set error msg
            $this->result = [
                '_success' => false,
                '_errorMsg' => $msg,
                '_timestamp' => time(),
            ];
            $this->save();
        };

        if($this->published_at || $this->getOption('video_convert_ended')){
            // if not video or is already published or is converted
            return true;
        }

        $attachments = $this->getAttachments();

        // check if no attachment
        if(count($attachments) === 0){
            return true;
        }

        // check mime type of all attachments
        $hasVideo = false;
        foreach ($attachments as $attachment){
            if(Str::contains($attachment['mime'], 'video')){
                $hasVideo = true;
                break;
            }
        }
        if(!$hasVideo){
            // no video found
            return true;
        }

        // check if all done
        $converted = true;
        foreach ($attachments as $attachment) {
            if(!isset($attachment['converted']))
                $converted = false;
        }

        if($converted){
            // all done already
            return true;
        }

        // only one post per account will be processed at the same time
        if($this->account->getOption('video_convert_started')){
            $startedAt = $this->account->getOption('video_convert_started.started_at', time() - 60 * 60 * 3);
            if($startedAt && time() - $startedAt > 60 * 60 * 2){
                // if the process is running for more than 2 hours, we can assume it's stuck
                $this->account->removeOption('video_convert_started');
            } else {
                return false;
            }
        }

        if(!$actuallyPrepare){
            if(app()->environment('production')) {
                dispatch((new OptimizeVideo($this))->onQueue('default_long')->onConnection('redis_long'));
            } else {
                dispatch((new OptimizeVideo($this)));
            }
            return null;
        }

        $this->account->setOption('video_convert_started', [
            'post_id' => $this->id,
            'started_at' => time(),
        ]);

        // mark as converting
        $this->setMultipleOptions([
            'video_convert_started' => true,
            'preparing_video_started_at' => time(),
        ]);

        $this->refresh();

        foreach ($attachments as $index => $attachment){

            if(isset($attachment['url'])){
                unset($attachments[$index]['url']);
            }

            if(isset($attachment['converted']) && $attachment['converted']){
                continue;
            }

            // ffmpeg - convert each video to mp4
            $path = $attachment['path'];

            $type = $attachment['type'];

            if($type === 'gif'){
                continue; // gif are also marked as video in the past, so skip
            }

            if(!\Storage::exists($path)){
                $failPost('File not found.');
                return false;
            }
            // get stream resource of the video
            $streamVideo = \Storage::readStream($path);

            $localPath = 'converting_videos/' . str_random() . '_' . basename($path);

            $newName = 'converted_' . str_random() . '_' . explode('.', basename($path))[0] . '.mp4';
            $newPath = str_replace(basename($localPath), $newName, $localPath);
            $newCloudPath = str_replace(basename($path), $newName, $path);

            // save the video locally
            \Storage::disk('local')->writeStream($localPath, $streamVideo);

            $ffprobe = FFProbe::create();

            try {
                $videoStream = $ffprobe->streams(storage_path('app/' . $localPath))->videos()->first();
            } catch (\Exception $exception){

                if(Str::contains($exception->getMessage(), 'Unable to probe')){
                    // if ffprobe is unable to probe the file, it means the file is invalid or inaccessible
                    $failPost('Video file is probably invalid. Please ensure a valid video file');
                } else {
                    $failPost('Video file is invalid or inaccessible: ' . $exception->getMessage());
                }

                if(\Storage::disk('local')->exists($localPath)) {
                    // if ig returns the file as it is, we simply move the orig file
                    // so check if localPath file really exists before deleting
                    \Storage::disk('local')->delete($localPath);
                }

                return false;
            }

            $audioStream = $ffprobe->streams(storage_path('app/' . $localPath))->audios()->first();

            // Get frames per second
            $frameRate = $videoStream->get('r_frame_rate');

            // Convert fraction format (e.g. "30000/1001" for 29.97 fps)
            if (strpos($frameRate, '/') !== false) {
                list($num, $den) = explode('/', $frameRate);
                $frameRate = round($num / $den, 2);
            }
            
            $frameRate = (float)$frameRate;

            $rotation = null;

            if($videoStream->has('rotation')){
                // get rotation
                $rotation = (int) $videoStream->get('rotation');
            } else {
                $sideDataList = $videoStream->get('side_data_list');
                if (is_array($sideDataList)) {
                    foreach ($sideDataList as $sideData) {
                        if (isset($sideData['rotation'])) {
                            $rotation = (int) $sideData['rotation'];
                            break 2; // exit both loops
                        }
                    }
                }
            }

            if($rotation !== null){
                // we need to reset the rotation first?
                // ffmpeg -i test.mp4 output.mp4 resets the rotation and applies it permanently (if it is in video)
                // https://stackoverflow.com/questions/69386275/mp4-and-rotation-remove-flags-but-set-rotation

                $ffmpeg = FFmpeg::create([
                    'timeout' => 1200,
                    'threads' => 16, // half of all cpus so ffmpeg doesn't use 100% cpu
                ]);
                $video = $ffmpeg->open(storage_path('app/' . $localPath));
                $video
                    // crf, preset, max queuing size
                    ->addFilter(new SimpleFilter(['-crf', '26']))
                    ->addFilter(new SimpleFilter(['-preset', 'veryfast']))
                    ->addFilter(new SimpleFilter(['-max_muxing_queue_size',  '9999'])); // fix for defective encoded video files; too large value can cause a high load

                // simply re encode and it will remove rotation
                $format = new \FFMpeg\Format\Video\X264('aac', 'libx264');
                $format->setPasses(1); // this is needed for not messing quality of some videos

                $fixedName = 'converting_videos/fixed_' . basename($localPath);

                try {
                    $video
                        ->save($format, storage_path('app/' . $fixedName));

                    // now move this file to $localPath location
                    rename(storage_path('app/' . $fixedName), storage_path('app/' . $localPath));

                    // re-get stream
                    $videoStream = $ffprobe->streams(storage_path('app/' . $localPath))->videos()->first();
                } catch (\Exception $exception){
                    \Log::error('rotation fix failed: ' . $rotation);
                    report($exception);
                }
            }

            if($this->account->type === 'tiktok.profile' && $frameRate < 23){
                $failPost('TikTok videos must have a frame rate of 23 or more.');
                if(\Storage::disk('local')->exists($localPath)) {
                    \Storage::disk('local')->delete($localPath);
                }
                return false;
            }


            // verify duration
            try {
                $durationSecs = (float)$ffprobe->format(storage_path('app/' . $localPath))->get('duration');
            } catch (\Exception $exception){
                $failPost('File is invalid or inaccessible: ' . $exception->getMessage());
                if(\Storage::disk('local')->exists($localPath)) {
                    // if ig returns the file as it is, we simply move the orig file
                    // so check if localPath file really exists before deleting
                    \Storage::disk('local')->delete($localPath);
                }
                return false;
            }

            // get width n height
            $dimensions = $videoStream->getDimensions();

            \InstagramAPI\Media\Video\FFmpeg::$defaultTimeout = 600; // increase timeout for this lib

            // if IG video, let it be handled by its dedicated class
            if(in_array($this->account->type, ['instagram.direct', 'instagram.api']) || ($this->account->type === 'facebook.page' && ($this->getOption('post_as_story') || $this->getOption('post_as_reel'))) ){

                $isVideo = Str::contains($attachment['mime'], 'video');

                $isStory = $this->getOption('post_as_story');
                $isReel = $isVideo && $this->getOption('post_as_reel');

                if($isVideo && !$isStory && in_array($this->account->type, ['instagram.direct', 'instagram.api'])){
                    $isReel = true; // videos are always posted as reels now
                }

                $mediaType = null;
                if($isReel) $mediaType = 'REELS';
                else if($isStory) $mediaType = 'STORIES';
                else if($isVideo) $mediaType = 'VIDEO';

                \InstagramAPI\Media\Video\FFmpeg::$defaultTimeout = 600; // seconds
                \InstagramAPI\Media\Video\FFmpeg::$defaultBinary = 'D:\ffmpeg\ffmpeg\bin\ffmpeg.exe';
                $videoFixOperation = InstagramMedia::EXPAND; // default operation

                try {
                    $igVideo = new InstagramVideo(storage_path('app/' . $localPath), [
                        'operation' => $videoFixOperation,
                        'targetFeed' => $mediaType === 'REELS' ? \InstagramAPI\Constants::FEED_REEL : ($mediaType === 'STORIES' ? \InstagramAPI\Constants::FEED_STORY : \InstagramAPI\Constants::FEED_TIMELINE) ,
                    ]);
                    $igFile = $igVideo->getFile();
                    $attachment['converted'] = true;
                    rename($igFile, storage_path('app/' . $newPath));
                } catch (\Exception $exception){}

            } else if(
                ($this->account->type === 'google.youtube' && $this->getOption('post_as_short')) ||
                $this->account->type === 'pinterest.profile'
            ){

                // if it's YouTube short and already has good dimensions
                if($this->account->type === 'google.youtube' && $dimensions->getWidth() === 1080 && $dimensions->getHeight() === 1920){
                    // mark as converted
                    $attachments[$index]['converted'] = true;

                    // cleanup
                    \Storage::disk('local')->delete($localPath);

                    // move on
                    continue;
                }

                try {
                    $igVideo = new InstagramVideo(storage_path('app/' . $localPath), [
                        'operation' => InstagramMedia::CROP,
                        'targetFeed' => \InstagramAPI\Constants::FEED_REEL, // tiktok videos & YouTube shorts are always posted as reels
                    ]);
                    $igFile = $igVideo->getFile();
                    $attachment['converted'] = true;
                    rename($igFile, storage_path('app/' . $newPath));
                } catch (\Exception $exception){}

            } else if ($this->account->type === 'tiktok.profile'){
                // if thumbnail image is set, we need to add the thumbnail to the video
                // https://stackoverflow.com/a/********
                // step 1: convert thumbnail to mp4 video of 2ms
                // step 2: convert/optimize the video with ffmpeg (with final/same codec as the thumbnail video)
                // step 3: convert both videos to mpeg-2 transport stream (ts) format
                // step 4: concatenate the thumbnail video with the original video
                // step 5: mark as converted
                if($this->getOption('thumbnail.key')){
                    $posterCloudKey = $this->getOption('thumbnail.key');

                    $posterLocalKey = 'temp/' . str_random() . '_' . basename($posterCloudKey);

                    try {
                        // we have a custom thumbnail, so we need to add it to the video
                        $posterStream = Storage::cloud()->readStream($posterCloudKey);

                        \Storage::disk('local')->writeStream($posterLocalKey, $posterStream);

                        $filePath = \Storage::disk('local')->path($posterLocalKey);

                        $ffmpeg = FFMpeg::create([
                            'timeout' => 1200,
                            'threads' => 16, // half of all cpus so ffmpeg doesn't use 100% cpu
                        ]);

                        // first, we convert the image to video of 2ms using the same fps and dimensions as the video
                        $posterVideo = $ffmpeg->open($filePath);

                        // ffmpeg -loop 1 -framerate 29.97 -i image.jpg -vf "scale=1080:1920" -c:v libx264 -t 0.2 -pix_fmt yuv420p thumbnail.mp4
                        $posterVideo
                            ->addFilter(new SimpleFilter(['-loop', '1']))
                            ->addFilter(new SimpleFilter(['-framerate', $frameRate]))
                            ->addFilter(new SimpleFilter(['-t', '0.2']))
                            ->addFilter(new SimpleFilter(['-vf', 'scale=' . $dimensions->getWidth() . ':' . $dimensions->getHeight() . ',fps=' . $frameRate]))
                            ->addFilter(new SimpleFilter(['-pix_fmt', 'yuv420p'])); // ensure compatibility with all devices

                        $posterFormat = new X264();
                        $posterFormat
                            ->setPasses(1)
                            ->setAudioCodec('aac')
                            ->setAudioKiloBitrate(128);

                        $posterVideo->save($posterFormat, $filePath . '.mp4'); // step 1 is done, now we have a thumbnail video

                        // optimize video using our standard treatment
                        $video = $ffmpeg->open(storage_path('app/' . $localPath));

                        $video
                            ->addFilter(new SimpleFilter(['-crf', '26']))
                            ->addFilter(new SimpleFilter(['-preset', 'veryfast']))
                            ->addFilter(new SimpleFilter(['-vf', 'scale=\'min(1280,ceil(iw/2)*2)\':-2,format=yuv420p'])) // scale to 1280 width as max
                            ->addFilter(new SimpleFilter(['-max_muxing_queue_size',  '9999'])); // fix for defective encoded video files; too large value can cause a high load

                        $format = new X264();
                        $format->setPasses(1); // this is needed for not messing quality of some videos

                        $format->on('progress', function ($video, $format, $percentage) {
                            //echo "$percentage% transcoded\n";
                        });

                        $format
                            //->setKiloBitrate(1000)
                            //->setAudioChannels(2)
                            ->setAudioCodec('aac')
                            ->setAudioKiloBitrate(128);

                        try {
                            $video
                                ->save($format, storage_path('app/' . $newPath));
                        } catch (\Exception $exception){
                            \Log::info($video->getFinalCommand($format, storage_path('app/' . $newPath)));
                            $failPost('File is invalid or inaccessible. Please make sure the file is a valid video file.');
                            if(\Storage::disk('local')->exists($localPath)) {
                                // if ig returns the file as it is, we simply move the orig file
                                // so check if the localPath file really exists before deleting
                                \Storage::disk('local')->delete($localPath);
                            }
                            return false;
                        }

                        // step 2 is done; now we have an optimized video
                        // step 3: convert both videos to mpeg-2 transport stream (ts) format
                        // ffmpeg -i image.mp4 -c copy -bsf:v h264_mp4toannexb -f mpegts image.ts
                        $thumbnailVideo = $ffmpeg->open($filePath . '.mp4');
                        $thumbnailVideo
                            ->addFilter(new SimpleFilter(['-c', 'copy']))
                            ->addFilter(new SimpleFilter(['-bsf:v', 'h264_mp4toannexb']))
                            ->addFilter(new SimpleFilter(['-f', 'mpegts']));
                        $thumbnailTsPath = $filePath . '.ts';

                        $thumbnailVideo->save((new X264('aac'))->setPasses(1), $thumbnailTsPath);

                        $mainVideo = $ffmpeg->open(storage_path('app/' . $newPath));
                        $mainVideo
                            ->addFilter(new SimpleFilter(['-c', 'copy']))
                            ->addFilter(new SimpleFilter(['-bsf:v', 'h264_mp4toannexb']))
                            ->addFilter(new SimpleFilter(['-f', 'mpegts']));
                        $mainTsPath = storage_path('app/' . $newPath . '.ts');

                        $mainVideo->save((new X264('aac'))->setPasses(1), $mainTsPath);

                        // step 4: concatenate the thumbnail video with the original video
                        $concatenatedVideoPath = $newPath . '_final.mp4';

                        $pathToActualVideo = \Storage::disk('local')->path($newPath);
                        $pathToFinalVideo = \Storage::disk('local')->path($concatenatedVideoPath);

                        $ffmpeg
                            ->open($pathToActualVideo)
                            ->concat([
                                $thumbnailTsPath,
                                $mainTsPath,
                            ])
                            ->saveFromSameCodecs($pathToFinalVideo, true);

                        // step 4 is done; now we have a final video with thumbnail
                        $attachment['converted'] = true;
                        $oldNewPath = $newPath; // save the old path for cleanup
                        $newPath = $concatenatedVideoPath; // our code later will use this path

                        if (\Storage::disk('local')->exists($oldNewPath)) {
                            // delete the old video
                            \Storage::disk('local')->delete($oldNewPath);
                        }

                        @unlink($thumbnailTsPath); // delete the thumbnail ts file
                        @unlink($mainTsPath); // delete the main ts file

                    } catch (\Exception $exception){
                        report($exception);
                    } finally {
                        // delete the local file
                        if (\Storage::disk('local')->exists($posterLocalKey)) {
                            \Storage::disk('local')->delete($posterLocalKey);
                        }
                    }
                }

            } else if(in_array($this->account->type, ['reddit.profile', 'reddit.subreddit'])){ // for reddit, the poster is required

                $ffmpeg = FFMpeg::create([
                    'timeout' => 1200,
                    'threads' => 16, // half of all cpus so ffmpeg doesn't use 100% cpu
                ]);

                $fVideo = $ffmpeg->open(storage_path('app/' . $localPath));

                $newPosterPath = 'converting_videos/' . $newName . '.jpg';

                $fVideo
                    ->frame(\FFMpeg\Coordinate\TimeCode::fromSeconds($durationSecs > 1 ? 1 : 0))
                    ->save(storage_path('app/' . $newPosterPath));

                // upload the poster
                \Storage::putFileAs('attachments', new File(storage_path('app/' . $newPosterPath)), basename($newCloudPath) . '.jpg');

                // set the poster path
                $attachments[$index]['poster_path'] = $newCloudPath . '.jpg';

                // cleanup
                \Storage::disk('local')->delete($newPosterPath);

            }


            $attachment['converted']  = $attachment['converted'] ?? false;

            if(!$attachment['converted']){
                $preset = 'veryfast';
                if($durationSecs > 20 * 60){
                    // don't convert, it's too large
                    if($type === 'mp4'){
                        // don't convert if mp4
                        $attachments[$index]['converted'] = true;

                        // cleanup
                        \Storage::disk('local')->delete($localPath);

                        // move on
                        continue;
                    }
                } else if($durationSecs < 5 * 60){
                    $preset = 'medium';
                }

                // convert with ffmpeg
                $ffmpeg = FFMpeg::create([
                    'timeout' => 1200,
                    'threads' => 16, // half of all cpus so ffmpeg doesn't use 100% cpu
                ]);
                $video = $ffmpeg->open(storage_path('app/' . $localPath));

                $video
                    ->addFilter(new SimpleFilter(['-crf', '26']))
                    ->addFilter(new SimpleFilter(['-preset', $preset]))
                    ->addFilter(new SimpleFilter(['-vf', 'scale=\'min(1280,ceil(iw/2)*2)\':-2,format=yuv420p'])) // scale to 1280 width as max
                    ->addFilter(new SimpleFilter(['-max_muxing_queue_size',  '9999'])); // fix for defective encoded video files; too large value can cause a high load

                $format = new X264();
                $format->setPasses(1); // this is needed for not messing quality of some videos

                $format->on('progress', function ($video, $format, $percentage) {
                    //echo "$percentage% transcoded\n";
                });

                $format
                    //->setKiloBitrate(1000)
                    //->setAudioChannels(2)
                    ->setAudioCodec('aac')
                    ->setAudioKiloBitrate(128);

                try {
                    $video
                        ->save($format, storage_path('app/' . $newPath));
                } catch (\Exception $exception){
                    \Log::info($video->getFinalCommand($format, storage_path('app/' . $newPath)));
                    $failPost('File is invalid or inaccessible. Please make sure the file is a valid video file.');
                    if(\Storage::disk('local')->exists($localPath)) {
                        // if ig returns the file as it is, we simply move the orig file
                        // so check if the localPath file really exists before deleting
                        \Storage::disk('local')->delete($localPath);
                    }
                    return false;
                }

            }

            // now save the video back to our storage
            \Storage::putFileAs('attachments', new File(storage_path('app/' . $newPath)), $newName);

            // now delete the video locally
            \Storage::disk('local')->delete($newPath);

            if(\Storage::disk('local')->exists($localPath)) {
                // if ig returns the file as it is, we simply move the orig file
                // so check if localPath file really exists before deleting
                \Storage::disk('local')->delete($localPath);
            }

            // set new path
            $attachments[$index]['path'] = $newCloudPath;
            $attachments[$index]['old_path'] = $path; // just for record

            $attachments[$index]['converted'] = true;

            try {
                // delete old cloud file
                \Storage::delete($path);
            } catch (\Exception $exception){
                report($exception);
            }

        }

        $options = $this->options;
        $options['attachments'] = $attachments;
        $this->options = $options;
        $this->save();

        // mark as converted
        $this->setMultipleOptions([
            'video_convert_ended' => true,
            'preparing_video_ended_at' => time(),
        ]);

        // add log if time took is more than 5 minutes
        $minutesTaken = now()->diffInMinutes(Carbon::createFromTimestamp($this->getOption('preparing_video_started_at')));
        if($minutesTaken > 5){
            \Log::warning('Post->prepareVideo(): Video conversion took ' . $minutesTaken . ' minutes', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);
        }

        $this->refresh(); // so that new options are available, v. important

        $this->account->removeOption('video_convert_started'); // important: remove the flag

        return true;

    }

    /**
     * This is mostly used for API posts
     * @param Post $post
     * @param $status
     * @return void
     */
    public static function postbackIfNeeded(Post $post, $status)
    {
        if ($post->getOption('postback_url')){
            $data = [
                'post_id' => $post->id,
                'account_id' => $post->account_id,
                'status' => $status,
            ];

            if ($status === 'published'){
                $data['external_id'] = $post->external_id;
                $data['permalink'] = $post->getPermalink();
            }

            dispatch(new SendHTTPRequest('POST', $post->getOption('postback_url'), [
                // guzzle options
                'json' => $data,
            ]));
        }
    }

    /**
     * Publish this post now!
     *
     * @param bool $actuallyPublish
     * @return $this
     * @throws \Exception
     */
    public function publish(bool $actuallyPublish = false)
    {
        if($this->published_at) {

            if($this->user && $this->user->getOption('debug')){
                \Log::info('Post->publish(): Post is already published', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);
            }

            return $this;
        }

        // check if account is inactive
        if(!$this->account->active){

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publish(): Account is not connected', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

            // account is inactive
            $this->result = [
                '_success' => false,
                '_errorMsg' => 'Account is not connected.',
                '_timestamp' => time(),
            ];
            $this->save();
            return $this;
        }

        // start: preparation check
        // this prep timestamp check is for handling race conditions for video posts
        // when publishing and video conversion takes place at same time
        $preparation_started_at = $this->getOption('preparation_started_at');
        if($preparation_started_at){
            $prepTs = Carbon::createFromTimestamp($preparation_started_at);

            if(now()->diffInMinutes($prepTs) > 5){
                // prep process taking more than 5 min
                // something wrong
                // reset so cron can take care of it
                $this->removeOption('preparation_started_at');
            }

            \Log::info('Post->publish(): Post preparation is already in progress', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

            // wait for the job to finish
            return $this;
        }
        // end: preparation check

        if($this->draft || !$this->approved) {

            // shouldn't happen but still
            if($this->user && $this->user->getOption('debug')){
                \Log::info('Post->publish(): Post is draft or not approved', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);
            }

            return $this;
        }

        // publish this post now (if its pending / scheduled)
        if ($this->result !== null) {

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publish(): Post is already processed (result is set)', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

            // already published
            return $this;
        }


        if (!$actuallyPublish) {

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publish(): We do not have to publish it right now', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

            if (!$this->getOption('in_queue')) {

                if($this->user && $this->user->getOption('debug'))
                    \Log::info('Post->publish(): Adding post to job queue', [
                        'post_id' => $this->id,
                        'account_id' => $this->account_id,
                    ]);

                $this->setMultipleOptions([
                    'in_queue' => true,
                    'in_queue_at' => time(),
                ]);

                // set has_post_in_queue flag for account
//                $this->account->setMultipleOptions([
//                    'has_post_in_queue' => true,
//                    'post_in_queue_at' => time()
//                ]);

                dispatch((new PublishPost($this))->onQueue('posts'));
            } else {

                if($this->user && $this->user->getOption('debug'))
                    \Log::info('Post->publish(): Post is already in queue', [
                        'post_id' => $this->id,
                        'account_id' => $this->account_id,
                    ]);

                $inQueueTimestamp = $this->getOption('in_queue_at');

                // backwards compatible code
                if(!$inQueueTimestamp){
                    $inQueueTimestamp = time();
                    $this->setOption('in_queue_at', $inQueueTimestamp);
                }

                $inQueueTs = Carbon::createFromTimestamp($inQueueTimestamp);

                if(now()->diffInMinutes($inQueueTs) > 10){
                    // publishing since more than x min
                    // this is strange, no post should take this amount of time
                    // so, remove in_queue so next time cron adds it to queue
                    $this->removeMultipleOptions(['in_queue', 'in_queue_at']);

                    \Log::warning('Post->publish(): Post is in queue for more than 10 minutes', [
                        'post_id' => $this->id,
                        'account_id' => $this->account_id,
                    ]);

                }
            }
            return $this;
        }

        $lastPost = Post::where('account_id', $this->account_id)
            ->where('published_at', '>', now()->subMinutes(30))
            ->orderBy('published_at', 'desc')
            ->first();

        if($lastPost){
            // now, we need to see if the current post is a duplicate; this is particularly for protection against any unexpected bug (e.g. from queues)
            $lastPostStr = $lastPost->content; // post content and type

            // now append attachments
            $lastPostStr .= collect($lastPost->getAttachments())->map(function($att){
                return $att['mime'] . ':' . $att['type'] . ':' . $att['size'];
            })->values()->sort()->implode(',');

            $currentPostStr = $this->content;

            // now append attachments
            $currentPostStr .= collect($this->getAttachments())->map(function(/** @var UploadedFile $att */ $att){
                return $att['mime'] . ':' . $att['type'] . ':' . $att['size'];
            })->values()->sort()->implode(',');

            if($lastPostStr === $currentPostStr){
                // account is inactive
                $this->result = [
                    '_success' => false,
                    '_errorMsg' => 'Duplicate post detected. Please try again.',
                    '_timestamp' => time(),
                ];

                $this->save();
                return $this;
            }
        }

        if($this->user && $this->user->getOption('debug'))
            \Log::info('Post->publish(): Actually publishing post', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

        // prepare video: if it's not prepared, we should wait for it to be prepared
        $prepared = $this->prepareVideo(false);
        if($prepared === false){
            // pending already
            $pendingTimestamp = $this->getOption('preparing_video_started_at');

            // backwards compatible code
            if(!$pendingTimestamp){
                $pendingTimestamp = time();
                $this->setOption('preparing_video_started_at', $pendingTimestamp);
            }

            $pendingTs = Carbon::createFromTimestamp($pendingTimestamp);

            if(now()->diffInHours($pendingTs) < 2){
                // so, the conversion process was started less than 2 hrs ago
                // and is still pending
                // wait for it

                \Log::info('Post->publish(): Video conversion is in progress', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

                $this->removeMultipleOptions(['in_queue', 'in_queue_at']);

                return $this;
            }

            \Log::warning('Video convert process took too long or got interrupted', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

            // so we ignore it
            // and try to continue
            $this->removeMultipleOptions(['video_convert_started', 'preparing_video_started_at', 'in_queue', 'in_queue_at']);
            $this->account->removeOption('video_convert_started');

            // skip for now, but next time it will prepare the video
            return $this;

        } else if ($prepared === null){
            // we should let the queue handle this
            \Log::info('Post->publish(): Video conversion is in progress; waiting for it to finish', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

            // remove in_queue so next time cron adds it to queue
            $this->removeMultipleOptions(['in_queue', 'in_queue_at']);

            return $this;
        }

        if($this->user && $this->user->getOption('debug'))
            \Log::info('Post->publish(): shorten links if needed', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

        $this->shortenLinksIfNeeded();

        // at this point, the video should be ready if there is a video attachment
        if($this->user && $this->user->getOption('debug'))
            \Log::info('Post->publish(): Everything is ready', [
                'post_id' => $this->id,
                'account_id' => $this->account_id,
            ]);

        $fn_to_execute = 'publishTo' . studly_case(str_replace('.', '_', $this->account->type)); // converts twitter.profile to TwitterProfile

        try {

            if (!method_exists($this, $fn_to_execute)) {
                throw new \Exception('Method ' . $fn_to_execute . ' does not exist.');
            }

            $this->{$fn_to_execute}();

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->' . $fn_to_execute . ': post published to account ' . $this->account_id, [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

            $options = $this->options;

            if (isset($options['in_queue']))
                unset($options['in_queue']);

            $this->options = $options;
            $this->save();

            // trigger automation: account_post event for this account
            $data = [
                'content' => $this->content,
                'has_media' => !empty($this->getAttachments()),
                'media_url' => !empty($this->getAttachments()) ? $this->getAttachments()[0]['url'] : null, // pass media url too
                'post_id' => $this->external_id,
                'permalink' => $this->getPermalink(),
            ];
            $evData = ['network' => $this->account->getNetwork(), 'type' => 'post', 'id' => $this->external_id, 'account_id' => $this->account_id];
            /** @var Automation[]|Collection $automations */
            $automations = Automation::where('event', 'account_post')->where('active', true)->where('tag', $this->account->getNetwork() . ':' . $this->account_id)->get();
            foreach ($automations as $automation){
                $automation->execute($data, $evData);
            }

            // save post hashtags for reporting purpose
            $twExtractor = new \Twitter\Text\Extractor();
            $hashtags = collect($twExtractor->extractHashtags($this->content))->unique();
            if(!empty($hashtags)){
                try {
                    get_insights_db()->table('post_hashtags')->insert($hashtags->map(function ($h) {
                        return [
                            'post_id' => $this->id,
                            'account_id' => $this->account_id,
                            'hashtag' => $h,
                        ];
                    })->toArray());
                } catch (\Exception $e){
                    report($e);
                }
            }

            // save metric
            try {
                get_insights_db()->table('misc_metrics')->insert([
                    'user_id' => $this->user_id,
                    'account_id' => $this->account_id,
                    'object' => 'post:' . $this->id,
                    'action' => 'post_published',
                ]);
            } catch (\Exception $e) { }

            // send event
            try {
                EmailListHelper::getInstance()->sendEvent($this->user, 'post_published', $this->account->name . ' (' . $this->account->type . ')');
            } catch (\Exception $exception){
                report($exception);
            }

            // postback
            $this->postbackIfNeeded($this, 'published');

            // compare the publish_at and now to see if it's published late
            $lateMinutes = now()->diffInMinutes($this->publish_at);
            if ($lateMinutes >= 10) {
                \Log::warning('Post published late by ' . $lateMinutes . ' minutes', [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);
            }

        } catch (\Exception $exception) {
            // exception containing original error
            $targetException = $exception->getPrevious() ?: $exception;

            // if it's a guzzle client exception, log the response
            if($targetException instanceof \GuzzleHttp\Exception\ClientException){
                $response = $targetException->getResponse();
                $request = $targetException->getRequest();

                $errorString = 'Post publish error';
                if($request){
                    $errorString .= ' - ' . $request->getMethod() . ' ' . $request->getUri();
                }

                $responseBody = $response ? $response->getBody()->getContents() : null;
                if($responseBody){
                    $errorString .= ' | Response: ' . $responseBody;
                }

                report(new \Exception($errorString, 0, $targetException));
            }

            $options = $this->options;
            unset($options['in_queue']);

            $prevException = $exception->getPrevious();

            if($prevException){
                \Log::error('Post->' . $fn_to_execute . ': ' . $prevException->getMessage(), [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);
            }

            // set error msg
            $this->result = [
                '_success' => false,
                '_errorMsg' => $exception->getMessage(),
                '_trace' => $exception->getTraceAsString(),
                '_prevTrace' => $prevException ? $prevException->getTraceAsString() : null,
                '_timestamp' => time(),
            ];

            // check tries; remove this check if its not used anymore
            if(isset($options['tries'])) {
                $options['_tries'] = $options['tries']; // for record
                unset($options['tries']); // reset
            }

            // save it
            $this->options = $options;
            $this->save();

            if($this->user) {
                // notify user of this failure
                $this->user->notify(new PostFailure($this));
            }

            $this->account->testConnection(true, 'Unable to publish post. ' . $exception->getMessage());

            // postback
            $this->postbackIfNeeded($this, 'failed');

            if($this->user && $this->user->getOption('debug'))
                \Log::error('Post->' . $fn_to_execute . ': ' . $exception->getMessage(), [
                    'post_id' => $this->id,
                    'account_id' => $this->account_id,
                ]);

        }

        return $this;
    }

    /**
     * Publish to twitter.
     * @return void
     * @throws \Exception
     */
    private function publishToTwitterProfile()
    {

        if( starts_with($this->content, '@') ){
            // spam behavior not allowed by twitter
            throw new \Exception('Tweet cannot start with "@"');
        } else if(substr_count($this->content, '@') > $this->account->user->getOption('max_tweet_mentions', 6)){
            throw new \Exception('Tweet should have less than ' . $this->account->user->getOption('max_tweet_mentions', 6) . ' @ characters. Contact us if you think this should be increased.');
        }

        // only allow 60 tweets per hr for tw account
        $lastHrTweetsCount = Post::whereAccountId($this->account_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subHours(1))
            ->count();

        $maxTweetsPerHr = $this->account->user->getOption('max_tweets_per_hr', 60);

        if($lastHrTweetsCount >= $maxTweetsPerHr){
            // stop... can be spam
            throw new \Exception('Potentially spam: You are tweeting a lot. Please contact us if you think this should not happen.');
        }

        /** @var TwitterOauthCustom $tw */
        $tw = $this->account->getApi();

        $attachments = $this->getAttachments();

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = ['mp4'];

        // validate attachments
        if (!empty($attachments)) {

            $n_attachments = count($attachments);
            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            // validate media types
            foreach ($attachments as $attachment) {
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                    throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                }
            }

            // multiple attachments should be static images (no gif / video)
            if ($n_attachments > 1) {
                foreach($attachments as $attachment) {
                    if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                        throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per tweet');
                    }
                }
            }

        }

        $media_ids = [];

        // use helper to upload media to twitter
        foreach ($attachments as $index => $attachment) {
            
            $fStream = Storage::readStream($attachment['path']);
            $localPath = 'twitter_uploading/' . str_random() . '_' . basename($attachment['path']);
            // save the media locally
            Storage::disk('local')->writeStream($localPath, $fStream);

            if($this->type === 'video') {
                try {
                    $this->validateVideo(storage_path('app/' . $localPath));
                } catch (\Exception $exception) {
                    // cleanup
                    Storage::disk('local')->delete($localPath);
                    // now throw exception
                    throw $exception;
                }
            }

            $altText = $this->getOption('media_alt_text.' . $index,  $this->getOption('image_alt_text.' . $index));
            
            // backward compatibilty for existing posts
            if(strlen($altText) > 1000){
                throw new \Exception('Alt text for media should be less than 1000 characters');
            }
            // upload to twitter
            try {                
                $media_id = $tw->uploadMediaToTwitter(storage_path('app/' . $localPath), $this->account);
                
                if($altText){
                    $tw->addMediaAltText($media_id, $altText, $this->account);
                }

                $media_ids[] = $media_id;
              
            } catch (\Exception $exception) {
                // cleanup
                Storage::disk('local')->delete($localPath);
                if(!Str::contains($exception->getMessage(), ['expired', 'suspended', 'locked',])){
                    report($exception);
                }
                // now throw exception
                throw $exception;
            }
            Storage::disk('local')->delete($localPath); // cleanup
        }

        $content = $this->content;

        // parse @[id:text] to @text
        $content = preg_replace('/@\[([0-9]+):([^\]]+)\]/', '@$2', $content);

        try {
            $tw->setApiVersion('2');
            $response = $tw->post("tweets", array_filter([
                'text' => $content,
                'media' => !empty($media_ids) ? [ 'media_ids' => $media_ids ] : null
            ]), true);
            $tw->setApiVersion('1.1');

        } catch (\Exception $e) {
            if(Str::contains($e->getMessage(), 'Unknown SSL protocol')){
                // duck!!!, retry because this error is just too random and IDK why
                $this->publishToTwitterProfile();
                return;
            }
            throw $e;
        }

        if ($tw->getLastHttpCode() == 200 || $tw->getLastHttpCode() == 201) {
            // good
            $this->external_id = $response->data->id;
            $this->published_at = Carbon::now();
            $this->result = array_merge((array)$response, [
                '_success' => true,
            ]);
            $this->save();

            // now post threaded replies if needed
            if(isset($this->options['threaded_replies']) && !empty($this->options['threaded_replies'])){
                $lastTweetId = $response->data->id;
                $exceptions = [];
                $threadTweetIds = [];
                $threaded_replies = $this->options['threaded_replies'];

                if(!is_array($threaded_replies)) // shouldn't happen
                    $threaded_replies = (array) @json_decode($this->options['threaded_replies'], true);
                
                foreach($threaded_replies as $tweetIndex => $tweet){
                    $threadMediaIds = [];

                    if(is_array($tweet)){
                        if(!isset($tweet['media'])) $tweet['media'] = [];
                        foreach ($tweet['media'] as $file){
                            $path = $file['key'];
                            $localPath = 'temp/' . str_random() . '_' . basename($path);
                            try {
                                $fStream = \Storage::cloud()->readStream($path);
                                \Storage::disk('local')->writeStream($localPath, $fStream);
                                $threadMediaIds[] = $tw->uploadMediaToTwitter(storage_path('app/' . $localPath), $this->account);
                            } catch (\Exception $exception) {
                                // cleanup
                                Storage::disk('local')->delete($localPath);
                                $exceptions[$tweetIndex] =  $exception;
                            }
                        }
                    }

                    $content = '';
                    if(is_array($tweet)){
                        if(isset($tweet['text'])){
                            $content = $tweet['text'];
                        } else if(isset($tweet['tweet'])){ // old format
                            $content = $tweet['tweet'];
                        } else {
                            $content = '';
                        }
                    } else {
                        $content = $tweet;
                    }
                    // parse @[id:username] to @username
                    $content = preg_replace('/@\[([0-9]+):([^\]]+)\]/', '@$2', $content);
                    $tries = 0;
                    while($tries < 10){
                        try {
                            $tw->setApiVersion('2');
                            $responseTweet = $tw->post("tweets", array_filter([
                                'text' => $content,
                                'reply' => [
                                    'in_reply_to_tweet_id' => $lastTweetId
                                ],
                                'media' => !empty($threadMediaIds) ? [ 'media_ids' => $threadMediaIds ] : null
                            ]), true);
                            $tw->setApiVersion('1.1');
                            $lastTweetId = $responseTweet->data->id;
                            $threadTweetIds[$tweetIndex] = $responseTweet->data->id;
                            break;
                        } catch (\Exception $e) {
                            ++$tries;
                            if(Str::contains($e->getMessage(), 'Unknown SSL protocol')){
                                // retry
                                continue;
                            }
                            $exceptions[$tweetIndex] =  $e;
                            break;
                        }
                    }
                    sleep(1); // just to make sure we don't look spam to twitter may be
                }

                // if we are here, all replies are processed
                $result = $this->result;
                $result['threaded_replies'] = [
                    'tweetIds' => $threadTweetIds,
                    'exceptions' => collect($exceptions)->map(function($e){
                        /** @var \Exception $e */
                        return $e->getTraceAsString();
                    })->toArray(),
                ];
                $this->result = $result;
                $this->save();
            }

        } else {
            \Log::error("Twitter API error: \n" . json_encode($tw->getLastBody()));

            $lastBody = $tw->getLastBody();
            if(isset($lastBody, $lastBody->errors) && !empty($lastBody->errors)){
                throw new \Exception($lastBody->errors[0]->message);
            }

            throw new \Exception('HTTP error (' . $tw->getLastHttpCode() . ') while posting to twitter: ' . json_encode($tw->getLastBody()));
        }

    }
    
    /**
     * Publish to Facebook group
     * @return void
     * @throws \Exception
     */
    private function publishToFacebookGroup(){        
        try{
            $this->publishToFacebookPage();
        }catch(\Exception $e){
            //for app not installed case, we throw custom exception
            if(Str::contains(strtolower($e->getMessage()), ['requires app being installed in the group'])){
                throw new \Exception('It seems you have not installed the SocialBu app in your group.');
            }

            throw $e;
        }
    }

    /**
     * Publish to facebook page.
     * @return void
     * @throws \Exception
     */
    private function publishToFacebookPage()
    {
        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();

        /** @var Facebook $fb */
        $fb = $this->account->getApi();

        $attachments = $this->getAttachments();

        // check if there are post attachments (media)
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            if ($n_attachments > 0) { // all attachments should be static images (no gif / video)
                foreach ($attachments as $attachment)
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                        throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
            }
        }

        $post_data = [
            'message' => $this->content,
        ];
        
        $location = $this->getOption('location', []);

        $postAsStory = $this->getOption('post_as_story');

        $postAsReel = $this->getOption('post_as_reel');

        $localPath = '';
        try {
            if($this->type === 'video' && !empty($attachments)){

                $fStream = Storage::readStream($attachments[0]['path']);

                $localPath = 'facebook_uploading/' . str_random() . '_' . basename($attachments[0]['path']);

                // save the media locally
                Storage::disk('local')->writeStream($localPath, $fStream);

                try {
                    $videoDetails = $this->validateVideo(storage_path('app/' . $localPath));
                } catch (\Exception $exception){
                    // cleanup
                    Storage::disk('local')->delete($localPath);
                    // now throw exception
                    throw $exception;
                }

                if($postAsStory || $postAsReel){

                    if($postAsReel){
                        // max duration allowed is 90s
                        if($videoDetails['duration'] > 90){
                            throw new \Exception('Reel video duration should be less than 90 seconds.');
                        }
                    }

                    $endpointPath = $postAsStory ? 'video_stories' : 'video_reels';

                    try {
                        $res = $fb->post($this->account->account_id . '/' . $endpointPath , [
                            'upload_phase' => 'start',
                        ])->getDecodedBody();
            
                        $upload_url = $res['upload_url'];
                        $video_id = $res['video_id'];
                    } catch (\Exception $e) {
                        throw new \Exception('Some error occurred while initiating the video upload. Please try again.');
                    }

                    // copy vid file to public folder, so we can ask fb to download it
                    \Storage::disk('public')->writeStream('temp/' . $localPath, \Storage::disk('local')->readStream($localPath));                    
            
                    try {
                        $guzzle = guzzle_client(
                            [
                                'headers' => [
                                    'file_url' => \Storage::disk('public')->url('temp/' . $localPath),
                                ],
                            ]
                        );

                        $access = json_decode($this->account->token, true);
                        $access_token = $access['token'];

                        $res = $guzzle->post($upload_url . '?access_token=' . $access_token);

                    } catch (\Exception $e) {
                        throw new \Exception('Some error occurred while uploading the '. $postAsStory ? 'story' : 'reel ' .' video. Please try again.');
                    }

                    //check upload status
                    $success = false;
                    $tries = 0;
                    while(!$success && $tries < 5){
                        $tries++;
                        try{
                            $res = $fb->get($video_id . '?fields=status')->getDecodedBody();
                            if($res['status']['video_status'] === 'upload_complete'){
                                $success = true;
                            }
                            sleep(5);
                        } catch(\Exception $e){
                            $tries++;
                            sleep(5);
                        }
                    }

                    //time to upload video
                    if($success){
                        try {
                            $res = $fb->post($this->account->account_id . '/' . $endpointPath, array_filter([
                                'video_id' => $video_id,
                                'upload_phase' => 'finish',
                                'video_state' => 'PUBLISHED',
                                'place' => $endpointPath == 'video_reels' ? Arr::get($location, 'place_id', null) : null,
                                'description' => $post_data['message'],
                            ]));
                            $response = $res->getDecodedBody();
                        }catch(\Exception $e){
                            throw new \Exception('Some error occurred while finishing the video uploading. Please try again.');
                        }
                    }else {
                        throw new \Exception('Video uploading was not successful. Please try again.');
                    }

                } else {
                    $response = $fb->uploadVideo($this->account->account_id . '/videos', storage_path('app/' . $localPath), array_filter([
                        'description' => $post_data['message'],
                        'title' => $this->getOption('video_title'),
                    ]));
                    $video_id = $response['video_id'];
                }

                // cleanup
                Storage::disk('local')->delete($localPath);

            } else {

                $post_url = '/' . $this->account->account_id . '/';

                $media_id = null;
                if (count($attachments) > 0) {
                    if(count($attachments) === 1 && !$postAsStory) {
                        $post_data['caption'] = $post_data['message'];
                        unset($post_data['message']);

                        $fStream = Storage::readStream($attachments[0]['path']);

                        $localPath = 'facebook_uploading/' . str_random() . '_' . basename($attachments[0]['path']);

                        // save the media locally
                        Storage::disk('local')->writeStream($localPath, $fStream);

                        // use source with $fb->fileToUpload() to send the file directly because storage endpoint can go down too causing errors
                        $post_data['source'] = $fb->fileToUpload( storage_path('app/' . $localPath) );
                        
                        $altText = $this->getOption('media_alt_text.0', $this->getOption('image_alt_text.0'));
                        if($altText) {
                            $post_data['alt_text_custom'] = $altText;
                        }
                        $post_url .= 'photos';
                    } else {
                        // upload all images to fb
                        $post_data['attached_media'] = [];
                        foreach($attachments as $index => $attachment){
                       
                            $fStream = Storage::readStream($attachment['path']);
                            $localPath = 'facebook_uploading/' . str_random() . '_' . basename($attachment['path']);

                            // save the media locally
                            Storage::disk('local')->writeStream($localPath, $fStream);
                            $altText = $this->getOption('media_alt_text.' . $index, $this->getOption('image_alt_text.' . $index));

                            // use source with $fb->fileToUpload() to send the file directly because storage endpoint can go down too causing errors
                            $res = $fb->post($post_url . 'photos', array_merge([
                                'caption' => $post_data['message'],
                                'source' => $fb->fileToUpload(storage_path('app/' . $localPath)),
                                'published' => false,
                            ], array_filter([
                                'alt_text_custom' => $altText,
                            ])));

                            // delete the local file
                            Storage::disk('local')->delete($localPath);
                            $localPath = '';

                            $media_id = $res->getGraphNode()->getField('id');
                            
                            $post_data['attached_media'][] = [
                                'media_fbid' => $media_id,
                            ];
                        }
                        $post_data['attached_media'] = json_encode($post_data['attached_media']);
                        $post_url .= 'feed';
                    }
                } elseif (($link = $this->getLink())) {
                    $post_data['link'] = $link;
                    
                    $post_url .= 'feed';
                } else {
                    $post_url .= 'feed';
                }

                if($postAsStory){
                    $post_url = $this->account->account_id . '/photo_stories';
                    $post_data = [
                        'photo_id' => $media_id,
                    ];
                    $response = $fb->post($post_url, $post_data)->getDecodedBody();
                }else {

                    if(!empty($location) && isset($location['place_id'])){
                        $post_data['place'] = $location['place_id'];
                    }
                    $response = $fb->post($post_url, $post_data);
                }
            }
        } catch (\Exception $e) {
            if(!empty($localPath) && Storage::disk('local')->exists($localPath)){
                Storage::disk('local')->delete($localPath);
            }
            throw $e;
        }


        // cleanup if needed
        if(!empty($localPath) && Storage::disk('local')->exists($localPath)){
            Storage::disk('local')->delete($localPath);
        }

        if ( ($this->type === 'video' && $response['success']) || $postAsStory || $postAsReel || ($this->type !== 'video' && !$response->isError())) {
            // add comment on post
            $extraResult = [];
            if($this->getOption('comment') && !$postAsStory && !$postAsReel) {
                $postId = $this->type === 'video' ? $response['video_id'] : $response->getGraphNode()->getField('id');
                try {
                    $commentText = $this->getOption('comment');
                    $fb->post('/' . $postId . '/comments', ['message' => $commentText]);
                } catch( \Exception $e){
                    // ignore but save; because the post was actually published
                    $extraResult['comment_error'] = $e->getMessage();
                }
            }

            // add video thumbnail if needed
            if(isset($video_id) && $this->type === 'video' && $this->getOption('thumbnail.key') && !$postAsStory) {
                $path = $this->getOption('thumbnail.key');
                $posterStream = Storage::cloud()->readStream($path);

                $posterLocalKey = 'temp/' . str_random() . '_' . basename($path);

                \Storage::disk('local')->writeStream($posterLocalKey, $posterStream);
                $filePath = \Storage::disk('local')->path($posterLocalKey);

                //The source for the thumbnail, an image file
                try{
                    $fb->post($video_id . '/thumbnails', [
                        'is_preferred' => true,
                        'source' => $fb->fileToUpload($filePath),
                    ])->getDecodedBody();
                } catch(\Exception $e){
                    $extraResult['thumbnail_error'] = $e->getMessage();
                } finally {
                    Storage::disk('local')->delete($posterLocalKey);
                }
            }

            // good
            if($postAsReel && isset($video_id)){
                $this->external_id = $video_id; //for reels, there is no post_id in response so using video id... which seems off
                $this->published_at = Carbon::now();
                $this->result = array_merge( (array)$response, [
                    '_success' => true,
                ], $extraResult);
            }else {
                $this->external_id = $this->type === 'video' && !$postAsStory ? $response['video_id'] : ($postAsStory ? $response['post_id'] : $response->getGraphNode()->getField('id'));
                $this->published_at = Carbon::now();
                $this->result = array_merge( $this->type === 'video' && !$postAsStory ? $response : ($postAsStory ? ["post_id" => $response['post_id']] : $response->getDecodedBody()), [
                    '_success' => true,
                ], $extraResult);
            }

            $this->save();

        } else {
            if($this->type === 'video'){
                throw new \Exception('Unknown error occurred while uploading the video');
            } else {
                throw new \Exception('HTTP error (' . $response->getHttpStatusCode() . ') while posting to facebook');
            }
        }

    }

    /**
     * Publish image to instagram
     * @param int $tries
     * @return void
     * @throws \Exception
     */
    private function publishToInstagramDirect($tries = 1)
    {
        throw new \Exception('Post scheduling/publishing is not available for Instagram Direct Accounts anymore. It will be available for only those accounts that are connected via Facebook.');
    }

    /**
     * Publish image to instagram
     * @return void
     * @throws \Exception
     */
    private function publishToInstagramApi()
    {
        \Log::info("publishtoins");
        \Log::info("options:",[$this->options]);
        if(!$account) {
            /** @var Account $account */
            $account = $this->account;
        }

        $ALLOWED_MEDIA_TYPES = $account->getAttachmentTypes();

        $attachments = $this->getAttachments();

        if($this->user && $this->user->getOption('debug'))
            \Log::info('Post->publishToInstagramApi(): Publishing post to account ' . $account->account_id, [
                'post_id' => $this->id,
                'account_id' => $account->account_id,
                'attachments' => $attachments,
                'content' => $this->content,
            ]);

        // check if there are post attachments (media)
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            if ($n_attachments > 0) { // all attachments should be static images (no gif / video)
                foreach ($attachments as $attachment)
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                        throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
            }
        } else {
            throw new \Exception('No media attached');
        }
        
        
        /** @var Facebook $fb */
        $fb = $account->getApi();


        /** @var \App\Helpers\Instagram|Facebook $instagram */
        $instagram = $account->getApi();

        // // make sure limit is not reached
        $currUsage = $instagram->get('/' . $account->account_id . '/content_publishing_limit')->getGraphEdge()[0]->getField('quota_usage');

        if($currUsage >= 25){

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publishToInstagramApi(): Max limit reached', [
                    'post_id' => $this->id,
                    'account_id' => $account->account_id,
                    'currUsage' => $currUsage,
                ]);

            // max limit reached
            throw new \Exception('Maximum allowed publishing limit reached (25 posts per 24-hr window)');
        }

        $location = $this->getOption('location', []);

        // convert empty newlines to invisible space characters newlines so they actually appear as it is on ig
        $content = null;
        if(!empty($this->content)){

            $content = trim($this->content);

            $str_lines = explode("\n", $content);

            foreach($str_lines as &$line){
                if($line === '') $line = '⠀';
            }

            $content = implode("\n", $str_lines);
        }

        try {

            $isCarousel = $n_attachments > 1;
            $containerIds = [];
            $containerId = null;

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publishToInstagramApi(): going to process attachments', [
                    'post_id' => $this->id,
                    'account_id' => $account->account_id,
                ]);

            //only 3 collaborators allowed
            $collaborators = explode(',', $this->getOption('collaborators', ''));
            $collaborators = array_slice($collaborators, 0, 3);
            $collaborators = implode(',', $collaborators);

            // upload media files
            foreach($attachments as $index => $attachment) {

                $isVideo = Str::contains($attachment['mime'], 'video');

                $isStory = $this->getOption('post_as_story');
                $isReel = $isVideo && $this->getOption('post_as_reel', !$isStory); // videos are posted as reel always
                $altText = $this->getOption('media_alt_text.' . $index);
                $shareReelToFeed = $this->getOption('share_reel_to_feed');
                
                $mediaType = null;
                if($isReel) {
                    $mediaType = 'REELS';
                }else if($isStory) {
                    $mediaType = 'STORIES';
                }else if($isVideo) {
                    $mediaType = 'VIDEO';
                }


                $fStream = \Storage::readStream($attachment['path']);

                $localPath = 'instagram_uploading/' . str_random() . '_' . basename($attachment['path']);

                // save the media locally
                \Storage::disk('local')->writeStream($localPath, $fStream);

                $igMedia = \Storage::disk('local')->path($localPath);
                if($isVideo){

                    if($this->user && $this->user->getOption('debug'))
                        \Log::info('Post->publishToInstagramApi(): processing video', [
                            'post_id' => $this->id,
                            'account_id' => $account->account_id,
                            'attachment' => $attachment,
                            'video_path' => storage_path('app/' . $localPath),
                        ]);
                    
                    $vidInfo = $this->validateVideo(storage_path('app/' . $localPath));

                    if($this->user && $this->user->getOption('debug'))
                        \Log::info('Post->publishToInstagramApi(): video validated', [
                            'post_id' => $this->id,
                            'account_id' => $account->account_id,
                            'attachment' => $attachment,
                        ]);

                    \InstagramAPI\Media\Video\FFmpeg::$defaultTimeout = 480; // seconds
                    if($mediaType === 'REELS'){
                        // if width and height are already reel dimensions, don't process again
                        if($vidInfo['width'] === 1080 && $vidInfo['height'] === 1920){
                            // no need to process again
                        } else if($vidInfo['width'] === 720 && $vidInfo['height'] === 1280){
                            // no need to process again
                        } else {
                            // convert to reel dimensions
                            if($this->user && $this->user->getOption('debug'))
                                \Log::info('Post->publishToInstagramApi(): creating video object', [
                                    'post_id' => $this->id,
                                    'account_id' => $account->account_id,
                                ]);

                            $video = new InstagramVideo(storage_path('app/' . $localPath), [
                                'operation' =>  InstagramMedia::EXPAND,
                                'targetFeed' => \InstagramAPI\Constants::FEED_REEL,
                            ]);

                            if($this->user && $this->user->getOption('debug'))
                                \Log::info('Post->publishToInstagramApi(): video variable created', [
                                    'post_id' => $this->id,
                                    'account_id' => $account->account_id,
                                ]);

                            $igMedia = $video->getFile();
                        }
                    } else {

                        $video = new InstagramVideo(storage_path('app/' . $localPath), [
                            'operation' =>  InstagramMedia::EXPAND,
                            'targetFeed' => $mediaType === 'STORIES' ? \InstagramAPI\Constants::FEED_STORY : \InstagramAPI\Constants::FEED_TIMELINE,
                        ]);

                        if($this->user && $this->user->getOption('debug'))
                            \Log::info('Post->publishToInstagramApi(): video variable created', [
                                'post_id' => $this->id,
                                'account_id' => $account->account_id,
                            ]);

                        $igMedia = $video->getFile();

                    }

                } else {

                    if($this->user && $this->user->getOption('debug'))
                        \Log::info('Post->publishToInstagramApi(): processing image', [
                            'post_id' => $this->id,
                            'account_id' => $account->account_id,
                            'attachment' => $attachment,
                        ]);

                    $photo = new InstagramPhoto(storage_path('app/' . $localPath), array_filter([
                        'operation' => $mediaType === 'STORIES' ? InstagramMedia::EXPAND : InstagramMedia::CROP,
                        'targetFeed' => $mediaType === 'STORIES' ? \InstagramAPI\Constants::FEED_STORY : null,
                        'bgColor' => [0,0,0] //black bg
                    ]));
                    $igMedia = $photo->getFile();
                }

                if($this->user && $this->user->getOption('debug'))
                    \Log::info('Post->publishToInstagramApi(): media object created', [
                        'post_id' => $this->id,
                        'account_id' => $account->account_id,
                    ]);

                if( storage_path('app/' . $localPath) !==  $igMedia ) {
                    // move file to $localPath
                    if (!rename($igMedia, storage_path('app/' . $localPath))) {
                        throw new \Exception('File system error');
                    }
                }

                if($this->user && $this->user->getOption('debug'))
                    \Log::info('Post->publishToInstagramApi(): media file ready', [
                        'post_id' => $this->id,
                        'account_id' => $account->account_id,
                    ]);

                // copy vid file to public folder, so we can ask fb to download it
                \Storage::disk('public')->writeStream('temp/' . $localPath, \Storage::disk('local')->readStream($localPath));

                // get url to vid file
                $url = \Storage::disk('public')->url('temp/' . $localPath);
                $tags = $this->getOption('tags');
                $videoThumbnailCurrTime = $this->getOption('videoThumbnailCurrTime');
                $videoThumbnailInMilliSeconds = null;
                if($videoThumbnailCurrTime !== null && $videoThumbnailCurrTime !== '') {
                   $videoThumbnailInMilliSeconds = $videoThumbnailCurrTime * 1000;
                }
                $thumbnailUrl = null;
                if(isset($this->getOption('thumbnail')['key'])){
                    $path = $this->getOption('thumbnail')['key'];
                    $thumbnailUrl = \Storage::cloud()->temporaryUrl($path, now()->addDays(5));
                    if($this->user && $this->user->getOption('debug'))
                        \Log::info('Post->publishToInstagramApi(): processing thumbnail', [
                            'post_id' => $this->id,
                            'account_id' => $account->account_id,
                            'thumbnail' => $this->getOption('thumbnail'),
                        ]);
                }

                try {

                    if($isVideo && !$isStory) {
                        $mediaType = 'REELS'; // for video, we always use reels media type
                    }

                    // array filter will remove all null/false values
                    $response = $instagram->post('/' . $account->account_id . '/media', array_filter([
                        'caption' => !$isStory && !$isCarousel ? $content : null, // only when single media
                        'location_id' => !$isCarousel ? Arr::get($location, 'place_id', null) : null, // only when single media
                        $isVideo ? 'video_url' : 'image_url' => $url,
                        'cover_url' => $thumbnailUrl ? $thumbnailUrl : null,
                        'media_type' => $mediaType, // only for video or reel or stories
                        'is_carousel_item' => !$isStory && $isCarousel ? true : null, // only for carousel media
                        'share_to_feed' => !$isStory && $shareReelToFeed ? true : null, // only for reels
                        'user_tags' => (!$isStory || !$isReel) ?  $tags : null,
                        'thumb_offset' => (!$isStory  || $isCarousel) ? $videoThumbnailInMilliSeconds : null,
                        'collaborators' => !$isCarousel && !$isStory ? $collaborators : null,
                        'alt_text' => !$mediaType ? $altText : null // only for regular or carousel posts
                    ]));

                    $containerId = $response->getGraphNode()->getField('id');

                    if($isVideo){
                        // get status
                        $statusCode = null;
                        $tries = 1;
                        do {
                            // FB: "We recommend querying a container's status once per minute, for no more than 5 minutes."
                            if($tries >= 20){
                                // also notify us
                                report(new \Exception('Instagram could not process the video in time: ' . $statusCode));
                                throw new \Exception('Failed to publish: Instagram could not process the media');
                            }
                            $statusCode = $instagram->get('/' . $containerId . '?fields=status_code')->getGraphNode()->getField('status_code');

                            if($statusCode === 'IN_PROGRESS') {

                                if($this->user && $this->user->getOption('debug'))
                                    \Log::info('Post->publishToInstagramApi(): video status', [
                                        'post_id' => $this->id,
                                        'account_id' => $account->account_id,
                                        'status' => $statusCode,
                                    ]);

                                sleep(60);

                            }

                            ++$tries;
                        } while($statusCode === 'IN_PROGRESS');
                    }

                    // if we are here, the container is ready for publishing
                    $containerIds[] = $containerId;

                } catch (\Exception $e) {
                    throw new \Exception('Unable to upload the media: ' . $e->getMessage(), 0, $e);
                }

                // cleanup
                try {
                    \Storage::disk('local')->delete($localPath);
                    \Storage::disk('public')->delete('temp/' . $localPath);
                } catch (\Exception $exception){}
            }

            if($isCarousel){
                // for carousel, we need to create a new container that has all child containers
                $containerId = null;
                try {
                    $response = $instagram->post('/' . $account->account_id . '/media', array_filter([
                        'caption' => $content, // for carousel, we add caption to the carousel container
                        // 'location_id' => array_get($location, 'fb_id'), // for carousel, we add location to the carousel container
                        'media_type' => 'CAROUSEL', // carousel only
                        'children' => $containerIds, // carousel only
                        'collaborators' => $collaborators,
                    ]));

                    $containerId = $response->getGraphNode()->getField('id');

                    // get status
                    $statusCode = null;
                    $tries = 1;
                    do {
                        if($tries >= 5){
                            throw new \Exception('Failed to publish: Instagram could not process the carousel');
                        }
                        $statusCode = $instagram->get('/' . $containerId . '?fields=status_code')->getGraphNode()->getField('status_code');
                        ++$tries;
                        if($statusCode === 'IN_PROGRESS'){
                            // FB: "We recommend querying a container's status once per minute, for no more than 5 minutes."
                            sleep(60);
                        }
                    } while($statusCode === 'IN_PROGRESS');

                    // now carousel is ready for publishing

                } catch (\Exception $e) {
                    throw new \Exception('Unable to setup the carousel: ' . $e->getMessage(), 0, $e);
                }
            }

            if($this->user && $this->user->getOption('debug'))
                \Log::info('Post->publishToInstagramApi(): publishing post to IG', [
                    'post_id' => $this->id,
                    'account_id' => $account->account_id,
                    'containerId' => $containerId,
                ]);

            // publish the container in case of single media or in case of carousel, the $containerId is the container to publish
            $postId = $instagram->post('/' . $account->account_id . '/media_publish', [
                'creation_id' => $containerId,
            ])->getGraphNode()->getField('id');

            $postRes = $instagram->get('/' . $postId . '?fields=permalink,id,media_type,media_product_type')->getGraphNode()->asArray();
            $response = new \stdClass();
            $response->url = $postRes['permalink'];
            $response->media_id = $postRes['id'];
            $response->id = $postRes['id'];
            $response->fb_id = $postRes['id'];

        } catch (\Exception $e) {
            // cleanup if any
            if(isset($localPath)){
                if(\Storage::disk('local')->exists($localPath)) {
                    \Storage::disk('local')->delete($localPath);
                }
            }
            throw $e;
        }

        // if we are here, it means post was published successfully

        // post comment if needed
        if($this->getOption('comment')) {

            try {
                $instagram->post('/' . $response->id . '/comments', [
                    'message' => $this->getOption('comment'),
                ]);
            } catch (\Exception $exception) {
                if(!Str::contains($exception->getMessage(), 'too many tags attached')) {
                    //report($exception); // something wrong?
                    $this->setOption('comment_exception', $exception->getTraceAsString());
                }
            }

        }
        $this->external_id = $response->url;
        $this->published_at = Carbon::now();
        $this->result = array_merge((array)$response, [
            '_success' => true,
        ]);
        $this->save();

        // save media id if we have in options
        $this->setOption('media_id', $response->media_id);

        if($this->user && $this->user->getOption('debug'))
            \Log::info('Post->publishToInstagramApi(): post published', [
                'post_id' => $this->id,
                'account_id' => $account->account_id,
                'result' => $this->result,
            ]);
    }

    /**
     * @throws GuzzleException
     */
    private function publishToLinkedinBrand(){
        $this->publishToLinkedinProfile();
    }

    /**
     * @throws GuzzleException
     */
    private function publishToLinkedinOrg(){
        $this->publishToLinkedinProfile();
    }

    /**
     * Publish to LinkedIn profile.
     * @return void
     * @throws GuzzleException
     * @throws \Exception
     */
    private function publishToLinkedinProfile()
    {

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();

        /** @var LinkedinClient $linkedin */
        try {
            $linkedin = $this->account->getApi()->useRest();
        } catch (\Exception $e) {
            if(Str::contains($e->getMessage(), 'invalid_grant')){
                throw new \Exception('Unable to connect to LinkedIn API. The connection is invalid, expired, or revoked. Please re-connect your account');
            }
            throw $e;
        }

        $attachments = $this->getAttachments();
        // check if there are post attachments (media)
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            if ($this->type === 'video' && $n_attachments > 1)
                throw new \Exception('Only one attachment is allowed for video posts');

            if ($n_attachments > 0) {
                foreach ($attachments as $attachment)
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                        throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
            }
        }
        $post_data = [
            'lifecycleState' => 'PUBLISHED',
            'visibility' => 'PUBLIC',
            'commentary' => $this->content ? $this->content : '',
            'distribution' => [
                "feedDistribution" => "MAIN_FEED",
                "targetEntities" => [],
                "thirdPartyDistributionChannels" => []
            ],
        ];

        // add mentions
        // convert @[organization-********:devtesting] to !!!devtesting!!!organization!!!********!!!
        $post_data['commentary'] = preg_replace('/@\[([^\]]+)-([^\]]+):([^\]]+)\]/', '!!!$3!!!$1!!!$2!!!', $post_data['commentary']);

        // escape special characters for linkedin: (, ), {, }, [, ], |, @, *, <, >,# , _, ~
        $post_data['commentary'] = preg_replace_callback(
            '/([\(\)\{\}\[\]\|])|([@*#<>\\\_~])/m',
            function ($matches) {
                return '\\'.$matches[0];
            },
            $post_data['commentary']
        );

        // convert !!!devtesting!!!organization!!!********!!!  to LinkedIn mention element
        $post_data['commentary'] = preg_replace('/!!!([^!]+)!!!([^!]+)!!!([^!]+)!!!/', '@[$1](urn:li:$2:$3)', $post_data['commentary']);
        
        if($this->account->type === 'linkedin.profile')
            $post_data['author'] = 'urn:li:person:' . $this->account->account_id;
        else if($this->account->type === 'linkedin.org' || $this->account->type === 'linkedin.brand')
            $post_data['author'] = 'urn:li:organization:' . $this->account->account_id;

        try {
            if (count($attachments) > 0) {
                      
                foreach ($attachments as $index => $attachment) {

                    $fStream = Storage::readStream($attachment['path']);

                    $localPath = 'linkedin_uploading/' . str_random() . '_' . basename($attachment['path']);

                    // save the media locally
                    Storage::disk('local')->writeStream($localPath, $fStream);

                    
                    if(in_array($attachment['type'], ['mp4', 'm4v', 'mov', 'qt', 'avi',])){
                        $post_type = 'video';
                    } else if(in_array($attachment['type'], ['pdf', 'doc', 'docx', 'ppt', 'pptx'])){
                        $post_type = 'document';
                    } else {
                        $post_type = 'image';
                    }

                    if ($post_type === 'video' && count($attachments) === 1) {
                        try {
                            $this->validateVideo(storage_path('app/' . $localPath));
                        } catch (\Exception $exception) {
                            // cleanup
                            Storage::disk('local')->delete($localPath);
                            // now throw exception
                            throw $exception;
                        }
                    }

                    $urn = null;
                    try {
                        $urn = $linkedin->uploadFile(storage_path('app/' . $localPath), $post_type, $post_data['author']);
                        Storage::disk('local')->delete($localPath); // cleanup
                    } catch (\Exception $e) {
                        Storage::disk('local')->delete($localPath); // cleanup
                        throw $e;
                    }
                    $altText = $this->getOption('media_alt_text.' . $index, $this->getOption('image_alt_text.' . $index));

                    if($post_type === 'document'){
                        $mediaObj = array_filter([
                            'id' => $urn,
                            'title' => $this->getOption('document_title', $attachment['name']),
                        ]);
                    } else {
                        $mediaObj = array_filter([
                            'id' => $urn,
                            'altText' => $altText
                        ]);
                    }

                    if(count($attachments) > 1 && $post_type !== 'video'){
                        //for multiple attachments, using multiImage API
                        $post_data['content']['multiImage']['images'][] = $mediaObj;
                    } else {
                        $post_data['content']['media'] = $mediaObj;
                    }
                }
            } else if($this->getLink()){

                $linkData = [
                    'source' => $this->getLink(),
                    'title' => $this->getOption('link_title'),
                    'description' => $this->getOption('link_description'),
                    'thumbnail' => null,
                ];

                $link_thumbnail = null;
                if($this->getOption('thumbnail')){
                    $link_thumbnail = $this->getOption('thumbnail');
                }else if($this->getOption('link_thumbnail')){
                    $link_thumbnail = $this->getOption('link_thumbnail');
                }

                if(isset($link_thumbnail)){
                    $path = $link_thumbnail['key'];
                    $fStream = Storage::cloud()->readStream($path);
                    $localPath = 'linkedin_uploading/' . str_random() . '_' . basename($path);
                    Storage::disk('local')->writeStream($localPath, $fStream);
                    $linkData['thumbnail'] = $linkedin->uploadFile(storage_path('app/' . $localPath), 'image', $post_data['author']);
                    // delete local file
                    Storage::disk('local')->delete($localPath);
                }

                if(!$linkData['title'] || !$linkData['description'] || !$linkData['thumbnail']){
                    // try to fetch from url
                    try {
                        $meta_tags = extract_meta_tags($this->getLink());

                        $imageUrl = $metaTitle = $metaDescription = null;
                        foreach ($meta_tags as $tag => $value) {

                            if (!$value) {
                                continue;
                            }
                            if (!$metaTitle && in_array($tag, ['title', 'og:title', 'twitter:title'])) {
                                $metaTitle = $value;
                            }
                            if (!$metaDescription && in_array($tag, ['og:description', 'twitter:description', 'description'])) {
                                $metaDescription = $value;
                            }
                            if (!$imageUrl && in_array($tag, ['og:image', 'twitter:image', 'image'])) {
                                /** @var string $image */
                                $image = $value;
                                if (!starts_with($image, 'http')) {
                                    $image = (string)UriResolver::resolve(\GuzzleHttp\Psr7\uri_for($this->getLink()), \GuzzleHttp\Psr7\uri_for($image));
                                }
                                $imageUrl = trim($image);
                            }
                        }

                        if($metaTitle && !$linkData['title']){
                            $linkData['title'] = $metaTitle;
                        }
                        if($metaDescription && !$linkData['description']){
                            $linkData['description'] = $metaDescription;
                        }

                        if ($imageUrl && !$linkData['thumbnail']) {
                            \Log::info('Fetching image from url: ' . $imageUrl);
                            $fName = basename(parse_url($imageUrl, PHP_URL_PATH));
                            $localPath = 'linkedin_uploading/' . str_random() . '_' . $fName;

                            try {
                                // use guzzle to stream file to localPath
                                $guzzle = $linkedin->getClient();
                                $guzzle->request('GET', $imageUrl, [
                                    'sink' => storage_path('app/' . $localPath)
                                ]);
                                $linkData['thumbnail'] = $linkedin->uploadFile(storage_path('app/' . $localPath), 'image', $post_data['author']);
                            } catch (\Exception $e) {
                                \Log::error('Error fetching image from url: ' . $e->getMessage());
                                if($e instanceof \GuzzleHttp\Exception\ClientException){
                                    // log response
                                    $response = $e->getResponse();
                                    $body = $response->getBody()->getContents();
                                    \Log::error('LinkedIn API Error: ' . $body);
                                }
                            }

                            // delete local file
                            if(Storage::disk('local')->exists($localPath)) {
                                Storage::disk('local')->delete($localPath);
                            }
                        }
                    } catch (\Exception $e) {

                        \Log::error('Error fetching link data: ' . $e->getMessage(), [
                            'link' => $this->getLink(),
                        ]);

                        if($e instanceof \GuzzleHttp\Exception\ClientException){
                            // log response
                            $response = $e->getResponse();
                            $body = $response->getBody()->getContents();
                            \Log::error('LinkedIn API Error: ' . $body);
                        }

                        // do nothing
                        report($e);
                    }
                }

                if(!$linkData['title']){
                    // set default title as link url without http/s
                    // 'title' is required field
                    $linkData['title'] = preg_replace('/^https?:\/\//', '', $this->getLink());
                }

                // add link data
                $post_data['content']['article'] = array_filter($linkData);

                if( $this->getOption('trim_link_from_content') === true ){
                    // trim link
                    $post_data['commentary'] = str_replace($this->getLink(), '', $post_data['commentary']);
                }
            }
            $response = $linkedin->request('posts', $post_data, Method::POST);

            $object_id = $response->getHeader('x-restli-id')[0];

            // if comment is set, then add it as first comment to the post
            $comment = $this->getOption('comment'); 
            if(!empty($comment)){

                try {

                    // we need to ensure that the post is published before we can comment on it
                    try {
                        $sleepCount = 0;
                        while (true) {
                            $post = $linkedin->request('posts/' . urlencode($object_id), [], Method::GET);
                            $json = json_decode($post->getBody()->getContents(), true);

                            if (isset($json['lifecycleState']) && $json['lifecycleState'] === 'PUBLISHED') {
                                break;
                            }

                            if ($sleepCount >= 10) {
                                break; // max 10 seconds
                            }

                            sleep(1);
                            $sleepCount++;
                        }
                    } catch (\Exception $e) {
                        // should not happen
                        $this->setOption('post_check_exception', $e->getTraceAsString());
                        // wait for 5s
                        sleep(5);
                    }

                    $commentResponse = $linkedin->request('socialActions/' . urlencode($object_id) . '/comments', [
                        'actor' => $post_data['author'],
                        'object' => $object_id,
                        "message" => [
                            "text" => $comment
                        ],
                    ], Method::POST);
                    // get comment id
                    $comment_id = $commentResponse->getHeader('x-restli-id')[0];
                    $this->setOption('comment_id', $comment_id);
                } catch (\LinkedIn\Exception $e) {
                    $this->setOption('comment_exception', $e->getTraceAsString());
                }
            }
        } catch (\LinkedIn\Exception $e) {
            if($e->getDescription()){
                throw new \Exception($e->getDescription(), 0, $e);
            }
            throw $e;
        }

        if ( $response->getStatusCode() === 201 ) {
            // good
            $this->external_id = array_first($response->getHeader('X-RestLi-Id'));
            $this->published_at = Carbon::now();
            $this->result = array_merge( $response->getHeaders(), [
                '_success' => true,
            ]);
            $this->save();
        } else {
            throw new \Exception('Error (' . $response->getStatusCode() . '): Unknown response from LinkedIn');
        }

    }

    /**
     * Publish to Google My Business.
     * @return void
     * @throws \Exception
     */
    private function publishToGoogleLocation()
    {
        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = $this->account->getVideoAttachmentTypes();

        /** @var \Google_Service_MyBusiness $service */
        $service = $this->account->getApi();

        // check if there are post attachments (media)
        $attachments = $this->getAttachments();
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            foreach ($attachments as $attachment)
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                    throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
        }

        $postObject = [
            'summary' => $this->content,
            'topicType' => 'STANDARD', // standard, event, offer, alert; not supporting alert right now
        ];

        // check if specific post type
        $topicType = $this->getOption('topic_type');
        if($topicType) $topicType = strtoupper($topicType); // default is: STANDARD, empty is also same as STANDARD
        if(in_array($topicType, ['EVENT', 'OFFER',])){
            $postObject['topicType'] = $topicType;

            // no timezone info needed for these by google
            $startTimestamp = Carbon::createFromFormat('d/m/Y h:i a', $this->getOption('event_start'));
            $endTimestamp  = Carbon::createFromFormat('d/m/Y h:i a', $this->getOption('event_end'));
            $postObject['event'] = [
                'title' => $this->getOption('event_title'),
                'schedule' => [
                    'startDate' => [
                        'year' => $startTimestamp->year,
                        'month' => $startTimestamp->month,
                        'day' => $startTimestamp->day
                    ],
                    'startTime' => [
                        'hours' => $startTimestamp->hour,
                        'minutes' => $startTimestamp->minute,
                        'seconds' => $startTimestamp->second,
                        'nanos' => 0
                    ],
                    'endDate' => [
                        'year' => $endTimestamp->year,
                        'month' => $endTimestamp->month,
                        'day' => $endTimestamp->day
                    ],
                    'endTime' => [
                        'hours' => $endTimestamp->hour,
                        'minutes' => $endTimestamp->minute,
                        'seconds' => $endTimestamp->second,
                        'nanos' => 0
                    ],
                ]
            ];

            // only needed for offer
            if($topicType === 'OFFER'){
                $postObject['offer'] = array_filter([
                    'couponCode'=> $this->getOption('offer_coupon'),
                    'redeemOnlineUrl' => $this->getOption('offer_link'),
                    'termsConditions' => $this->getOption('offer_terms')
                ]);
            }
        }

        $callToAction = $this->getOption('call_to_action');
        // call to action can be added in all posts except offers
        if($topicType !== 'OFFER' && in_array($callToAction, ['BOOK', 'ORDER', 'SHOP', 'LEARN_MORE', 'SIGN_UP', 'CALL'])){
            $postObject['callToAction'] = [
                'actionType' => $callToAction
            ];
            if($callToAction !== 'CALL'){ // not needed for call cta

                $ctaUrl = $this->getOption('call_to_action_url');

                // validate url first
                if(!is_active_url($ctaUrl)){
                    throw new \Exception('Invalid call-to-action URL: ' . $ctaUrl);
                }

                $postObject['callToAction']['url'] = $ctaUrl;
            }
        }

        $filesToDelete = []; //for cleanup
        if(!empty($attachments)){
            $postObject['media'] = collect($attachments)->map(function($arr) use (&$filesToDelete){
                $mediaUrl = '';
                try {
                    $fStream = Storage::readStream($arr['path']);
                    $localPath = 'temp/gmb_uploading/' . str_random() . '_' . basename($arr['path']);

                    $img =\Intervention\Image\Facades\Image::make($fStream); //removes exif metadata from image

                    // if image is webp, we need to convert it to jpg because Google My Business does not support webp images
                    if($img->mime() === 'image/webp'){
                        $img->encode('jpg', 90); // convert to jpg
                        $localPath .= '.jpg'; // change extension to jpg
                    }
                    
                    $img->save(storage_path('app/public/' . $localPath));

                    $mediaUrl = \Storage::disk('public')->url($localPath);

                    $filesToDelete[] = $localPath;
                } catch(\Exception $e) {
                    throw new \Exception('Unable to upload media: ' . $e->getMessage());
                }
                return [
                    'sourceUrl' => $mediaUrl,
                    'mediaFormat' => $this->type === 'image' ? 'PHOTO' : 'VIDEO'
                ];
            })->toArray();
        }

        try {
            $response = $service->accounts_locations_localPosts->create($this->account->account_id, new \Google_Service_MyBusiness_LocalPost($postObject));

            $postName = $response->getName();

            // we need to check if the post is processed and is live
            $tries = 0;
            $maxTries = 60; // equals to waiting 1 min
            $postState = $response->getState();

            while($tries < $maxTries && (!$postState || in_array($postState, ['PROCESSING', 'LOCAL_POST_STATE_UNSPECIFIED']))){
                try {
                    $postState = $service->accounts_locations_localPosts->get($postName)->getState();
                    if($postState !== 'PROCESSING'){
                        break;
                    }
                } catch (\Exception $exception){}
                sleep(1);
                ++$tries;
            }

            if($postState === 'REJECTED'){
                // should we delete the post?
                // we can if needed
                // throw error: post was rejected by google
                throw new \Exception('This post was rejected due to content policy violation.');
            }

            // good
            $this->external_id = $response->getName();
            $this->published_at = Carbon::now();
            $this->result = array_merge((array) $response, [
                '_success' => true,
            ]);
            $this->save();

            // save media to gallery if needed
            if(!empty($attachments) && $this->getOption('save_media_to_gallery')){
                foreach($postObject['media'] as $media){
                    $media['locationAssociation'] = [
                        "category" => 'CATEGORY_UNSPECIFIED',
                    ];
                    try {
                        $service->accounts_locations_media->create($this->account->account_id, new \Google_Service_MyBusiness_MediaItem($media));
                    } catch (\Exception $exception){
                        // save in option just for record
                        $this->setOption('media_gallery_exception', $exception->getMessage());
                    }
                }
            }

        } catch (\Exception $e) {

            if(Str::contains($e->getMessage(), 'PERMISSION_DENIED')){
                // error happens when we don't have permission
                // also happens when the location is a chain location
                throw new \Exception('Permission error: Location verification required, insufficient permissions, or the location belongs to a business chain');
            }

            if($e instanceof \Google_Service_Exception){
                // google api error
                $json = $e->getMessage(); // the message is a json string
                $errResponse = @json_decode($json);
                if($errResponse && isset($errResponse->error->message) && isset($errResponse->error->status) && isset($errResponse->error->code)){

                    $helpfulError = $errResponse->error->message;

                    // if we have details[0]->errorDetails[0]->message, then use it
                    if(isset($errResponse->error->details[0]->errorDetails[0]->message)){
                        $helpfulError = ( isset($errResponse->error->details[0]->errorDetails[0]->field) ? '(' . $errResponse->error->details[0]->errorDetails[0]->field . ') ' : '' ) . $errResponse->error->details[0]->errorDetails[0]->message;
                    }

                    // throw error with clear message
                    throw new \Exception($errResponse->error->status . ': ' . $helpfulError);
                }
            }

            // only report if needed
            if( !Str::contains(strtolower($e->getMessage()), ['internal error', 'rejected']) ){
                report($e);
            }

            throw $e;
        } finally {
            //clean
            if(!empty($filesToDelete)){
                try {
                    foreach($filesToDelete as $filePath){
                        \Storage::disk('public')->delete($filePath);
                    }
                } catch (\Exception $exception){}
            }
        }
    }

    /**
     * Publish to YouTube.
     * @return void
     * @throws \Exception
     */
    private function publishToGoogleYoutube()
    {
        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = $this->account->getVideoAttachmentTypes();

        /** @var \Google\Service\YouTube $service */
        $service = $this->account->getApi();

        // check if there are post attachments (media)
        $attachments = $this->getAttachments();
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            foreach ($attachments as $attachment)
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                    throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
        } else {
            throw new \Exception('No media attached');
        }

        $content = '';
        if(!empty($this->content)){
            $content = trim($this->content);
        }

        $title = $this->getOption('video_title', Str::limit($content, 70));

        if($this->getOption('post_as_short') && !Str::contains(strtolower($title), '#shorts') && !Str::contains(strtolower($content), '#shorts')){
            $title = Str::limit($title, 70) . ' #Shorts';
        }

        $privacyStatus = $this->getOption('privacy_status', 'public');

        if(!in_array($privacyStatus, ['public', 'private', 'unlisted'])){
            // some posts have PUBLIC_... something, possibly because of some frontend bug
            // as privacy_status is also used for tiktok
            $privacyStatus = 'public';
        }

        try {

            $response = null;

            // upload media files
            foreach($attachments as $attachment) {

                $fStream = \Storage::readStream($attachment['path']);

                $localPath = 'youtube_uploading/' . str_random() . '_' . basename($attachment['path']);

                // save the media locally
                \Storage::disk('local')->writeStream($localPath, $fStream);

                $this->validateVideo(storage_path('app/' . $localPath));

                try {

                    $videoPath = storage_path('app/' . $localPath);

                    $snippet = new \Google\Service\YouTube\VideoSnippet();
                    $snippet->setTitle($title); // required
                    $snippet->setDescription($content);

                    $videoTags = explode(',', $this->getOption('video_tags', ''));
                    $videoTags = array_map('trim', $videoTags);
                    $videoTags = array_filter($videoTags);

                    $snippet->setTags($videoTags);

                    $snippet->setCategoryId($this->getOption('category_id', 22)); // required

                    $status = new \Google\Service\YouTube\VideoStatus();
                    $status->setPrivacyStatus($privacyStatus);

                    if($this->getOption('made_for_kids')){
                        $status->setSelfDeclaredMadeForKids($this->getOption('made_for_kids', false));
                    }

                    $video = new \Google\Service\YouTube\Video();
                    $video->setSnippet($snippet);
                    $video->setStatus($status);

                    // Specify the size of each chunk of data, in bytes. Set a higher value for
                    // reliable connection as fewer chunks lead to faster uploads. Set a lower
                    // value for better recovery on less reliable connections.
                    $chunkSizeBytes = 50 * 1024 * 1024;

                    // Setting defer flag to true tells the client to return a request which can be called
                    // with ->execute(); instead of making the API call immediately.
                    $service->getClient()->setDefer(true);

                    $request = $service->videos->insert(
                        'snippet,status',
                        $video
                    );

                    // Create a MediaFileUpload object for resumable uploads.
                    $media = new \Google\Http\MediaFileUpload(
                        $service->getClient(),
                        $request,
                        'video/*',
                        null,
                        true,
                        $chunkSizeBytes
                    );
                    $media->setFileSize(filesize($videoPath));

                    // Read the media file and upload it chunk by chunk.
                    $status = false;
                    $handle = \GuzzleHttp\Psr7\Utils::tryFopen($videoPath, 'rb');
                    while (!$status && !feof($handle)) {
                        $chunk = fread($handle, $chunkSizeBytes);
                        $status = $media->nextChunk($chunk);
                    }

                    fclose($handle);

                    // If you want to make other calls after the file upload, set setDefer back to false
                    $service->getClient()->setDefer(false);

                    // $status['snippet']['title'],
                    // $status['id']);

                    // cleanup
                    try {
                        \Storage::disk('local')->delete($localPath);
                    } catch (\Exception $exception){}

                    $response = $status;

                } catch (\Exception $e) {
                    throw new \Exception('Unable to upload the media. Please try again later', 0, $e);
                }

                break; // only one media is allowed
            }

            // good
            $this->external_id = $response['id'];
            $this->published_at = Carbon::now();
            $this->result = array_merge((array) $response, [
                '_success' => true,
            ]);
            $this->save();

            if($this->getOption('thumbnail') && !$this->getOption('post_as_short')){
                $path = $this->getOption('thumbnail')['key'];

                $posterStream = Storage::cloud()->readStream($path);
                $posterLocalPath = 'temp/youtube_uploading/' . str_random() . '_' . basename($path);
                
                \Storage::disk('local')->writeStream($posterLocalPath, $posterStream);

                try {
                    $imageContent = file_get_contents(storage_path('app/' . $posterLocalPath));
                    $mimeType = mime_content_type(storage_path('app/' . $posterLocalPath));
                    
                    $res = $service->thumbnails->set(
                        $response['id'],
                        [
                            'data' => $imageContent, 
                            'mimeType' => $mimeType,
                            'uploadType' => 'media'
                        ]
                    );

                    $this->result =array_merge((array) $this->result, [
                        'thumbnail' => (array) $res,
                    ]);
                    $this->save();

                    \Storage::disk('local')->delete($posterLocalPath);
                } catch (\Exception $e) {
                    $this->result =array_merge((array) $this->result, [
                        'thumbnail_error' => $e->getMessage(),
                    ]);
                    
                    \Storage::disk('local')->delete($posterLocalPath);
                }
                
            }

        } catch (\Exception $e) {

            // cleanup if any
            if(isset($localPath)){
                if(\Storage::disk('local')->exists($localPath)) {
                    \Storage::disk('local')->delete($localPath);
                }
            }

            if($e instanceof \Google_Service_Exception){
                // google api error
                $errResponse = null;
                try {
                    $json = $e->getMessage(); // the message is a json string
                    $errResponse = @json_decode($json);
                } catch (\Exception $exception){}

                if($errResponse && isset($errResponse->error->message) && isset($errResponse->error->status) && isset($errResponse->error->code)){
                    // throw error with clear message
                    throw new \Exception($errResponse->error->code . ' ' . $errResponse->error->status . ': ' . $errResponse->error->message, 0, $e);
                }
            }

            // only report if needed
            if( !Str::contains(strtolower($e->getMessage()), ['internal error', 'video']) ){
                report($e);
            }

            throw $e;
        }
    }

    /**
     * Publish to Reddit.
     * @return void
     * @throws \Exception
     */
    private function publishToRedditSubreddit()
    {

        // don't allow more than 30 posts per 1 hr
        $postsCount = Post::whereAccountId($this->account_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subHours(1))
            ->count();

        $maxPostsPerHr = $this->account->user->getOption('max_posts_per_hr', 30);

        if($postsCount >= $maxPostsPerHr){
            // stop... can be spam
            throw new \Exception('Potentially spam: Please slow down. Please contact us if you think this should not happen.');
        }

        $token = json_decode($this->account->token, true);

        /** @var Client $reddit */
        $reddit = $this->account->getApi();

        $attachments = $this->getAttachments();

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = ['mp4'];

        // validate attachments
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            // validate media types
            foreach ($attachments as $attachment) {
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                    throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                }
            }

            // multiple attachments should be static images (no gif / video)
            if ($n_attachments > 1) {
                foreach($attachments as $attachment) {
                    if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                        throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per tweet');
                    }
                }
            }

        }

        $medias = [];

        $uploadMedia = function($attachment) use($reddit){

            $fStream = Storage::readStream($attachment['path']);
            $localPath = 'reddit_uploading/' . str_random() . '_' . basename($attachment['path']);
            // save the media locally
            Storage::disk('local')->writeStream($localPath, $fStream);

            if(Str::contains($attachment['mime'], 'video')) {
                try {
                    $this->validateVideo(storage_path('app/' . $localPath));
                } catch (\Exception $exception) {
                    // cleanup
                    Storage::disk('local')->delete($localPath);
                    // now throw exception
                    throw $exception;
                }
            } else if(Str::contains($attachment['mime'], 'gif')){
                // is a gif; covert to silent mp4

                // convert the gif to mp4 using ffmpeg
                $ffmpeg = \FFMpeg\FFMpeg::create([
                    'threads' => 16, // half of all cpus so ffmpeg doesn't use 100% cpu
                ]);
                $video = $ffmpeg->open(storage_path('app/' . $localPath));

                // we need mp4 format with no audio
                $video->save(new \FFMpeg\Format\Video\X264('aac'), storage_path('app/' . $localPath) . '.mp4');

                // delete the original gif
                Storage::disk('local')->delete($localPath);

                // update localPath
                $localPath .= '.mp4';

                // update $attachment
                $attachment['path'] .= '.mp4';
                $attachment['mime'] = 'video/mp4';
                $attachment['type'] = 'mp4';
            }

            // first send request to get upload url
            try {
                $uploadRes = $reddit->post('/api/media/asset.json?raw_json=1', [
                    'form_params' => [
                        'filepath' => basename($attachment['path']),
                        'mimetype' => $attachment['mime'],
                    ],
                ]);
            } catch (\Exception $exception) {
                if($exception instanceof \GuzzleHttp\Exception\ClientException){
                    $response = $exception->getResponse();
                    if($response && $response->getStatusCode() === 429){
                        throw new \Exception('Reddit rate limit exceeded. Please try later.', 0, $exception);
                    }
                }

                // cleanup
                Storage::disk('local')->delete($localPath);

                throw $exception;
            }
            $uploadResJson = json_decode($uploadRes->getBody(), true);

            if(!isset($uploadResJson['asset']['asset_id']))
                throw new \Exception('Invalid response from reddit: ' . json_encode($uploadResJson));

            $uploadUrl = $uploadResJson['args']['action'];
            $uploadFields = $uploadResJson['args']['fields'];

            $postMultiPart = [];

            foreach($uploadFields as $field){
                $postMultiPart[] = [
                    'name' => $field['name'],
                    'contents' => $field['value'],
                ];
            }

            // important: the file must be the last part, otherwise aws will throw error
            $postMultiPart[] = [
                'name' => 'file',
                'contents' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
                'filename' => basename($attachment['path']),
            ];

            // now upload the file
            $uploadClient = guzzle_client(); // because we can't send any auth headers
            try {
                $uploadClient->post($uploadUrl, [
                    'multipart' => $postMultiPart,
                ]);
            } catch (\Exception $exception) {
                // cleanup
                Storage::disk('local')->delete($localPath);
                throw $exception;
            }

            // find $uploadFields[] with name = key
            $keyField = collect($uploadFields)->where('name', 'key')->first();
            if(!$keyField)
                throw new \Exception('Invalid response from reddit: ' . json_encode($uploadResJson));

            $key = $keyField['value'];

            // the media is uploaded, get relevant info
            return [
                'asset_id' => $uploadResJson['asset']['asset_id'],
                'link' => $uploadUrl . '/' . $key,
                'websocket_url' => $uploadResJson['asset']['websocket_url'],
                'mime' => $attachment['mime'],
            ];
        };

        $poster_url = ''; // for video only

        foreach ($attachments as $attachment) {

            // upload to reddit
            try {
                $medias[] = $uploadMedia($attachment);

                // sleep for 1 second to avoid reddit rate limit
                sleep(1);

                // if there is a poster url, upload it too
                if(isset($attachment['poster_path'])){
                    // upload poster
                    $posterUpload = $uploadMedia([
                        'path' => $attachment['poster_path'],
                        'mime' => 'image/jpeg',
                        'type' => 'image',
                    ]);
                    // set poster url
                    $poster_url = $posterUpload['link'];
                    sleep(1);
                } else if(Str::contains($attachment['mime'], 'gif')){
                    // if gif, use the gif itself as poster
                    $tempFile = tempnam(sys_get_temp_dir(), 'gif');
                    file_put_contents($tempFile, \Storage::readStream($attachment['path']));

                    $image = \Intervention\Image\Facades\Image::make($tempFile);
                    // save as jpg to the same path
                    $image->save($tempFile, null, 'jpg');

                    $posterPath = \Storage::putFile('attachments', new \Illuminate\Http\File($tempFile));
                    $posterUpload = $uploadMedia([
                        'path' => $posterPath,
                        'mime' => 'image/jpg',
                        'type' => 'jpg',
                    ]);
                    $poster_url = $posterUpload['link'];

                    // cleanup
                    unlink($tempFile);
                    \Storage::delete($posterPath);

                    sleep(1);
                }

            } catch (\Exception $exception) {

                // if guzzle response is available, use it
                if($exception instanceof \GuzzleHttp\Exception\ClientException){
                    $response = $exception->getResponse();
                    $json = $response->getBody()->getContents();
                    \Log::info('Reddit upload error: ' . $json);
                    \Log::info('Headers: ' . json_encode($response->getHeaders()));
                }

                // now throw exception
                throw $exception;
            }
        }

        // we have all attachments uploaded here (in $medias)
        $kind = 'self'; // default kind

        $title = $this->getOption('title');
        if(!$title){
            // get title from $this->content
            $title = $this->content;
        }
        // make sure title is max 300 chars
        $title = Str::limit($title, 300);

        // post body text (can be markdown)
        $text = $this->content ?? '';

        $link = $this->getOption('link');
        // if all we have is a link in $this->content then we should post as link
        if(!$link && $this->getLink() && trim($this->content) === $this->getLink()){
            $link = $this->getLink();
        }

        if(count($attachments) > 0){
            $mime = $attachments[0]['mime'];
            if(Str::contains($mime, 'gif')){
                $kind = 'videogif';
            } else if(Str::contains($mime, 'video')){
                $kind = 'video';
            } else {
                $kind = 'image';
            }
        } else if($link){
            $kind = 'link';
            $text = '';
        }

        $data = array_filter([
            'api_type' => 'json',
            'extension' => 'json',
            'kind' => $kind,
            'resubmit' => true,
            'sendreplies' => (bool) $this->getOption('send_replies', true),
            'nsfw' => (bool) $this->getOption('is_nsfw'),
            'spoiler' => (bool) $this->getOption('is_spoiler'),
            'title' => $title,
            'text' => $text,
        ]);

        // add flair if needed
        $flairId = $this->getOption('flair_id');
        if($flairId){
            $flairs = $this->account->getExtraData('flairs');
            if($flairs){
                // get flair where id = $flairId
                $flair = collect($flairs)->where('id', $flairId)->first();
                if($flair){
                    // add flair
                    $data['flair_id'] = $flair['id'];
                    $data['flair_text'] = $flair['text'];
                }
            }
        }

        if($kind !== 'self' && $kind !== 'link'){

            if(!isset($medias[0]))
                throw new \Exception('Invalid media attachment: Not found after upload');

            // it's an image/video/gif
            $data['url'] = $medias[0]['link'];

            if($kind === 'video' || $kind === 'videogif'){
                // we need a video_poster_url too
                $data['video_poster_url'] = $poster_url;
            }
        } else if($kind === 'link'){
            $data['url'] = $link;
        }

        $sr = $token['subreddit']; // subreddit name or u_username for profile
        $data['sr'] = $sr;

        // before posting, we have to connect to the websocket; if we have a media
        $websocket = null;
        if(isset($medias[0]['websocket_url'])){
            $websocket = new \WebSocket\Client($medias[0]['websocket_url'], [
                'timeout' => 60,
            ]);
            // ping
            $websocket->ping();
        }

        $submitRes = $reddit->post('/api/submit', [
            'form_params' => $data,
            'retry_enabled' => false, // no retry for this request; if we get rate-limited, we simply fail?
        ]);

        if ($submitRes->getStatusCode() !== 200) {
            \Log::error("Reddit API error: " . json_encode($submitRes->getBody()) );
            throw new \Exception('HTTP error (' . $submitRes->getStatusCode() . ') while posting to Reddit');
        }

        // get json
        $response = json_decode($submitRes->getBody(), true);

        // check for any errors
        if(isset($response['json']['errors']) && count($response['json']['errors']) > 0){
            $errors = $response['json']['errors'];
            throw new \Exception('Reddit API error: ' . json_encode($errors));
        }

        $permalink = '';
        $postId = '';
        try {
            if (!isset($response['json']['data']['url'])) {
                // possible that user added a media file
                // we don't receive url in this case
                // we can get the url by connecting to the websocket_url though
                $websocketUrl = $response['json']['data']['websocket_url'];
                $tries = 0;
                while (true) {
                    try {
                        $dataFromWebsocket = $websocket->receive();
                        \Log::info('Data from websocket: ' . $dataFromWebsocket);

                        $dataFromWebsocket = json_decode($dataFromWebsocket, true);

                        if (isset($dataFromWebsocket['payload']['redirect'])) {
                            $permalink = $dataFromWebsocket['payload']['redirect'];
                            $permalink = Str::replaceLast('.json', '', $permalink);

                            // extract id from permalink
                            $permalinkParts = explode('/comments/', $permalink);
                            if (isset($permalinkParts[1])) {
                                $postId = $permalinkParts[1];
                                $postId = explode('/', $postId)[0];
                            } else {
                                $postId = $websocketUrl;
                            }
                            break;
                        }

                        $tries++;

                        if ($tries > 10) {
                            break;
                        }

                    } catch (\Exception $e) {
                        report($e);
                        break;
                    }
                }
                $websocket->close();

                if (!$permalink) {
                    $permalink = 'https://reddit.com/r/' . $sr;
                    $postId = $websocketUrl;
                }

            } else {
                $permalink = $response['json']['data']['url'];
                $permalink = Str::replaceLast('.json', '', $permalink); // strip .json from end

                $postId = $response['json']['data']['id'];
            }
        } catch (\Exception $exception){
            // ignore
            // set permalink to subreddit
            if(!$permalink)
                $permalink = 'https://reddit.com/r/' . $sr;
        }

        //send comment;
        if($this->getOption('comment')){
            try {
                $submitRes = $reddit->post('/api/comment', [
                    'form_params' => [
                        'thing_id' => 't3_' . $postId,
                        'text' => $this->getOption('comment')
                    ],
                ]);
            } catch (\Exception $e) {
                $this->setOption('comment_exception', $e->getTraceAsString());
            }
        }
        
        // good
        $this->external_id = $postId;
        $this->published_at = Carbon::now();
        $this->result = array_merge((array) $response, [
            '_success' => true,
            'permalink' => $permalink,
        ]);
        $this->save();
    }

    /**
     * @throws \Exception
     */
    private function publishToRedditProfile(){
        $this->publishToRedditSubreddit();
    }

    /**
     * Publish to Mastodon.
     * @return void
     * @throws \Exception
     */
    private function publishToMastodonProfile()
    {
        /** @var MastodonClient $m */
        $m = $this->account->getApi();

        $attachments = $this->getAttachments();

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = ['mp4'];

        // validate attachments
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            // validate media types
            foreach ($attachments as $attachment) {
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                    throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                }
            }

            // multiple attachments should be static images (no gif / video)
            if ($n_attachments > 1) {
                foreach($attachments as $attachment) {
                    if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                        throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per tweet');
                    }
                }
            }

        }

        $media_ids = [];

        // upload medias to mastodon
        foreach ($attachments as $index => $attachment) {
            $fStream = Storage::readStream($attachment['path']);
            $localPath = 'mastodon_uploading/' . str_random() . '_' . basename($attachment['path']);
            // save the media locally
            Storage::disk('local')->writeStream($localPath, $fStream);

            if($this->type === 'video') {
                try {
                    $this->validateVideo(storage_path('app/' . $localPath));
                } catch (\Exception $exception) {
                    // cleanup
                    Storage::disk('local')->delete($localPath);
                    // now throw exception
                    throw $exception;
                }
            }
            $altText = $this->getOption('media_alt_text.' . $index, $this->getOption('image_alt_text.' . $index));

            $postMultiPart = [
                [
                    'name' => 'file',
                    'contents' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
                    'filename' => basename($attachment['path']),
                ],
            ];

            if($altText){
                $postMultiPart[] = [
                    'name' => 'description',
                    'contents' => $altText,
                ];
            }

            // upload to mastodon
            try {

                // just for the upload, we need to use v2
                $m->apiVersion('v2');

                $res = $m->call('POST', '/media', [
                    'multipart' => $postMultiPart,
                ]);

                // back to v1
                $m->apiVersion('v1');

                if(!isset($res['id']))
                    throw new \Exception('Invalid response from mastodon: ' . json_encode($res));

                // see if we need to wait for the file to be ready
                $tries = 0;
                while($res['url'] === null){
                    if($tries > 20)
                        throw new \Exception('Media upload failed: ' . json_encode($res));
                    sleep(1);
                    $res = $m->get('/media/' . $res['id']);
                    $tries++;
                }

                $media_ids[] = $res['id'];
            } catch (\Exception $exception) {
                if ($exception instanceof \GuzzleHttp\Exception\ServerException){
                    if($exception->getResponse()->getStatusCode() === 500){
                        throw new \Exception('Mastodon server error: ' . $exception->getMessage());
                    }
                } else {
                    report($exception);
                }
                // now throw exception
                throw $exception;
            } finally {
                // cleanup
                Storage::disk('local')->delete($localPath);
            }
        }

        $data = array_filter([
            'status' => $this->content,
            'sensitive' => $this->getOption('mark_sensitive'), // to mark the media as sensitive
            'spoiler_text' => $this->getOption('spoiler'), // to add spoiler to the whole post; this also marks the media as sensitive
            'media_ids' => $media_ids,
        ]);

        $response = $m->call('POST', "/statuses", [
            'json' => $data,
        ]);

        if ($m->getResponse()->getStatusCode() == 200) {
            // good
            $this->external_id = $response['id'];
            $this->published_at = Carbon::now();
            $this->result = array_merge((array)$response, [
                '_success' => true,
            ]);
            $this->save();
        } else {
            $r = $m->getResponse();
            \Log::error("Mastodon API error: \n" . $r ? json_encode($r->getBody() . '') : '');
            throw new \Exception('HTTP error (' . $r ? $r->getStatusCode() : 'unknown'. ') while posting to Mastodon');
        }

        // now post threaded replies if needed
        if(isset($this->options['threaded_replies']) && !empty($this->options['threaded_replies'])){
            $lastPostId = $response['id'];
            $exceptions = [];
            $threadPostIds = [];
            $threaded_replies = $this->options['threaded_replies'];

            if(!is_array($threaded_replies)) {
                // shouldn't happen
                $threaded_replies = (array) @json_decode($this->options['threaded_replies'], true);
            }

            foreach($threaded_replies as $replyIndex => $reply){
                $media_ids = [];

                if(is_array($reply)){
                    if(!isset($reply['media'])) $reply['media'] = [];

                    $n_attachments = count($reply['media']);

                    if ($n_attachments > $this->account->getMaxAttachments())
                        throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

                    // validate media types
                    foreach ($reply['media'] as $attachment) {
                        if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                            throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                        }
                    }

                    // multiple attachments should be static images (no gif / video)
                    if ($n_attachments > 1) {
                        foreach($reply['media'] as $attachment) {
                            if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                                throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per post');
                            }
                        }
                    }

                    foreach ($reply['media'] as $file){
                        $path = $file['key'];
                        $fStream = Storage::cloud()->readStream($path);
                        $localPath = 'mastodon_uploading/' . str_random() . '_' . basename($path);
                        // save the media locally
                        Storage::disk('local')->writeStream($localPath, $fStream);

                        if($this->type === 'video') {
                            try {
                                $this->validateVideo(storage_path('app/' . $localPath));
                            } catch (\Exception $exception) {
                                // cleanup
                                Storage::disk('local')->delete($localPath);
                                // now throw exception
                                throw $exception;
                            }
                        }

                        $postMultiPart = [
                            [
                                'name' => 'file',
                                'contents' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
                                'filename' => basename($path),
                            ],
                        ];

                        // upload to mastodon
                        try {

                            // just for the upload, we need to use v2
                            $m->apiVersion('v2');
                            $res = $m->call('POST', '/media', [
                                'multipart' => $postMultiPart,
                            ]);

                            // back to v1
                            $m->apiVersion('v1');

                            if(!isset($res['id']))
                                throw new \Exception('Invalid response from mastodon: ' . json_encode($res));

                            // see if we need to wait for the file to be ready
                            $tries = 0;
                            while($res['url'] === null){
                                if($tries > 20)
                                    throw new \Exception('Media upload failed: ' . json_encode($res));
                                sleep(1);
                                $res = $m->get('/media/' . $res['id']);
                                $tries++;
                            }

                            $media_ids[] = $res['id'];
                        } catch (\Exception $exception) {
                            $exceptions[$replyIndex] = $exception;
                            if ($exception instanceof \GuzzleHttp\Exception\ServerException){
                                if($exception->getResponse()->getStatusCode() === 500){
                                    throw new \Exception('Mastodon server error: ' . $exception->getMessage());
                                }
                            } else {
                                report($exception);
                            }
                            // now throw exception
                            throw $exception;
                        } finally {
                            // cleanup
                            Storage::disk('local')->delete($localPath);
                        }
                    }
                }

                $content = '';
                if(is_array($reply)){
                    if(!isset($reply['text'])){
                        $reply['text'] = '';
                    }
                    $content = $reply['text'];
                } else {
                    $content = $reply;
                }

                $data = array_filter([
                    'status' => $content,
                    'media_ids' => $media_ids,
                    'in_reply_to_id' => $lastPostId
                ]);
                $tries = 0;
                while($tries < 10){
                    try {
                        $response = $m->call('POST', "/statuses", [
                            'json' => $data,
                        ]);
                        if ($m->getResponse()->getStatusCode() == 200) {
                            $threadPostIds[] = $response['id'];
                            $lastPostId = $response['id'];
                            break;
                        }
                    } catch(\Exception $e){
                        $tries ++;
                        $exceptions[$replyIndex] = $e;
                    }   
                }
                
                sleep(1); // just to make sure we don't look spam
            }

            // if we are here, all replies are processed
            $result = $this->result;
            $result['threaded_replies'] = [
                'threadPostIds' => $threadPostIds,
                'exceptions' => collect($exceptions)->map(function($e){
                    /** @var \Exception $e */
                    return $e->getTraceAsString();
                })->toArray(),
            ];
            $this->result = $result;
            $this->save();
        }

    }

    /**
     * Publish video to TikTok
     * @return void
     * @throws \Exception
     */
    private function publishToTiktokProfile()
    {
        $account = $this->account;

        $ALLOWED_MEDIA_TYPES = $account->getAttachmentTypes();

        $attachments = $this->getAttachments();
        // check if there are post attachments (media)
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            if ($n_attachments > 0) { // all attachments should be static images (no gif / video)
                foreach ($attachments as $attachment)
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                        throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
            }
        } else {
            throw new \Exception('No media attached');
        }

        if($this->type === 'image' && $this->getOption('title')){
            // to handle emojis in title
            $utf16 = iconv('UTF-8', 'UTF-16LE', $this->getOption('title'));
            $title_length = strlen($utf16) / 2;

            if($title_length > 90){
                throw new \Exception('Title should be less than 90 characters');
            }
        }

        /** @var Client $client */
        $client = $account->getApi();

        $content = null;
        if(!empty($this->content)){
            $content = trim($this->content);
        }

        $privacyStatus = $this->getOption('privacy_status', 'PUBLIC_TO_EVERYONE');

        if(in_array(strtolower($privacyStatus), ['public', 'open', 'everyone'])){
            // some posts have "PUBLIC" as status as we didn't validate the input (API user)
            $privacyStatus = 'PUBLIC_TO_EVERYONE';
        }

        $publish_id = null;
        $filesToDelete = [];
        try {

            $post_info = array_filter([
                "title" => $this->type === 'image' ? $this->getOption('title', '') : $content,   
                "description" => $this->type === 'image' ? $content : '',
                "privacy_level" => $privacyStatus,

                // deprecated options used as fallback
                "disable_duet" => !$this->getOption('allow_duet', $this->getOption('duet', false)),
                "disable_comment" => !$this->getOption('allow_comment', $this->getOption('comment', false)),
                "disable_stitch" => !$this->getOption('allow_stitch', $this->getOption('stitch', false)),

                // video cover is added until the first 0.2s; first frame is set if not provided which is also fine for us
                // "video_cover_timestamp_ms" => 200,
                'brand_content_toggle' => $this->getOption('own_brand'), //User promoting his/her own business.
                'brand_organic_toggle' => $this->getOption('branded_content'),  //User is in a paid partnership with a brand.

                'auto_add_music' => $this->type === 'image' ? $this->getOption('auto_add_music') : false,
            ]);

            if($this->type === 'image'){
                
                $image_urls = [];
                $width = $height = null;
                foreach($attachments as $attachment) {
                    $fStream = \Storage::readStream($attachment['path']);
                        
                    $localPath = 'tiktok_uploading/' . str_random() . '_' . explode('.', basename($attachment['path']))[0] . '.jpg';

                    $img = \Intervention\Image\Facades\Image::make($fStream);
                    
                    $width = $img->width();
                    $height = $img->height();

                    if($width > 1920){
                        $img->resize(1920, null, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    if($height > 1080){
                        $img->resize(null, 1080, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $img->save(storage_path('app/public/' . $localPath));

                    $image_urls[] = \Storage::disk('public')->url($localPath);
                    
                    $filesToDelete[] = $localPath;
                }

                $jsonPostData = [
                    "post_info" => $post_info,
                    'source_info' => [
                        'source' => 'PULL_FROM_URL',
                        'photo_cover_index' => 0,
                        'photo_images' => $image_urls
                    ],
                    'post_mode'=> 'DIRECT_POST',
                    'media_type'=> 'PHOTO'
                ];

                try{
                    $initRes = $client->post('/v2/post/publish/content/init/', [
                        'json' => $jsonPostData
                    ]);
    
                    $response = json_decode($initRes->getBody()->getContents(), true);
    
                    $json = $response['data'];
                    $publish_id = $json['publish_id'];
                }catch(\Exception $e){

                    \Log::warning('TikTok post that generated error: ' . json_encode($jsonPostData));

                    if(Str::contains(strtolower($e->getMessage()), ['user did not authorize the scope', 'invalid request parameters'])){
                        throw new \Exception('Please re-connect your TikTok account and grant full permissions to SocialBu.');
                    }

                    if($e instanceof ClientException && $e->getResponse()){
                        $res = @json_decode($e->getResponse()->getBody()->getContents(), true);
                        if($res && isset($res['error'])){

                            $code = $res['error']['code'];

                            $errMsg = $code;
                            if($code === 'spam_risk_too_many_pending_share'){
                                $errMsg = 'Too many pending shares. Please clear any pending shares from TikTok app and try again later';
                            } else if ($code === 'spam_risk_too_many_posts'){
                                $errMsg = 'Too many posts. Please try again later.';
                            } else {
                                \Log::info($width . 'x' . $height);
                                report(new \Exception('TikTok error: ' . json_encode($res)));
                            }

                            throw new \Exception('Unable to upload the media: ' . $errMsg, 0, $errMsg === $code ? $e : null);
                        }
                    }
                    throw new \Exception('Unable to upload the media. Please try again later', 0, $e);
                }
            }else {
                // upload media files
                foreach($attachments as $attachment) {
    
                    $fStream = \Storage::readStream($attachment['path']);
    
                    $localPath = 'tiktok_uploading/' . str_random() . '_' . basename($attachment['path']);
    
                    // save the media locally
                    \Storage::disk('local')->writeStream($localPath, $fStream);
    
                    $this->validateVideo(storage_path('app/' . $localPath));
    
                    // for cleanup
                    $filesToDelete[] = $localPath;
                
                    try {
    
                        $size = filesize(storage_path('app/' . $localPath));

                        // we need to do chunking for videos as recommended by TikTok
                        // https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide?enter_method=left_navigation
                        $minChunkSize = 5 * 1000 * 1000;  // 5 MB
                        $maxChunkSize = 32 * 1000 * 1000; // 64 MB is TikTok limit, but we use less than that, so we can increase the final/only chunk size if needed
                        $maxChunks = 1000;
                        $maxLastChunkSize = 128 * 1000 * 1000; // 128MB

                        if($size > $maxChunkSize){
                            $chunkSize = $maxChunkSize;
                            $totalChunks = floor($size / $chunkSize);

                            // Calculate last chunk size
                            $lastChunkSize = $size - ($totalChunks * $chunkSize);

                            // Adjust total chunks if last chunk is too large
                            if ($lastChunkSize > $maxLastChunkSize) {
                                $totalChunks++;
                                $lastChunkSize = $size - ($totalChunks * $chunkSize);
                            }

                            // if last chunk is more than 5 mb, add one more chunk
                            if($lastChunkSize > $minChunkSize && $lastChunkSize < $maxChunkSize && $totalChunks > 1){

                                // so, we can increase number of chunks, BUT, we should only do that if the last chunk is > maxLastChunkSize
                                if($lastChunkSize > $maxLastChunkSize){
                                    $totalChunks++;
                                } else {
                                    // no need, because last chunk is not too large enough, so we simply will send it as part of the last chunk
                                }

                                // adjust last chunk size
                                $lastChunkSize = $size - ($totalChunks * $chunkSize);
                            }
                        } else {
                            $chunkSize = $size;
                            $totalChunks = 1;
                        }

                        // Enforce chunk count limits
                        $totalChunks = max(1, min($maxChunks, $totalChunks));

                        if ($totalChunks === 1){ // if only one chunk, chunk size will be the size of the file
                            $chunkSize = $size;
                        }

                        try {

                            $jsonData = [
                                'source_info' => [
                                    'source' => 'FILE_UPLOAD',
                                    'video_size' => $size,
                                    'chunk_size' => $chunkSize,
                                    'total_chunk_count' => $totalChunks,
                                ],
                                'post_info' => $post_info,
                            ];

                            \Log::info(json_encode($jsonData));

                            $initRes = $client->post('v2/post/publish/video/init/', [
                                'json' => $jsonData
                            ]);

                            $response = json_decode($initRes->getBody()->getContents(), true);

                            $json = $response['data'];
                            $publish_id = $json['publish_id'];
                            $upload_url = $json['upload_url'];

                            // get stream
                            $handle = \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'rb');

                            // now upload the video in chunks
                            for ($i = 0; $i < $totalChunks; $i++) {
                                $start = $i * $chunkSize;
                                $end = $start + $chunkSize - 1;
                                if ($end >= $size) {
                                    $end = $size - 1;
                                }

                                if ($i + 1 == $totalChunks) { // if this is last chunk, set end to size - 1
                                    \Log::info('Uploading last chunk: ' . ($i + 1));
                                    $end = $size - 1;
                                }

                                // get chunk
                                $chunk = fread($handle, $end - $start + 1);

                                $chunkHeaders = [
                                    'Content-Type' => 'video/mp4',
                                    'Content-Length' => $end - $start + 1,
                                    'Content-Range' => 'bytes ' . $start . '-' . $end . '/' . $size,
                                ];

                                \Log::info('Uploading chunk ' . ($i + 1) . ' of ' . $totalChunks . ' (' . $start . ' - ' . $end . ')');

                                \Log::info(json_encode($chunkHeaders));

                                // now, upload the chunk
                                $res = $client->put($upload_url, [
                                    'headers' => $chunkHeaders,
                                    'body' => $chunk,
                                ]);

                            }

                        } catch (\Exception $uploadEx){
                            if( Str::contains($uploadEx->getMessage(), ['404']) ){
                                // Client error: `PUT https://open-upload-i18n.tiktokapis.com/upload?upload_id=7506520107166926870&upload_token=39e97d91-9036-0fa1-b981-2b6ec2c3aaac` resulted in a `404 Not Found` response
                                // strange 404 error, try to pull by URL

                                $jsonData = [
                                    'source_info' => [
                                        'source' => 'PULL_FROM_URL',
                                        'video_url' => app()->environment('production') ? \Storage::temporaryUrl($attachment['path'], now()->addDays(1)) : \Storage::url($attachment['path']),
                                    ],
                                    'post_info' => $post_info,
                                ];

                                \Log::info(json_encode($jsonData));

                                $initRes = $client->post('v2/post/publish/video/init/', [
                                    'json' => $jsonData
                                ]);

                                $response = json_decode($initRes->getBody()->getContents(), true);

                                $json = $response['data'];
                                $publish_id = $json['publish_id'];

                            } else {
                                throw $uploadEx;
                            }

                        }

                    } catch (\Exception $e) {
                        
                        if(Str::contains(strtolower($e->getMessage()), ['user did not authorize the scope', 'invalid request parameters'])){
                            throw new \Exception('Please re-connect your TikTok account and grant full permissions to SocialBu.');
                        }
    
                        if($e instanceof ClientException && $e->getResponse()){
                            $res = @json_decode($e->getResponse()->getBody()->getContents(), true);
                            if($res && isset($res['error'])){
    
                                $code = $res['error']['code'];
    
                                $errMsg = $code . (isset($res['error']['message']) ? ': ' . $res['error']['message'] : '');
                                if($code === 'spam_risk_too_many_pending_share'){
                                    $errMsg = 'Too many pending shares. Please clear any pending shares from TikTok app and try again later';
                                } else if ($code === 'spam_risk_too_many_posts'){
                                    $errMsg = 'Too many posts. Please try again later.';
                                } else {
                                    \Log::info(json_encode($res));
                                    report($e);
                                }
    
                                throw new \Exception('Unable to upload the media: ' . $errMsg);
                            }
                        }
                        throw new \Exception('Unable to upload the media. Please try again later', 0, $e);
                    }

                    break; // only one media is allowed
                }
            }

        } catch (\Exception $e) {
            // cleanup if any
            if(isset($localPath)){
                if(\Storage::disk('local')->exists($localPath)) {
                    \Storage::disk('local')->delete($localPath);
                }
            }
            if(!empty($filesToDelete)){
                try {
                    foreach($filesToDelete as $path){
                        if(\Storage::disk('public')->exists($path)) {
                            \Storage::disk('public')->delete($path);
                        }
                        if(\Storage::disk('local')->exists($path)) {
                            \Storage::disk('local')->delete($path);
                        }
                    }
                } catch (\Exception $ee){}
            }
            throw $e;
        }

        if(!$publish_id){
            throw new \Exception('Something went wrong (no publish ID found). Please try again later');
        }
        $publicaly_available_post_id = null;
        $success = false;
        $tries = 0;
        $exception = null;
        while($tries < 60){
            try {
                $res = $client->post('/v2/post/publish/status/fetch/', [
                    'json' => [
                            "publish_id" => $publish_id
                        ]
                    ]
                );
                $resData = json_decode($res->getBody()->getContents(), true);

                if($resData['data']['status'] === 'PUBLISH_COMPLETE'){
                    $success = true;
                    $publicaly_available_post_id = isset($resData['data']['publicaly_available_post_id']) ? $resData['data']['publicaly_available_post_id'] : null;
                    break;
                }

                if($resData['data']['status'] === 'FAILED'){
                    // should throw error
                    throw new \Exception('Fail reason: ' . $resData['data']['fail_reason']);
                }
                sleep(1);
                
            } catch (\Exception $e) {
                \Log::info('Error while fetching media status: ' . $e->getMessage());
                \Log::info($e);
                $exception = $e;

                if (Str::contains($e->getMessage(), 'Fail reason:')){
                    // no need to retry
                    break;
                }

                sleep(1);
            }
            $tries++;
        }

        if(!empty($filesToDelete)){
            try {
                foreach($filesToDelete as $filePath){
                    if(\Storage::disk('public')->exists($filePath)) {
                        \Storage::disk('public')->delete($filePath);
                    }
                }
            } catch (\Exception $ee){}
        }

        if(isset($localPath)){
            if(\Storage::disk('local')->exists($localPath)) {
                \Storage::disk('local')->delete($localPath);
            }
        }

        if(!$success && $exception){
            if(Str::contains(strtolower($exception->getMessage()), ['401', 'token is invalid'])){
                throw new \Exception('Please re-connect your TikTok account and grant full permissions to SocialBu.');
            }
            if(!Str::contains($exception->getMessage(), 'Fail reason')) {
                report($exception);
            }
            throw new \Exception('Error while sending media to Tiktok. ' . $exception->getMessage());
        }

        // if we are here, it means video was uploaded successfully
        $this->external_id = $publish_id;
        $this->published_at = Carbon::now();
        $this->result = [
            '_success' => true,
            'publish_id' => $publish_id,
            'publicaly_available_post_id' => $publicaly_available_post_id,
        ];
        $this->save();

        if(!$success && !$exception){
            // we don't have any error, but the post is not published yet
            $this->setOption('badge', 'Unconfirmed');
        }

    }
    /**
     * Publish to Pinterest Profile.
     * @return void
     * @throws \Exception
     */
    private function publishToPinterestProfile()
    {
        // only allow 10 posts per hr (to prevent any spam - pinterest can send strike if spam is done)
        $lastHrCount = Post::whereAccountId($this->account_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subHours(1))
            ->count();

        $maxPostsPerHr = $this->account->user->getOption('max_posts_per_hr', 10);

        if($lastHrCount >= $maxPostsPerHr){
            // stop... can be spam
            throw new \Exception('Potentially spam (#mhr): You are posting a lot. Please contact us if you think this should not happen.');
        }

        // we also need to check by matching user_id because pinterest can perma ban us so we can't risk spam
        $lastHrCount = Post::where('user_id', $this->account->user_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subHours(1))
            ->whereHas('account', function ($query) {
                return $query->where('type', 'pinterest.profile');
            })
            ->count();

        if($lastHrCount >= 50){
            // stop... can be spam
            throw new \Exception('Potentially spam (#muhr): You are posting a lot. Please contact us if you think this should not happen.');
        }

        // also limit 10 posts per 1 day per account
        $lastDayCount = Post::whereAccountId($this->account_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subDays(1))
            ->count();

        if($lastDayCount >= 10){
            // stop... can be spam
            throw new \Exception('Potentially spam (#mpd): You are posting a lot. Please contact us if you think this should not happen.');
        }

        // 200 per day by the post-user
        $lastDayCount = Post::where('user_id', $this->user_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subDays(1))
            ->whereHas('account', function ($query) {
                return $query->where('type', 'pinterest.profile');
            })
            ->count();

        if($lastDayCount >= 200){
            // stop... can be spam
            throw new \Exception('Potentially spam (#mupd): You are posting a lot. Please contact us if you think this should not happen.');
        }

        // now, we check the last x posts by post-user
        // if any of the pin detail is similar, we reject the post
        $lastPosts = Post::where('user_id', $this->user_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subDays(1))
            ->whereHas('account', function ($query) {
                return $query->where('type', 'pinterest.profile');
            })
            ->orderBy('published_at', 'desc')
            ->take(50)
            ->get();

        $riskyCount = 0;
        foreach($lastPosts as $post){
            if(
                $post->getOption('pin_title') === $this->getOption('pin_title') ||
                $post->getOption('pin_link') === $this->getOption('pin_link') ||
                $post->content === $this->content
            ){

                if($post->getOption('board_id') === $this->getOption('board_id')){
                    throw new \Exception('Potentially spam: You are posting similar content.');
                } else {
                    $riskyCount++;
                    if($riskyCount >= 3){
                        throw new \Exception('Potentially spam: You are posting similar content.');
                    }
                }

            }
        }

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();

        /** @var Client $client */
        $client = $this->account->getApi();

        $attachments = $this->getAttachments();
        // check if there are post attachments (media)
        if (!empty($attachments)) {

            $n_attachments = count($attachments);

            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            if ($n_attachments > 0) {
                foreach ($attachments as $attachment)
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES))
                        throw new \Exception('Attachment can only be of the following types: ' . implode(', ', $ALLOWED_MEDIA_TYPES));
            }
        } else {
            throw new \Exception('No media attached');
        }

        $extra_data = $this->account->getOption('extra_data');
        $board_id = null;
        if(isset($extra_data) && isset($extra_data['boards'])){
            foreach ($extra_data['boards'] as $board) {
                if ($board['id'] == $this->getOption('board_id') || strtolower($board['name']) == strtolower($this->getOption('board_name')) ) {
                    $board_id = $board['id'];
                    break;
                }
            }
        } else {
            throw new \Exception('Boards not found, cannot publish the post!');
        }

        if(!$board_id){
            throw new \Exception('Board not found, cannot publish the post!');
        }

        if(
            $this->type === 'video' &&
            (
                !$this->getOption('thumbnail') ||
                !$this->getOption('thumbnail.key') ||
                !\Storage::exists($this->getOption('thumbnail')['key'])
            )
        ){
            throw new \Exception('For video pins, thumbnail is required!');
        }

        $attachment = $attachments[0];

        $pinTitle = $this->getOption('pin_title'); // max 100 characters
        $pinDescription = null; // max 500 characters

        // if there is no title, use the content as title
        if(!$pinTitle && $this->content && strlen($this->content) <= 100){
            $pinTitle = $this->content;
        } else {
            $pinDescription = $this->content;
        }

        // make sure link is valid
        if($this->getOption('pin_link')){
            $link = trim($this->getOption('pin_link'));
            if(!filter_var($link, FILTER_VALIDATE_URL)){
                throw new \Exception('Invalid link: ' . $link);
            }
        }

        $pinCreationData = [
            'title' => $pinTitle,
            'description' => $pinDescription,
            'link' => trim($this->getOption('pin_link')),
            'note' => $this->getOption('pin_note'),
            'board_id' => $board_id,
            'alt_text' => $this->getOption('media_alt_text.0', $this->getOption('image_alt_text.0')),
        ];
        $localPath = '';
        $cleanupFiles = function() use(&$localPath){
            if(!empty($localPath) && Storage::disk('local')->exists($localPath)){
                Storage::disk('local')->delete($localPath);
            }
        };

        if($this->type === 'video'){

            $fStream = Storage::readStream($attachment['path']);

            $localPath = 'pinterest_uploading/' . str_random() . '_' . basename($attachment['path']);

            // save the media locally
            Storage::disk('local')->writeStream($localPath, $fStream);

            try {
                $this->validateVideo(storage_path('app/' . $localPath));
            } catch (\Exception $exception){
                $cleanupFiles();
                report($exception);
                // now throw exception
                throw new \Exception('Invalid video file');
            }

            // initiate file upload
            try {
                $res = $client->post('/v5/media', [
                    'json' => [
                        'media_type' => 'video'
                    ]
                ]);
                $data = json_decode($res->getBody(), true);

                $upload_url = $data['upload_url'];
                $media_id = $data['media_id'];
                $upload_params = $data['upload_parameters'];

            } catch(\Exception $e){
                $cleanupFiles();
                report($e);
                throw new \Exception('Unable to start media upload to Pinterest.');
            }

            $multipartData = [];
            foreach($upload_params as $index => $value){
                $multipartData[]= [
                    'name' => $index,
                    'contents' => $value
                ];
            }

            $multipartData[] = [
                'name' => 'file',
                'contents' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
            ];

            $guzzle = guzzle_client();
            try {
                $guzzle->post($upload_url, [
                    'multipart' => $multipartData,
                ]);
            } catch(\Exception $e){
                $cleanupFiles();
                report($e);
                throw new \Exception('Unable to upload media to Pinterest.');
            }

            $success = false;
            $tries = 0;
            while(!$success && $tries < 24){
                try {
                    $res = $client->get('/v5/media/'. $media_id);
                    $data = json_decode($res->getBody(), true);
                    if($data['status'] === 'succeeded'){
                        $success = true;
                    }
                    $tries++;
                    sleep(5);
                } catch(\Exception $e){
                    $cleanupFiles();
                    report($e);
                    throw new \Exception('Unable to check media status.');
                }
            }

            if(!$success){
                $cleanupFiles();
                throw new \Exception('Unable to upload media to Pinterest: media not ready.');
            }

            $cover_image_url = \Storage::cloud()->temporaryUrl($this->getOption('thumbnail')['key'], now()->addDays(1));

            $pinCreationData['media_source'] = [
                'source_type' => 'video_id',
                'cover_image_url' => $cover_image_url,
                'media_id' => $media_id,
            ];

            // cleanup
            $cleanupFiles();

        } else {

            $url = app()->environment('production') ? \Storage::temporaryUrl($attachments[0]['path'], now()->addDays(1)) : \Storage::url($attachments[0]['path']);

            $pinCreationData['media_source'] = [
                'source_type' => 'image_url',
                'url' => $url,
            ];

        }

        $res = $client->post('/v5/pins', [
            'json' => array_filter($pinCreationData),
        ]);

        $response = json_decode($res->getBody(), true);
        $publish_id = $response['id'];

        $this->external_id = $publish_id;
        $this->published_at = Carbon::now();
        $this->result = array_merge($response, [
            '_success' => true,
        ]);

        $this->save();

    }

    /**
     * Publish to threads.
     * @return void
     * @throws \Exception
     */
    private function publishToThreadsProfile()
    {
        // only allow 10 posts per hr (total 250 allowed per 24 hrs)
        $lastHrCount = Post::whereAccountId($this->account_id)
            ->where('published_at', '<=', now())
            ->where('published_at', '>=', now()->subHours(1))
            ->count();

        $maxPostsPerHr = $this->account->user->getOption('max_threads_per_hr', 10);

        if($lastHrCount >= $maxPostsPerHr){
            // stop... can be spam
            throw new \Exception('Potentially spam: You are posting a lot. Please contact us if you think this should not happen.');
        }

        /** @var Client $threads */
        $threads = $this->account->getApi();

        $attachments = $this->getAttachments();

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = ['mp4'];

        // validate attachments
        if (!empty($attachments)) {

            $n_attachments = count($attachments);
            if ($n_attachments > $this->account->getMaxAttachments())
                throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');

            // validate media types
            foreach ($attachments as $attachment) {
                if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                    throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                }
            }

            // multiple attachments should be static images (no gif / video)
            if ($n_attachments > 1) {
                foreach($attachments as $attachment) {
                    if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                        throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per tweet');
                    }
                }
            }

        }

        $content = $this->content;

        // parse @[id:text] to @text
        $content = preg_replace('/@\[([0-9]+):([^\]]+)\]/', '@$2', $content);

        $containerIds = [];

        $isCarousel = count($attachments) > 1;

        $filesToClean = [];
        $cleanup = function() use(&$filesToClean){
            foreach($filesToClean as $path){
                if(Storage::disk('public')->exists($path)){
                    Storage::disk('public')->delete($path);
                }
            }
            $filesToClean = [];
        };

        // first, we upload media
        foreach ($attachments as $attachment) {

            $fStream = Storage::readStream($attachment['path']);
            $localPath = 'threads_uploading/' . str_random() . '_' . basename($attachment['path']);
            // save the media publicly
            Storage::disk('public')->writeStream($localPath, $fStream);

            $filesToClean[] = $localPath;

            $isVideo = Str::contains($attachment['mime'], 'video');

            // upload
            try {

                if($isVideo) {
                    $this->validateVideo(storage_path('app/public/' . $localPath));
                }

                $uploadRes = $threads->post('me/threads', [
                    'form_params' => array_filter([
                        $isVideo ? 'video_url' : 'image_url' => Storage::disk('public')->url($localPath),
                        'is_carousel_item' => $isCarousel,
                        'media_type' => $isVideo ? 'VIDEO' : 'IMAGE',
                        'text' => $isCarousel ? null : $content, // only for single image/video
                        'reply_control' => $isCarousel ? null : $this->getOption('reply_control'),
                    ]),
                ]);

                $json = json_decode($uploadRes->getBody(), true);

                if(isset($json['error'])){
                    throw new \Exception('Unable to upload: ' . json_encode($json['error']));
                }

                if(!isset($json['id'])){
                    report(new \Exception('Threads `id` not set: ' . json_encode($json)));
                }

                $containerIds[] = $json['id'];

            } catch(\Exception $e){

                if ($e instanceof \GuzzleHttp\Exception\ClientException){
                    $body = @json_decode($e->getResponse()->getBody(), true);
                    if($body){
                        if(isset($body['error']['error_user_msg'])){
                            $cleanup();
                            throw new \Exception('Unable to upload media to Threads: ' . $body['error']['error_user_msg']);
                        }
                    }
                }

                $cleanup();
                throw new \Exception('Unable to upload media to Threads: ' . $e->getMessage());
            }

        }

        $finalContainerId = null;

        if($isCarousel){
            try {
                // create carousel
                $carouselRes = $threads->post('me/threads', [
                    'form_params' => array_filter([
                        'media_type' => 'CAROUSEL',
                        // csv of container ids
                        'children' => implode(',', $containerIds),
                        'text' => $content,
                        'reply_control' => $this->getOption('reply_control'),
                    ]),
                ]);

                $json = json_decode($carouselRes->getBody(), true);
                
                if (!isset($json['id'])) {
                    $cleanup();

                    if(Arr::get($json, 'error.message')){
                        throw new \Exception(Arr::get($json, 'error.message'));
                    }

                    $err = new \Exception('Unable to create carousel post on threads: ' . json_encode($json));
                    report($err);
                    throw $err;
                }

                $finalContainerId = $json['id'];
            } catch (\Exception $e){

                if ($e instanceof \GuzzleHttp\Exception\ClientException){
                    $body = @json_decode($e->getResponse()->getBody(), true);
                    if($body){
                        if(isset($body['error']['error_user_msg'])){
                            $cleanup();
                            throw new \Exception('Unable to create carousel on Threads: ' . $body['error']['error_user_msg']);
                        }
                    }
                }

                $cleanup();
                report($e);
                throw new \Exception('Unable to create carousel on Threads');

            }
        } else if(count($containerIds) > 0) {
            $finalContainerId = $containerIds[0];
        } else {
            try {
                // text post, create text container
                $textRes = $threads->post('me/threads', [
                    'form_params' => array_filter([
                        'media_type' => 'TEXT',
                        'text' => $content,
                        'reply_control' => $this->getOption('reply_control'),
                    ]),
                ]);

                $json = json_decode($textRes->getBody(), true);

                if (!isset($json['id'])) {
                    $cleanup();

                    if(Arr::get($json, 'error.message')){
                        throw new \Exception(Arr::get($json, 'error.message'));
                    }

                    $err = new \Exception('Unable to create text post on threads: ' . json_encode($json));
                    report($err);
                    throw $err;
                }

                $finalContainerId = $json['id'];

            } catch (\Exception $e){

                if ($e instanceof \GuzzleHttp\Exception\ClientException){
                    $body = @json_decode($e->getResponse()->getBody(), true);
                    if($body){
                        if(isset($body['error']['error_user_msg'])){
                            $cleanup();
                            throw new \Exception('Unable to create post on Threads: ' . $body['error']['error_user_msg']);
                        }
                    }
                }

                $cleanup();
                report($e);
                throw new \Exception('Unable to create post on Threads');

            }
        }

        // make sure container is ready
        $tries = 0;
        while($tries <= 20){
            try {
                $res = $threads->get($finalContainerId. '?fields=status');
                $json = json_decode($res->getBody(), true);
                //not sure why but sometimes there is no status field
                if (isset($json['status']) && $json['status'] === 'FINISHED') {
                    //fb downloaded the media, so cleanup
                    $cleanup();
                    break;
                }
                sleep(10);

                if($tries >= 20){
                    report(new \Exception('Container not published: ' . json_encode($json)));
                }
            } catch(\Exception $e){
                $cleanup();
                report($e);
                sleep(1);
            }

            $tries++;

        }

        $response = $threads->post("me/threads_publish", [
            'form_params' => [
                'creation_id' => $finalContainerId,
            ]
        ]);

        $json = json_decode($response->getBody(), true);

        if(isset($json['error'])){
            throw new \Exception('Unable to publish: ' . json_encode($json['error']));
        }

        if(!isset($json['id'])){
            report(new \Exception('Threads `id` not set: ' . json_encode($json)));
        }

        $this->external_id = $json['id'];

        // get permalink
        $permalink = null;
        try {
            $res = $threads->get($json['id'] . '?fields=permalink,id');
            $json = json_decode($res->getBody(), true);
            $permalink = isset($json['permalink']) ? $json['permalink'] : null;
        } catch(\Exception $e){
            $cleanup();
            report($e);
        }

        // good
        $this->published_at = Carbon::now();
        $this->result = [
            '_success' => true,
            'id' => $this->external_id,
            'containers' => $containerIds,
            'permalink' => $permalink,
        ];
        $this->save();

        // now post threaded replies if needed
        if(isset($this->options['threaded_replies']) && !empty($this->options['threaded_replies'])){
            $lastPostId = $json['id'];
            $exceptions = [];
            $containerIds = [];
            $threadReplyIds = [];
            $threaded_replies = $this->options['threaded_replies'];

            if(!is_array($threaded_replies)) // shouldn't happen
                $threaded_replies = (array) @json_decode($this->options['threaded_replies'], true);
            
                
            foreach($threaded_replies as $replyIndex => $reply){
                $finalContainerId = null;
                $content = '';
                if(is_array($reply)){
                    if(!isset($reply['text'])){
                        $reply['text'] = '';
                    }
                    if(!isset($reply['media'])) $reply['media'] = [];
                    $content = $reply['text'];
                } else {
                    $content = $reply;
                    $reply = [
                        'text' => $content,
                        'media' => []
                    ];
                }

                // normalize newlines in content
                $content = preg_replace('/\r\n|\r|\n/', "\n", $content); 

                $isCarousel = count($reply['media']) > 1;

                foreach ($reply['media'] as $file){
                    $path = $file['key'];

                    $fStream = Storage::cloud()->readStream($path);
                    $localPath = 'threads_uploading/' . str_random() . '_' . basename($path);
                    // save the media publicly
                    Storage::disk('public')->writeStream($localPath, $fStream);

                    $filesToClean[] = $localPath;

                    $isVideo = Str::contains($file['mime'], 'video');
                    // upload
                    try {

                        if($isVideo) {
                            $this->validateVideo(Storage::disk('public')->path($localPath));
                        }

                        $uploadRes = $threads->post('me/threads', [
                            'form_params' => array_filter([
                                $isVideo ? 'video_url' : 'image_url' => Storage::disk('public')->url($localPath),
                                'is_carousel_item' => $isCarousel,
                                'media_type' => $isVideo ? 'VIDEO' : 'IMAGE',
                                'text' => $isCarousel ? null : $content, // only for single image/video
                                'reply_to_id' => $lastPostId
                            ]),
                        ]);

                        $json = json_decode($uploadRes->getBody(), true);

                        if(isset($json['error'])){
                            throw new \Exception('Unable to upload: ' . json_encode($json['error']));
                        }

                        if(!isset($json['id'])){
                            report(new \Exception('Threads `id` not set: ' . json_encode($json)));
                        }

                        $containerIds[] = $json['id'];

                        $finalContainerId = $json['id'];

                    } catch(\Exception $e){

                        $exceptions[$replyIndex] = $e;
                        if ($e instanceof \GuzzleHttp\Exception\ClientException){
                            $body = @json_decode($e->getResponse()->getBody(), true);
                            if($body){
                                if(isset($body['error']['error_user_msg'])){
                                    $cleanup();
                                    throw new \Exception('Unable to upload media to Threads: ' . $body['error']['error_user_msg']);
                                }
                            }
                        }

                        $cleanup();
                        report($e);
                        throw new \Exception('Unable to upload media to Threads');
                    }

                }

                if($isCarousel){
                    try {
                        // create carousel
                        $carouselRes = $threads->post('me/threads', [
                            'form_params' => array_filter([
                                'media_type' => 'CAROUSEL',
                                // csv of container ids
                                'children' => implode(',', $containerIds),
                                'text' => $content,
                                'reply_to_id' => $lastPostId
                            ]),
                        ]);

                        $json = json_decode($carouselRes->getBody(), true);

                        if(isset($json['error'])){
                            throw new \Exception('Unable to create carousel: ' . json_encode($json['error']));
                        }

                        if(!isset($json['id'])){
                            report(new \Exception('Threads `id` not set: ' . json_encode($json)));
                        }

                        $finalContainerId = $json['id'];
                    } catch (\Exception $e){
                        $exceptions[$replyIndex] = $e;

                        if ($e instanceof \GuzzleHttp\Exception\ClientException){
                            $body = @json_decode($e->getResponse()->getBody(), true);
                            if($body){
                                if(isset($body['error']['error_user_msg'])){
                                    $cleanup();
                                    throw new \Exception('Unable to create carousel on Threads: ' . $body['error']['error_user_msg']);
                                }
                            }
                        }

                        $cleanup();
                        report($e);
                        throw new \Exception('Unable to create carousel on Threads');

                    }
                } else if(empty($reply['media'])) {
                    try {
                        // text post, create text container
                        $textRes = $threads->post('me/threads', [
                            'form_params' => array_filter([
                                'media_type' => 'TEXT',
                                'text' => $content,
                                'reply_to_id' => $lastPostId
                            ]),
                        ]);

                        $json = json_decode($textRes->getBody(), true);

                        if (!isset($json['id'])) {
                            $cleanup();

                            if(Arr::get($json, 'error.message')){
                                throw new \Exception('Error while posting threaded reply: ' .  Arr::get($json, 'error.message'));
                            }

                            $err = new \Exception('Error while posting threaded reply: ' . json_encode($json));
                            report($err);
                            throw $err;
                        }

                        $finalContainerId = $json['id'];
                    } catch (\Exception $e){
                        $exceptions[$replyIndex] = $e;

                        if ($e instanceof \GuzzleHttp\Exception\ClientException){
                            $body = @json_decode($e->getResponse()->getBody(), true);
                            if($body){
                                if(isset($body['error']['error_user_msg'])){
                                    $cleanup();
                                    throw new \Exception('Unable to create post on Threads: ' . $body['error']['error_user_msg']);
                                }
                            }
                        }

                        $cleanup();
                        report($e);
                        throw new \Exception('Unable to create post on Threads');

                    }
                }
                // make sure container is ready
                $tries = 0;
                while($tries <= 20){
                    try {
                        $res = $threads->get($finalContainerId. '?fields=status');
                        $json = json_decode($res->getBody(), true);
                        if($json['status'] === 'FINISHED'){
                            //fb downloaded the media, so cleanup
                            $cleanup();
                            break;
                        } else {
                            \Log::info('Container status: ' . $json['status']);
                        }
                        sleep(10);

                        if($tries >= 20){
                            report(new \Exception('Container not published: ' . json_encode($json)));
                        }
                    } catch(\Exception $e){
                        $exceptions[$replyIndex] = $e;
                        $cleanup();
                        report($e);
                        sleep(1);
                    }
                    $tries++;
                }

                $response = $threads->post("me/threads_publish", [
                    'form_params' => [
                        'creation_id' => $finalContainerId,
                    ]
                ]);

                $json = json_decode($response->getBody(), true);

                if(isset($json['error'])){
                    throw new \Exception('Unable to publish: ' . json_encode($json['error']));
                }

                if(!isset($json['id'])){
                    report(new \Exception('Threads `id` not set: ' . json_encode($json)));
                }

                $lastPostId = $json['id'];
                $threadReplyIds[] = $lastPostId;
            }

            // if we are here, all replies are processed
            $result = $this->result;
            $result['threaded_replies'] = [
                'threadReplyIds' => $threadReplyIds,
                'exceptions' => collect($exceptions)->map(function($e){
                    /** @var \Exception $e */
                    return $e->getTraceAsString();
                })->toArray(),
            ];
            $this->result = $result;
            $this->save();
        }

    }

    /**
     * Publish to bluesky.
     * @return void
     * @throws \Exception
     */
    private function publishToBlueskyProfile()
    {
        try {
            /** @var Client $bluesky */
            $bluesky = $this->account->getApi();
        } catch (\Exception $e) {
            if($e instanceof \GuzzleHttp\Exception\ClientException){
                $body = @json_decode($e->getResponse()->getBody(), true);
                if($body){
                    if(isset($body['error'], $body['message'])){
                        throw new \Exception('Unable to connect to Bluesky account: ' . $body['message']);
                    }
                }
            }
            throw new \Exception('Unable to connect to Bluesky account: ' . Str::limit($e->getMessage(), 50), 0, $e);
        }

        $attachments = $this->getAttachments();

        $ALLOWED_MEDIA_TYPES = $this->account->getAttachmentTypes();
        $ALLOWED_VIDEO_TYPES = [];

        $preparePost = function($content, $attachments) use (&$ALLOWED_MEDIA_TYPES, &$ALLOWED_VIDEO_TYPES, &$bluesky){
            // validate attachments
            if (!empty($attachments)) {
    
                $n_attachments = count($attachments);
                if ($n_attachments > $this->account->getMaxAttachments())
                    throw new \Exception('Exceeded maximum number of allowed attachments (' . $this->account->getMaxAttachments() .')');
    
                // validate media types
                foreach ($attachments as $attachment) {
                    if (!in_array($attachment['type'], $ALLOWED_MEDIA_TYPES)) {
                        throw new \Exception('Invalid attachment type: ' . $attachment['type']);
                    }
                }
    
                // multiple attachments should be static images (no gif / video)
                if ($n_attachments > 1) {
                    foreach($attachments as $index => $attachment) {
                        if(in_array($attachment['type'], array_merge($ALLOWED_VIDEO_TYPES, ['gif']))) {
                            throw new \Exception('When attaching video/gif, only 1 media attachment is allowed per tweet');
                        }
                    }
                }
    
            }
    
            $content = $content ?: '';
    
            // parse @[id:text] to @text
            $content = preg_replace('/@\[([0-9]+):([^\]]+)\]/', '@$2', $content);
    
            $images = [];
            $video = null;
    
            $filesToClean = [];
            $cleanup = function() use(&$filesToClean){
                foreach($filesToClean as $path){
                    if(Storage::disk('local')->exists($path)){
                        Storage::disk('local')->delete($path);
                    }
                }
                $filesToClean = [];
            };
    
            // first, we upload media
            foreach ($attachments as $index => $attachment) {

                $path = null;
                if(!isset($attachment['path']) && isset($attachment['key'])){
                    $path =  $attachment['key'];
                    $fStream = Storage::cloud()->readStream($attachment['key']);
                } else {
                    $path = $attachment['path'];
                    $fStream = Storage::readStream($attachment['path']);
                }    
                
                $localPath = 'bluesky_uploading/' . str_random() . '_' . basename($path);
                // save the media publicly
                Storage::disk('local')->writeStream($localPath, $fStream);

                // if it's an image and type is png, convert to jpg
                $shouldConvertToJPG = Str::contains($attachment['mime'], 'image') && in_array($attachment['type'], ['png',]);
    
                if($shouldConvertToJPG){
                    $filesToClean[] = $localPath; // so we clean up original file
    
                    $img = \Intervention\Image\Facades\Image::make(storage_path('app/' . $localPath));
    
                    $newPath = 'bluesky_uploading/' . str_random() . '_' . basename($path) . '.jpg';
    
                    $img->save(storage_path('app/' . $newPath), 90);
                    $localPath = $newPath;
                }
    
                $filesToClean[] = $localPath;
    
                $isVideo = Str::contains($attachment['mime'], 'video');

                $aspectRatio = null; // for holding image aspect ratio

                // upload
                try {
    
                    if($isVideo) {
                        $vidDetails = $this->validateVideo(storage_path('app/' . $localPath)); // this function also returns video details

                        $aspectRatio = [
                            'width' => $vidDetails['width'],
                            'height' => $vidDetails['height'],
                        ];

                    } else {
    
                        // bluesky api only allows images less than 976kb
                        reduce_image_size_if_needed(storage_path('app/' . $localPath), 976 * 1024);
                        
                        $img = \Intervention\Image\Facades\Image::make(storage_path('app/' . $localPath));
    
                        // get aspect ratio
                        $aspectRatio = [
                            'width' => $img->width(),
                            'height' => $img->height(),
                        ];
                    }

                    try {
                        if($isVideo){
                            //upload video to blob api
                            $token = json_decode($this->account->token, true);

                            $did = $token['did'];

                            if(!isset($token['didDoc']['service'][0]['serviceEndpoint'])){
                                throw new \Exception('No service endpoint found for Bluesky account');
                            }
    
                            $serviceEndpoint = $token['didDoc']['service'][0]['serviceEndpoint'];
    
                            $didWeb = 'did:web:' . parse_url($serviceEndpoint, PHP_URL_HOST);
    
                            $serviceAuth = $bluesky->get('com.atproto.server.getServiceAuth', [
                                'query' => [
                                    'aud' => $didWeb,
                                    'exp' => time() + 60,
                                    'lxm' => 'com.atproto.repo.uploadBlob',
                                ]
                            ]);
    
                            $authToken = json_decode($serviceAuth->getBody(), true);
    
                            //custom client with service auth token
                            $client = guzzle_client([
                                'headers' => [
                                    'content-type' => $attachment['mime'],
                                    'Accept' => 'application/json',
                                    'Authorization' => 'Bearer ' . $authToken['token'],
                                ]                            
                            ]);
    
                            $res = $client->post('https://video.bsky.app/xrpc/app.bsky.video.uploadVideo', [
                                'body' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
                                'query' => [
                                    'did' => $did,
                                    'name' => $attachment['name'],
                                ]
                            ]);
    
                            $json = json_decode($res->getBody(), true);
    
                            $jobId = $json['jobId'];
    
    
                            $tries = 0;
                            while($tries < 30){ // sometimes bluesky takes too much to process the video
                                $status = $client->get('https://video.bsky.app/xrpc/app.bsky.video.getJobStatus', [
                                    'query' => [
                                        'jobId' => $jobId,
                                    ]
                                ]);
    
                                $status = json_decode($status->getBody(), true);
    
                                if($status['jobStatus']['state'] === 'JOB_STATE_COMPLETED'){
                                    break;
                                }
    
                                sleep(5);
                                $tries++;
                            }

                            if(!isset($status) || !isset($status['jobStatus']) || !isset($status['jobStatus']['blob'])){
                                throw new \Exception('Video taking too long to process.');
                            }

                            $blob = $status['jobStatus']['blob'];

                            $video = array_merge([
                                '$type' => 'app.bsky.embed.video',
                                'video' => $blob,
                                'alt' => $this->getOption('media_alt_text.0', $this->getOption('image_alt_text.0', '')),
                            ], array_filter([
                                'aspectRatio' => $aspectRatio,
                            ]));
                        } else {
                            //upload image to blob api
                            $uploadRes = $bluesky->post('com.atproto.repo.uploadBlob', [
                                'body' => \GuzzleHttp\Psr7\Utils::tryFopen(storage_path('app/' . $localPath), 'r'),
                                'headers' => [
                                    'Content-Type' => $attachment['mime'],
                                ],
                            ]);

                            $json = json_decode($uploadRes->getBody(), true);
                            $blob = $json['blob'];

                            $images[] = array_merge([
                                'alt' => $this->getOption('media_alt_text.' . $index, $this->getOption('image_alt_text.' . $index, '')),
                                'image' => $blob,
                            ], array_filter([
                                'aspectRatio' => $aspectRatio,
                            ]));
                        }
                        
                        
                    }  catch (\Exception $e) {

                        if($e instanceof ClientException){
                            $body = @json_decode($e->getResponse()->getBody(), true);
                            if($body){
                                if(isset($body['jobStatus']['error'])){
                                    throw new \Exception('Error uploading ' . ($isVideo ? 'video' : 'image') . ' to Bluesky: ' . $body['jobStatus']['error']);
                                }
                            }
                        }

                        throw new \Exception('Error uploading ' . ($isVideo ? 'video' : 'image') . ' to Bluesky: ' . $e->getMessage());
                    }
    
                } finally {
                    $cleanup();
                }
    
            }
            
            $extractor = new Extractor();
            $links = $extractor->extractURLsWithIndices($content); // array of obj (url, indices)
    
            $facets = [];
    
            $newContent = $content;
            foreach($links as $link){
    
                $origLink = $link['url'];
    
                // first, remove http:// or https:// from the link
                $origLink = preg_replace('/^https?:\/\//', '', $origLink);
    
                // also delete www
                $origLink = preg_replace('/^www\./', '', $origLink);
    
                // first we truncate
                $newLink = Str::limit($origLink, 20, '...');
    
                // replace the link in content
                $newContent = str_replace($link['url'], $newLink, $newContent);
    
                // now get new indices (by finding index of newLink in newContent)
                $indices = [ strpos($newContent, $newLink), strpos($newContent, $newLink) + strlen($newLink) ];
    
                $facets[] = [
                    'index' => [
                        'byteStart' => $indices[0],
                        'byteEnd' => $indices[1],
                    ],
                    'features' => [
                        [
                            '$type' => 'app.bsky.richtext.facet#link',
                            'uri' => (Str::startsWith($link['url'], 'http://') || Str::startsWith($link['url'], 'https://')) ? $link['url'] : 'https://' . $link['url'],
                        ]
                    ]
                ];
            }
            // update content
            $content = $newContent;
    
    
            // Match @username.domain.tld, @domain.tld, or @domain format
            $pattern = '/@([a-zA-Z0-9][a-zA-Z0-9-]*(?:\.[a-zA-Z0-9][a-zA-Z0-9-]*)*)\b/';
            $mentions = [];
    
            preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
    
            foreach ($matches[0] as $index => $match) {
                $mention = $match[0];
                $byteStart = mb_strlen(mb_substr($content, 0, $match[1],'8bit'), '8bit'); //8bit encoding
                $byteEnd = $byteStart + mb_strlen($mention, '8bit');
    
                $mentions[] = [
                    'screen_name' => $matches[1][$index][0],
                    'indices' => [
                        $byteStart,
                        $byteEnd
                    ]
                ];
            }
    
            foreach($mentions as $mention){
    
                $json = null;
                try {
    
                    // resolve handle to did
                    $resp = $bluesky->get('com.atproto.identity.resolveHandle', [
                        'query' => [
                            'handle' => $mention['screen_name'],
                        ]
                    ]);
    
                    $json = json_decode($resp->getBody(), true);
    
                } catch(\Exception $e){
                    continue;
                }
    
                $facets[] = [
                    'index' => [
                        'byteStart' => $mention['indices'][0],
                        'byteEnd' => $mention['indices'][1],
                    ],
                    'features' => [
                        [
                            '$type' => 'app.bsky.richtext.facet#mention',
                            'did' => $json['did'],
                        ]
                    ]
                ];
            }
    
            // Match hashtags using regex... the hashtag indices represent byte positions in the UTF-8 encoded string, not character positions, which is crucial for Bluesky's post
            preg_match_all('/#[\p{L}\p{N}_]+/u', $content, $matches, PREG_OFFSET_CAPTURE);
            
            foreach ($matches[0] as $match) {
                $hashtag = $match[0];
                $byteStart = mb_strlen(mb_substr($content, 0, $match[1], '8bit'), '8bit'); //8bit encoding
                $byteEnd = $byteStart + mb_strlen($hashtag, '8bit');
                
                $facets[] = [
                    'index' => [
                        'byteStart' => $byteStart,
                        'byteEnd' => $byteEnd
                    ],
                    'features' => [
                        [
                            '$type' => 'app.bsky.richtext.facet#tag',
                            'tag' => substr($hashtag, 1) // Remove # from start
                        ]
                    ]
                ];
            }
    
    
            $embed = null;
    
            if(count($attachments) > 0){
                if(!empty($images)){
                    $embed = [
                        '$type' => 'app.bsky.embed.images',
                        'images' => $images,
                    ];
                } else if($video){
                    $embed = $video;
                }
            } else if(!empty($links)){
    
                try {
                    $metaTags = extract_meta_tags($links[0]['url']);
                } catch (\Exception $e){
                    \Log::error('Error while fetching meta tags: ' . $e->getMessage());
                    $metaTags = [];
                }
    
                $thumb = null;
    
                // fetch image and upload if possible
                $META_IMAGE_KEYS = ['og:image', 'twitter:image', 'twitter:image:src', 'image',];
                foreach($META_IMAGE_KEYS as $key){
                    if(isset($metaTags[$key]) && filter_var($metaTags[$key], FILTER_VALIDATE_URL)){
    
                        // upload to blob api
                        $localPath = 'bluesky_uploading/' . str_random() . '_' . basename($metaTags[$key]);
                        $guzzle = guzzle_client();

                        try {
                            $guzzle->get($metaTags[$key], [
                                'sink' => \Storage::disk('local')->path($localPath),
                            ]);

                            $mimeType = \Storage::disk('local')->mimeType($localPath);

                            // if it's an image and type is png, convert to jpg
                            $shouldConvertToJPG = Str::contains($mimeType, ['png', 'webp']);

                            if($shouldConvertToJPG){
                                $img = \Intervention\Image\Facades\Image::make(\Storage::disk('local')->path($localPath));

                                $newPath = $localPath. '.jpg';

                                $img->save(\Storage::disk('local')->path($newPath), 90);

                                // delete old file
                                if(Storage::disk('local')->exists($localPath)){
                                    Storage::disk('local')->delete($localPath);
                                }

                                $localPath = $newPath;

                                $mimeType = 'image/jpeg';
                            }

                            reduce_image_size_if_needed(\Storage::disk('local')->path($localPath), 976 * 1024);

                            try {
                                $uploadRes = $bluesky->post('com.atproto.repo.uploadBlob', [
                                    // stream body
                                    'body' => \GuzzleHttp\Psr7\Utils::tryFopen(\Storage::disk('local')->path($localPath), 'r'),
                                    'headers' => [
                                        'Content-Type' => $mimeType,
                                    ],
                                ]);
                            } catch (\Exception $e){
                                \Log::info('File: ' . $metaTags[$key] . ' - ' . \Storage::disk('local')->getMimetype($localPath));
                                report($e);
                                throw $e;
                            }
    
                            $json = json_decode($uploadRes->getBody(), true);
    
                            $thumb = $json['blob'];
    
                        } catch (\Exception $e){
                            \Log::info('Error while fetching thumb: ' . $e->getMessage());
                        } finally {
                            if(Storage::disk('local')->exists($localPath)){
                                Storage::disk('local')->delete($localPath);
                            }
                        }
    
                        break;
                    }
                }
    
                $embed = [
                    '$type' => 'app.bsky.embed.external',
                    'external' => array_merge(
                        [
                            'description' => isset($metaTags['description']) && $metaTags['description'] ? $metaTags['description'] : '',
                            'title' => isset($metaTags['title']) && $metaTags['title'] ? $metaTags['title'] : '',
                        ], 
                        array_filter([
                            'uri' => (Str::startsWith($links[0]['url'], 'http://') || Str::startsWith($links[0]['url'], 'https://')) ? $links[0]['url'] : 'https://' . $links[0]['url'], 
                            'thumb' => $thumb,
                        ])
                    ),
                ];
            }

            return [
                'content' => $content,
                'embed' => $embed,
                'facets' => $facets,
            ];
        };

        $res = $preparePost($this->content, $attachments);

        //now publish the post
        $response = $bluesky->post("com.atproto.repo.createRecord", [
            'json' => [
                'repo' => $this->account->account_id,
                'collection' => 'app.bsky.feed.post',
                'record' => array_merge(
                    [
                        '$type' => 'app.bsky.feed.post',
                        'text' => $res['content'],
                        'createdAt' => Carbon::now()->toIso8601String()
                    ], 
                    array_filter([
                        'embed' => $res['embed'],
                        'facets' => $res['facets'],
                    ])
                ),
            ]
        ]);

        $json = json_decode($response->getBody(), true);

        $this->external_id = $json['uri'];

        $rootPost = [];
        $rootPost['uri'] = $json['uri'];
        $rootPost['cid'] = $json['cid'];

        // good
        $this->published_at = Carbon::now();
        $this->result = array_merge($json, [
            '_success' => true,
        ]);
        $this->save();

        //here we send the threaded replies
        if(isset($this->options['threaded_replies']) && !empty($this->options['threaded_replies'])){
            $lastPost = $rootPost;
            $exceptions = [];
            $threadReplies = [];
            $threaded_replies = $this->options['threaded_replies'];

            if(!is_array($threaded_replies)) // shouldn't happen
                $threaded_replies = (array) @json_decode($this->options['threaded_replies'], true);
            
                
            foreach($threaded_replies as $replyIndex => $reply){

                $content = '';
                if(is_array($reply)){
                    if(!isset($reply['text'])){
                        $reply['text'] = '';
                    }
                    if(!isset($reply['media'])) $reply['media'] = [];
                    $content = $reply['text'];
                } else {
                    $content = $reply;
                    $reply = ['text' => $reply, 'media' => []];
                }


                try {
                    $res = $preparePost($content, $reply['media']);

                    $response = $bluesky->post("com.atproto.repo.createRecord", [
                        'json' => [
                            'repo' => $this->account->account_id,
                            'collection' => 'app.bsky.feed.post',
                            'record' => array_merge(
                                [
                                    '$type' => 'app.bsky.feed.post',
                                    'text' => $res['content'],
                                    'createdAt' => Carbon::now()->toIso8601String(),
                                    "reply" => [
                                        "root" => [
                                            "uri"=> $rootPost['uri'],
                                            "cid"=> $rootPost['cid']
                                        ],
                                        "parent" => [
                                            "uri" => $lastPost['uri'],
                                            "cid" => $lastPost['cid']
                                        ]
                                    ]
                                ], 
                                array_filter([
                                    'embed' => $res['embed'],
                                    'facets' => $res['facets']
                                ])
                            )
                        ]
                    ]);
                } catch (\Exception $e) {
                    $exceptions[$replyIndex] = $e;
                }

                $json = json_decode($response->getBody(), true);

                $lastPost['uri'] = $json['uri'];
                $lastPost['cid'] = $json['cid'];

                $threadReplies[$replyIndex] = $json;
            }

            // if we are here, all replies are processed
            $result = $this->result;

            $result['threaded_replies'] = [
                'threadReplies' => $threadReplies,
                'exceptions' => collect($exceptions)->map(function($e){
                    /** @var \Exception $e */
                    return $e->getTraceAsString();
                })->toArray(),
            ];
            $this->result = $result;
            $this->save();
        }
    }

    /**
     * Validate video for posting
     *
     * @param $fullPathVideo
     * @return array
     * @throws \Exception
     */
    private function validateVideo($fullPathVideo){

        $account = $this->account;

        // validate video
        $ffprobe = FFProbe::create();
        if(!$ffprobe->isValid($fullPathVideo)){
            throw new \Exception('Invalid video file');
        }

        $postType = 'post'; // default post type for getVideoLimit
        if($this->getOption('post_as_reel')){
            $postType = 'reel';
        }

        //for story and feed, video limits are same
        $videoLimit = $account->getVideoLimit($postType);

        // verify duration
        $durationSecs = (float) $ffprobe->format($fullPathVideo)->get('duration');

        if($durationSecs > $videoLimit['duration']['max']){
            throw new \Exception('Maximum video duration limit of ' . $videoLimit['duration']['max'] . ' seconds exceeded');
        }
        if($durationSecs < $videoLimit['duration']['min']){
            throw new \Exception('Minimum video duration must be ' . $videoLimit['duration']['min'] . ' seconds');
        }

        $videoStream = $ffprobe->streams($fullPathVideo)->videos()->first();

        if(!$videoStream){
            throw new \Exception('No video stream found. Please check your video file');
        }

        // validate dimensions
        $width = (int) $ffprobe->streams($fullPathVideo)->videos()->first()->get('width');
        $height = (int) $ffprobe->streams($fullPathVideo)->videos()->first()->get('height');
        if($videoLimit['dimensions']['max'][0] && $width > $videoLimit['dimensions']['max'][0]){
            throw new \Exception('Video width should be less than ' . $videoLimit['dimensions']['max'][0]);
        }
        if($videoLimit['dimensions']['max'][1] && $height > $videoLimit['dimensions']['max'][1]){
            throw new \Exception('Video height should be less than ' . $videoLimit['dimensions']['max'][1]);
        }

        if($videoLimit['dimensions']['min'][0] && $width < $videoLimit['dimensions']['min'][0]){
            throw new \Exception('Video width should be greater than ' . $videoLimit['dimensions']['min'][0]);
        }
        if($videoLimit['dimensions']['min'][1] && $height < $videoLimit['dimensions']['min'][1]){
            throw new \Exception('Video height should be greater than ' . $videoLimit['dimensions']['min'][1]);
        }

        // return details
        return [
            'duration' => $durationSecs,
            'width' => $width,
            'height' => $height,
        ];
    }


    /**
     * @deprecated use getMetrics instead
     */
    public function getInsights($preFetchedData = null){
        return $this->getMetrics($preFetchedData);
    }

    /**
     * @param array $ids
     * @return \Illuminate\Support\Collection
     * @throws \Exception
     * @deprecated use getMetricsForPosts instead
     */
    public static function getInsightsForPosts(array $ids){
        return self::getMetricsForPosts($ids);
    }

    public function getMetrics($preFetchedData = null){

        if($this->fetchedInsights){
            return $this->fetchedInsights;
        }

        $metricsByAccountType = [
            'facebook.page' => [
                'reactions' => 'int',
                'comments' => 'int',
                'impressions' => 'int',
                'shares' => 'int',
                'video_views' => 'int',
                'clicks' => 'int',
            ],
            'facebook.group' => [],
            'instagram.direct' => [],
            'instagram.api' => [
                'likes' => 'int',
                'impressions' => 'int',
                'comments' => 'int',
                'video_views' => 'int',
                'reach' => 'int',
                'saved' => 'int',
                'plays' => 'int',
                'shares' => 'int',
            ],
            'twitter.profile' => [
                'retweets' => 'int',
                'likes' => 'int',
            ],
            'mastodon.profile' => [
                'replies' => 'int',
                'reblogs' => 'int',
                'favourites' => 'int',
            ],
            'linkedin.org' => [
                'clicks' => 'int',
                'comments' => 'int',
                'engagement' => 'ratio',
                'impressions' => 'int',
                'likes' => 'int',
                'shares' => 'int',
                'unique_impressions' => 'int',
                'video_views' => 'int',
            ],
            'linkedin.brand' => [
                'clicks' => 'int',
                'comments' => 'int',
                'engagement' => 'ratio',
                'impressions' => 'int',
                'likes' => 'int',
                'shares' => 'int',
                'unique_impressions' => 'int',
                'video_views' => 'int',
            ],
            'linkedin.profile' => [
                'comments' => 'int',
                'likes' => 'int',
            ],
            'google.location' => [
                'queries_direct' => 'int',
                'queries_indirect' => 'int',
                'views_maps' => 'int',
                'views_search' => 'int',
                'actions_website' => 'int',
                'actions_phone' => 'int',
                'post_views' => 'int',
                'post_cta_clicks' => 'int',
            ],
            'tiktok.profile' => [], // none supported yet
            'google.youtube' => [], // todo: add
            'reddit.profile' => [
                'comments' => 'int',
                'score' => 'int',
            ],
            'reddit.subreddit' => [
                'comments' => 'int',
                'score' => 'int',
            ],
            'pinterest.profile' => [
                'saved' => 'int',
                'comments' => 'int',
                'impressions' => 'int',
                'pin_clicks' => 'int',
                'reactions' => 'int',
            ],

            'threads.profile' => [
                'views' => 'int',
                'likes' => 'int',
                'replies' => 'int',
                'reposts' => 'int',
                'quotes' => 'int',
            ],

            'bluesky.profile' => [
                'likes' => 'int',
                'replies' => 'int',
                'reposts' => 'int',
            ],
        ];

        $accountType = $this->account->type;
        if(!isset($metricsByAccountType[$accountType])){
            report(new \Exception('No insights configured for account: ' . $accountType));
            return [];
        }
        $metricsToFetch = $metricsByAccountType[$accountType];
        
        if(!$metricsToFetch){
            return [];
        }
        
        if($preFetchedData){
            $data = $preFetchedData;
        } else {
            try {
                $data = self::getMetricsForPosts([$this->id]);
            } catch (\Exception $e) {
                report($e);
                return [];
            }
        }

        // get rows for current post
        $metricsRows = $data->filter(function ($row) use($metricsToFetch){
            if(!isset($row->metric_type)) return false;
            return isset($metricsToFetch[$row->metric_type]) && $row->post_id === $this->id;
        });

        $fetchedMetrics = $metricsRows->map(function ($row) use($metricsToFetch){
            $dataType = $metricsToFetch[$row->metric_type];
            $value = $row->metric_value;
            if($dataType === 'int'){
                $value = (int) $value;
            } else if($dataType === 'ratio'){
                $value = (float) ($value / 100);
            }
            return [
                'type' => $row->metric_type,
                'value' => $value,
            ];
        })->filter(function($arr){
            return $arr['value'] !== 0; // prevent 0 values
        })->values()->toArray();

        $this->fetchedInsights = $fetchedMetrics;
        return $this->fetchedInsights;
    }

    /**
     * @param array $ids
     * @return \Illuminate\Support\Collection
     * @throws \Exception
     */
    public static function getMetricsForPosts(array $ids){

        if(empty($ids)) return collect();

        // latest IDs of each metric
        $latestIds = get_insights_db()->table('post_metrics')->whereIn('post_id', $ids)->selectRaw('max(`id`) as `id`')->groupBy('metric_type', 'post_id')->get()->pluck('id')->toArray();

        // fetch only the latest row for each metric_type
        return get_insights_db()->table('post_metrics')
            ->whereIn('post_id', $ids)
            ->whereIn('id', $latestIds)
            ->orderBy('id', 'desc')
            ->get([
                'post_id',
                'metric_type',
                'metric_value',
                'timestamp',
            ]);
    }

    /**
     * Get attachments
     * @return array
     */
    public function getAttachments(){
        $_attachments = [];
        if(isset($this->options['attachments'])){
            $_attachments = transform_attachments( (array) $this->options['attachments'] );
        }
        return $_attachments;
    }


    public function getPermalink(){
        if(!$this->published_at) return null;
        $network = explode('.', $this->account->type)[0];
        if ($this->account->type === "twitter.profile") {
            return (
                "https://twitter.com/" .
                $this->account->account_id .
                "/status/" .
                $this->external_id
            );
        } else if ($network === "facebook") {
            if($this->getOption('post_as_story')) return null;
            return "https://www.facebook.com/"  . $this->external_id;
        } else if ($network === "linkedin") {
            return "https://www.linkedin.com/feed/update/" . $this->external_id;
        } else if ($this->account->type === "instagram.direct") {
            return $this->external_id;
        } else if ($this->account->type === "instagram.api") {
            if ($this->getOption('post_as_story')) {
                $token = json_decode($this->account->token, true);
                $username = $token['username'];
                return (
                    "https://instagram.com/stories/" .
                    $username . "/" .
                    $this->getOption('media_id')
                );
            } else {
                return $this->external_id;
            }
        } else if ($this->account->type === "google.location") {
            return $this->result['searchUrl'];
        } else if ($this->account->type === "mastodon.profile") {
            $result = $this->result;
            return $result['url'] ?? null;
        } else if ($this->account->type === "google.youtube") {
            if($this->getOption('post_as_short')){
                return 'https://youtube.com/shorts/' . $this->external_id;
            } else {
                return 'https://youtube.com/watch?v=' . $this->external_id;
            }
        } else if($this->account->type === 'pinterest.profile'){
            return 'https://www.pinterest.com/pin/' . $this->external_id;
        } else if (in_array($this->account->type, ['reddit.profile', 'reddit.subreddit'])) {
            $result = $this->result;
            return $result['permalink'] ?? null;
        } else if($this->account->type === 'bluesky.profile'){
            $did = $this->account->account_id;
            $atUrl = $this->external_id; // "at://did:plc:immhr6uu35qoqkmxpciserh2/app.bsky.feed.post/3kyq6a7dxds26" => https://bsky.app/profile/did:plc:immhr6uu35qoqkmxpciserh2/post/3kyq6a7dxds26
            $parts = explode('.feed.post/', $atUrl);
            $rKey = $parts[1];
            // explode by did to get domain
            $domainReversed = explode($did . '/', $parts[0])[1];

            $domain = implode('.', array_reverse(explode('.', $domainReversed)));
            return 'https://' . $domain . '/profile/' . $did . '/post/' . $rKey;
        } else if($this->account->type === 'threads.profile'){
            $result = $this->result;
            return $result['permalink'] ?? null;
        }
        return null;
    }

    private function getOptions(){
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
