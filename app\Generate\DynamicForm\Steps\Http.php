<?php
namespace App\Generate\DynamicForm\Steps;
class Http extends \App\Generate\DynamicForm\Step
{
    public function validate()
    {
        $input = $this->input;
        if (!isset($input['url']) || !isset($input['method'])) {
            throw new \Exception('URL and method are required');
        }
    }

    public function execute()
    {
        $input = $this->getData();
        $headers = $input['headers'] ?? [];
        $timeout = $input['timeout'] ?? 30; 
        $client = guzzle_client(['headers' => $headers, 'timeout' => $timeout]);

        $options = [];

        $type = $input['type'] ?? 'json';

        if ($type === 'multipart' && !empty($input['data'])) {
            $options['multipart'] = [];
            foreach ($input['data'] as $name => $value) {
                if (is_array($value) && isset($value['path'])) {
                    $options['multipart'][] = [
                        'name'     => $name,
                        'contents' => fopen($value['path'], 'r'),
                        'filename' => $value['filename'] ?? basename($value['path']),
                    ];
                } else {
                    $options['multipart'][] = [
                        'name'     => $name,
                        'contents' => $value,
                    ];
                }
            }
        } elseif ($type === 'form' && !empty($input['data'])) {
            $options['form_params'] = $input['data'];
        } elseif ($type === 'json' && !empty($input['data'])) {
            $options['json'] = $input['data'];
        }

        try {
            $response = $client->request($input['method'], $input['url'], $options);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            throw $e;
        }

        $data = $response->getBody()->getContents();
        if($input['response_type'] === 'json'){
            $data = json_decode($data, true);
        } 
        $this->setOutput([
            'data' => $data,
            'headers' => $response->getHeaders(),
            'status_code' => $response->getStatusCode(),
        ]);
    }
}
