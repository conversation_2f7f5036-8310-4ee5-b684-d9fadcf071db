<template>
    <div>

        <div class="card rounded upload-wrapper" @dragover.prevent.stop="onDrag('start', $event)" @dragenter.prevent.stop="onDrag('start', $event)"
                @dragleave.prevent.stop="onDrag('end', $event)" @dragend.prevent.stop="onDrag('end', $event)"
                @drop.prevent.stop="onDrag('drop', $event)">
            <div class="card-body px-0 pt-5">
                <div class="row align-items-center">
                    <div class="col-md-12 mb-5">
                        <div class="bulk-import-container card bg-light rounded-lg text-center">
                            <div class="card-body rounded-lg">
                                <div class="pb-4 mb-1">
                                    <i class="ph ph-file-arrow-up ph-light file-icon"></i>
                                </div>
                                <p class="d-md-block d-none pb-1">Drag and drop CSV file or</p>
                                <div class="d-flex justify-content-center">
                                    <input type="file" class="d-none" ref="fileInput" @change="onFileChange" v-if="!file" />
                                    <button  class="btn btn-xs btn-primary btn-sm" @click.prevent="selectFile">
                                        Upload File
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 mb-5">
                        <ul class="list-styles pl-0">
                            <li>Download the CSV template <a class="pl-1 font-weight-500" href="/csv/content_import_sample.csv">from here</a> </li>
                            <li>Add your posts to the CSV file</li>
                            <li>Double-check your data for accuracy</li>
                            <li>Import the prepared .CSV file</li>
                            <li>You’re all set!</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="new_import" class="modal fade" role="dialog">
            <div class="modal-dialog mx-md-auto mx-0" role="document">
                <div class="modal-content">

                    <div class="d-flex align-items-center justify-content-center position-absolute h-100 w-100" >
                        <div class="card border rounded-xl import-modal">
                            <div class="card-body p-40">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status" v-if="isBusy">
                                            <span class="sr-only">Loading...</span>
                                        </div> 
                                        <div class="text-center" v-else-if="lastImport && lastImport.status === 'done'">
                                            <i class="ph ph-check-circle ph-2xl text-success"></i>
                                            <p class="lead-2 mt-5 mb-0"> {{ lastImport.count }} items were successfully imported and scheduled. Check them out <a href="/app/publish/posts">here</a>.</p> 
                                        </div> 
                                        <div v-else-if="errors && errors.length">
                                            <div class="text-danger">
                                                <div class="lead-2 mb-6" v-for="error in errors">
                                                    {{ error.message }}
                                                    <span v-if="error.row">
                                                        (row: {{ error.row }})
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else-if="data && data.length">
                                            <p class="lead-2 mb-6">{{ data.length }} items will be imported</p>
                                        </div>
                                        <div class="lead-2" 
                                            v-if="lastImport && lastImport.status === 'started'">
                                            Import in progress
                                        </div>
                                        
                                    </div>
                                    <div class="d-flex justify-content-center pt-0" v-if="!isBusy">
                                        <div v-if="!lastImport">
                                            <button class="btn btn-light btn-sm mr-2"
                                                    @click="closeModal" :disabled="isBusy">
                                                Cancel
                                            </button>
                                            <button class="btn btn-primary btn-sm"
                                                    @click="startImport" :disabled="(errors && errors.length) || isBusy">
                                                Import
                                            </button>
                                        </div>
                                        <button class="btn btn-primary btn-sm"
                                            @click="reset" v-else-if="!isBusy">
                                            Done
                                        </button>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Papa from "papaparse";
import $ from "jquery";
import { alertify, axios, axiosErrorHandler } from "../../components";

export default {
    name: "BulkUpload",
    data() {
        return {
            file: null,
            data: null,
            errors: null,
            lastImport: null,
            loading: true
        };
    },
    watch: {
        file(v) {
            if (!v) {
                // reset all fields related to file
                this.data = null;
                this.errors = null;
                this.lastImport = null;
            }
        },
        lastImport(v) {
            if (v && v.error) {
                // show error from last import
                this.errors = [new Error(v.error)];
            }
        }
    },
    computed: {
        isBusy() {
            return (this.lastImport && this.lastImport.status === "started") || this.loading;
        }
    },
    methods: {
        selectFile() {
            $(this.$refs.fileInput).click();
        },
        onFileChange(e) {
            const file = e.target.files[0];
            $("#new_import").modal({
                backdrop: "static",
                keyboard: false,
                show: true
            });
            this.setFile(file);
        },
        setFile(file) {
            //validate file
            if (!file.name.endsWith(".csv")) {
                alertify.error("Invalid file type. Please select a .csv file");
                return;
            }
            this.file = file;
            this.parseCsv();
        },
        onDrag(type, e) {
            if (type === "start") {
                $(".upload-wrapper").css("opacity", 0.6);
            } else if (type === "end" || type === "drop") {
                $(".upload-wrapper").css("opacity", 1);
            }
            if (type === "drop") {
                const file = Array.from(e.dataTransfer.files)[0];
                this.setFile(file);
                $('#new_import').modal('show');
            }
        },
        parseCsv() {
            if (!this.file) {
                return;
            }
            const _this = this;
            Papa.parse(this.file, {
                skipEmptyLines: true,
                header: true,
                transformHeader(h) {
                    return h.toLowerCase().trim();
                },
                error(err, file) {
                    _this.data = null;
                    _this.errors = [err];
                },
                complete(results) {
                    _this.errors = null;
                    _this.data = null;
                    if (results.errors.length) {
                        _this.errors = results.errors;
                    } else {
                        if (results.data.length === 0) {
                            _this.errors = [new Error("Nothing to import")];
                            return;
                        }

                        // validate data first
                        let rNo = 0;
                        const hasError = results.data.some(r => {
                            ++rNo;
                            return ["account", "account type", "publish at"].some(c => {
                                if ((!r[c] || !r[c].length) && !r['add to queue']) {
                                    _this.errors = [new Error('"' + c + '" not set for row ' + rNo)];
                                    return true;
                                }
                            });
                        });

                        if (hasError) return;

                        _this.data = results.data;
                    }
                }
            });
        },
        async startImport() {
            if (this.loading) return;
            this.loading = true;
            try {
                const fd = new FormData();
                fd.append("file", this.file);
                await axios.post("/app/publish/bulk_upload", fd);
                this.lastImport = {
                    status: "started"
                };
                this.getProgress();
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loading = false;
        },
        async getProgress() {
            this.loading = true;
            // periodically get status after an import has started
            try {
                const { data } = await axios.get("/app/publish/bulk_upload/progress");

                this.lastImport = data;
                if (this.lastImport.status && this.lastImport.status === "started") {
                    // re-fetch
                    setTimeout(this.getProgress, 5000);
                }
                this.loading = false;
            } catch (e) {
                window.alert(axiosErrorHandler(e, true) + " - retrying in 5s");
                setTimeout(this.getProgress, 5000);
            }
        },
        async reset() {
            this.loading = true;

            if(this.lastImport){
                try {
                    await axios.post("/app/publish/bulk_upload/reset");
                    this.lastImport = null;
                    $("#new_import").modal('hide');

                } catch (e) {
                    axiosErrorHandler(e);
                }
            }

            this.file = null;
            this.loading = false;

        },
        closeModal(){
            this.file = null;
            $("#new_import").modal('hide');
        }
    },
    mounted() {
        this.getProgress();
    }
};
</script>

<style lang="scss" scoped>
.bulk-import-container{
    .card-body{
        padding: 80px 40px !important;
        border: 1px dashed #D1D7E3;
    }

}
.file-icon{
    color: #657085;
    font-size: 48px;
}
.p-40{
    padding: 40px !important;
}

</style>
