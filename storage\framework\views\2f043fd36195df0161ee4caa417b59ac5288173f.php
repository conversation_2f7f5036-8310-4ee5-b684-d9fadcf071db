<?php if($errors->any()): ?>
    <div class="alert alert-danger rounded-md border-0 p-4 d-flex justify-content-between align-items-center" role="alert">
        <i class="ph ph-warning-circle ph-lg" aria-hidden="true" style="padding-top:2px;"></i>
        <ul class="list-unstyled small-2 mb-0" style="margin-left: -16px;">
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <button type="button" class="close close-button text-danger" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/partials/errors.blade.php ENDPATH**/ ?>