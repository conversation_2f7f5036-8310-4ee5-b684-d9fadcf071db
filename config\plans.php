<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Pricing Plans
    |--------------------------------------------------------------------------
    |
    | This file is for storing our pricing plans.
    |
    */

    'free' => [
        'name' => 'Free',
        'price' => 0,
        'price_yearly' => 0,
        'limits' => [
            'monthly_posts' => 40, // using this
            'teams' => 0,
            'team_members' => 0,
            'accounts' => 2,
            'feeds' => 0,
            'queues' => 0,
            'automations' => 0,
        ],
    ],

    'starter' => [
        'name' => 'Starter',
        'price' => 8,
        'price_yearly' => 8 * 10,
        'limits' => [
            'monthly_posts' => 150,
            'teams' => 1,
            'team_members' => 2,
            'accounts' => 4,
            'feeds' => 1,
            'queues' => 2,
            'automations' => 4,
        ],
        'features' => [

        ],
    ],

    'standard2' => [
        'name' => 'Standard',
        'price' => 19,
        'price_yearly' => 19 * 10,
        'limits' => [
            'monthly_posts' => 800,
            'teams' => 2,
            'team_members' => 4,
            'accounts' => 12,
            'feeds' => 5,
            'queues' => 5,
            'automations' => 16,
            'generated_content' => 100
        ],
        'features' => [
            'content_generation' => true,
        ],
    ],

    'super' => [
        'name' => 'Super',
        'price' => 59,
        'price_yearly' => 59 * 10,
        'limits' => [
            'monthly_posts' => 50000,
            'teams' => 6,
            'team_members' => 8,
            'accounts' => 30,
            'feeds' => 15,
            'queues' => 15,
            'automations' => 60,
            'generated_content' => 250,
        ],
        'features' => [
            'content_generation' => true,
            'content_autocomplete' => true,
            'curation_generate' => true,
        ],
    ],

    'supreme' => [
        'name' => 'Supreme',
        'price' => 199,
        'price_yearly' => 199 * 10,
        'limits' => [
            'monthly_posts' => 100000,
            'teams' => 25,
            'team_members' => 20,
            'accounts' => 150,
            'feeds' => 50,
            'queues' => 50,
            'automations' => 400,
            'generated_content' => 600,
        ],
        'features' => [
            'content_generation' => true,
            'content_autocomplete' => true,
            'curation_generate' => true,
        ],
    ],

    'custom' => [ // for custom plans like life-time deals and so on
        'name' => 'Custom',
        'price' => 0,
        'price_yearly' => 0,
        'limits' => [
            'monthly_posts' => 0,
            'teams' => 0,
            'team_members' => 0,
            'accounts' => 0,
            'feeds' => 0,
            'queues' => 0,
            'automations' => 0,
            'generated_content' => 0,
        ],
        'features' => [
            'content_generation' => false,
            'content_autocomplete' => false,
        ],
    ],

    // old plans
    'basic' => [
        'name' => 'Basic',
        'price' => 4.99,
        'price_yearly' => 4.99 * 10,
        'limits' => [
            'monthly_posts' => 150,
            'teams' => 1,
            'team_members' => 3,
            'accounts' => 4,
            'feeds' => 1,
            'queues' => 0,
            'automations' => 4,
        ],
    ],
    'standard' => [
        'name' => 'Standard',
        'price' => 12,
        'price_yearly' => 12 * 10,
        'limits' => [
            'monthly_posts' => 800,
            'teams' => 3,
            'team_members' => 5,
            'accounts' => 10,
            'feeds' => 4,
            'queues' => 0,
            'automations' => 15,
        ],
        'features' => [
        ],
    ],
    'plus' => [
        'name' => 'Plus',
        'price' => 49,
        'price_yearly' => 49 * 10,
        'limits' => [
            'monthly_posts' => 50000,
            'teams' => 8,
            'team_members' => 20,
            'accounts' => 25,
            'feeds' => 10,
            'queues' => 0,
            'automations' => 40,
        ],
    ],
    'plus-ultra' => [
        'name' => 'Plus Ultra',
        'price' => 199,
        'price_yearly' => 199 * 10,
        'limits' => [
            'monthly_posts' => 50000,
            'teams' => 20,
            'team_members' => 40,
            'accounts' => 150,
            'feeds' => 30,
            'queues' => 0,
            'automations' => 200,
        ],
    ],
];
