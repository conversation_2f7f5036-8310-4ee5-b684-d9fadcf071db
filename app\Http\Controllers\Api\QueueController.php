<?php

namespace App\Http\Controllers\Api;

use App\Account;
use App\Http\Controllers\Controller;
use App\PublishQueue;
use App\User;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class QueueController extends Controller
{
    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response|void
     * @throws FileNotFoundException
     */
    public function addPost($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queueIds = is_array($id) ? $id : [$id];

        foreach ($queueIds as $queueId){
            $queue = PublishQueue::available()->firstWhere('id', $queueId);
            if(!$queue){
                abort(404, 'Queue not found.');
            }

            if($queue->team){
                if(!$queue->team->hasPermission(user(), 'queues.add_post')){
                    abort(400, 'You do not have permission to add content to queue');
                }
            }

        }
        if(empty($request->input('accounts', []))) {
            abort(400, 'No selected accounts found, please try again.');
        }
        // normalize attachments
        $attachments = PostsController::getAttachmentsFromRequest($request);

        /** @var User $user */
        $user = \Auth::user();

        /** @var Account[]|Collection $accounts */
        $accounts = $user->getAvailableAccounts()->whereIn('id', $request->input('accounts'));

        $all_options = $request->input('options', []);

        // validate for each account
        foreach ($accounts as $account){
            $options = isset($all_options[$account->id]) ? $all_options[$account->id] : $all_options;
            try {
                $account->publishPost([
                    'content' => $request->input('content'),
                    'attachments' => (array) $attachments,
                    'options' => $options,
                ], now(), true);
            } catch (\Exception $e) {
                if ($e instanceof ValidationException) {
                    throw $e;
                }
                abort(400, $e->getMessage());
            }
        }
        foreach ($queueIds as $queueId){
            $queue = PublishQueue::available()->firstWhere('id', $queueId);

            $queue->addItem([
                'content' => $request->input('content'),
                'attachments' => (array) $attachments,
                'options' => $request->input('options', []),
            ]);
        }

        return response()->noContent();
    }
}
