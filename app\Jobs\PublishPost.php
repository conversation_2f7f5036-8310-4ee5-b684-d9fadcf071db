<?php

namespace App\Jobs;

use App\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class PublishPost implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $post;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 900;

    /**
     * Create a new job instance.
     * @param Post $post
     */
    public function __construct(Post $post)
    {
        $this->post = $post;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        $concurrencyKey = 'publish_post::a_' . $this->post->account_id;

        // for pinterest, we need to use a key based on user ID, because multiple accounts can be linked to the same user
        // and because we can't risk for pinterest
        if($this->post->account->type === 'pinterest.profile'){
            $concurrencyKey = 'publish_post::u_' . $this->post->user_id;
        }

        \Redis::funnel($concurrencyKey)
            ->releaseAfter(60 * 5) // 5 minutes
            ->limit(1)
            ->then(function () {
                // mark the account as publishing so no other publishing job runs in parallel for this account
                if ($this->post->user && $this->post->user->getOption('debug'))
                    \Log::info('Starting to publish post for account: ' . $this->post->account_id, ['post_id' => $this->post->id]);
                $this->post->publish(true);
            }, function () {
                // Could not obtain lock...
                // do nothing: cron will pick it up next time
            });
    }
}
