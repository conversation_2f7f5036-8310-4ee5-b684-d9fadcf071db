<?php
/**
 * Created by PhpStorm.
 * User: dell
 * Date: 2/15/2019
 * Time: 1:05 AM
 */

namespace App\Helpers;


use App\Jobs\SendCrmEvent;
use App\Jobs\SyncEmailListContact;
use App\User;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class EmailListHelper
{

    static public $TAG_PAID = 3;
    static public $TAG_TRIAL = 4;
    static public $TAG_FREE = 2;
    static public $TAG_DEAL = 7;
    static public $TAG_DELETED = 6;
    static public $TAG_CANCELLED = 5;
    static public $TAG_PENDING_CANCELLATION = 8;

    static public $FIELD_PLAN_NAME = 1;
    static public $FIELD_BILLING_CYCLE = 2;
    static public $FIELD_COMPANY = 3;
    static public $FIELD_CUSTOMER_ID = 4;
    static public $FIELD_SUBSCRIPTION_ID = 5;
    static public $FIELD_COUNTRY = 6;
    static public $FIELD_IP = 7;
    static public $FIELD_JOINED_TEAMS = 8;
    static public $FIELD_PERSONA = 9;
    static public $FIELD_ACQUISITION_CHANNEL = 10;
    

    static private $instance = null;
    static public $LIST = 1;

    static public $CONTACT_ID_KEY = 'activecampaign_contact_id';

    /** @var Client $client */
    private $client = null;

    private $apiKey = '9b35a97964924db27034f07d4f52e531fcd2ecbe082551a80b1c8baccf128e9faf9e1325';

    /*
     * Get instance
     */
    static public function getInstance(){
        if(!self::$instance){
            self::$instance = new self();
        }
        return self::$instance;
    }

    function __construct()
    {
        $this->client = $client = new Client([
            // Base URI is used with relative requests
            'base_uri' => 'https://osamaejaz1.api-us1.com',
            // You can set any number of default request options.
            'timeout'  => 100,
            'connect_timeout' => 60,
            'read_timeout' => 30,
        ]);
    }

    protected function getClient(){
        return $this->client;
    }

    protected function getApiKey(){
        return $this->apiKey;
    }

    /**
     * @param string $email
     * @return bool
     * @throws \Exception
     */
    public function subscribeToBlog(string $email){
        $res = null;
        try {
            $response = $this->client->post('/admin/api.php?api_key=' . $this->apiKey . '&api_action=contact_sync&api_output=json', [
                RequestOptions::FORM_PARAMS => array_filter([
                    'email' => $email,
                    'p[2]' => '2', // list id
                    'form' => 1,
                ])
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::error($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res->result_code )
            return true;

        \Log::error('Unexpected response from ActiveCampaign: ' . @json_encode($res));

        throw new \Exception(isset($res->result_message) ? $res->result_message : 'List error');
    }

    /**
     * @param $user - This is the user object or null when that user is deleted
     * @param $event
     * @param $val
     * @param bool $really
     * @return bool|void
     */
    public function sendEvent($user, $event, $val = null, bool $really = false){

        if(!app()->environment('production') || session()->has('support_login')){
            return true;
        }

        if(!$user){
            // user not found, may be deleted
            return;
        }

        if($really){
            $this->getClient()->post('https://trackcmp.net/event', [
                RequestOptions::FORM_PARAMS => [
                    "actid" => "649547255",
                    "key" => "ec1d8648bae6290c8f07c0ec38ad6ee31e8b308e",
                    "event" => $event,
                    "eventdata" => is_string($val) ? $val : json_encode($val),
                    "visit" => json_encode([
                        // If you have an email address, assign it here.
                        "email" => $user->email,
                    ]),
                ],
            ]);


            // now send data to mixpanel
            guzzle_client()->post('https://api.mixpanel.com/import?strict=1&project_id=3098239', [
                // basic auth
                'auth' => [
                    'fd89c0aa124955530b44895ff5d7d4b7',
                    '',
                ],
                'json' => [
                    [
                        'event' => $event,
                        'properties' => array_merge([
                            'distinct_id' => $user->id . '',
                            'time' =>time(),
                            '$insert_id' => Str::uuid(),
                        ], is_array($val) ? $val : []),
                    ]
                ]
            ]);

        } else {
            dispatch( (new SendCrmEvent($user, $event, $val)) );
        }
    }

    /**
     * @param $user
     * @param $really
     * @return bool
     * @throws \Exception
     * @deprecated see syncUserContact instead
     */
    public function syncContact($user, $really = false){
        return $this->syncUserContact($user, $really);
    }

    /**
     * Sync all users - warning: only use from cli
     * @param int $delay seconds to delay for each user sync
     */
    public function syncAllUsers($delay = 1){
        User::withTrashed()->chunk(200, function ($users /** @var User[]|Collection $users */) use($delay){
            echo 'Syncing chunk of ' . count($users) . ' users' . "\r\n";
            $users->each(function($user /** @var User $user */ ) use($delay){
                // sync with api
                try {
                    $this->syncUserContact($user, true);
                } catch (\Exception $exception){
                    if($exception instanceof ClientException || $exception instanceof ServerException){
                        \Log::info($exception->getRequest()->getMethod() . ': ' . $exception->getRequest()->getUri() . ' - ' . $exception->getRequest()->getBody()->getContents());
                        \Log::info($exception->getResponse()->getBody());
                    }
                    \Log::info('sync ' . $user->email . ' failed');
                    throw $exception;
                }
                if($delay !== null && $delay !== 0) {
                    usleep($delay * 1000000);
                }
            });
        });

        // TODO: delete contacts in activecampaign which are not in db

    }

    public function syncToMixpanel(User $user){
        $client = guzzle_client();

        $sub = $user->getSubscription();

        $client->post('https://api.mixpanel.com/engage#profile-set', [
            'auth' => [
                'fd89c0aa124955530b44895ff5d7d4b7',
                '',
            ],
            'json' => [
                [
                    '$token' => 'fd89c0aa124955530b44895ff5d7d4b7',
                    '$distinct_id' => $user->id . '',
                    '$set' => [
                        // standard
                        '$name' => $user->name,
                        '$email' => $user->email,
                        '$phone' => $user->getPhone(),
                        '$created' => $user->created_at->timestamp,
                        '$avatar' => $user->getPic(),
                        '$country_code' => $user->getOption('last_location.country'),
                        '$timezone' => $user->getTimezone(),

                        // custom
                        'plan_name' => $user->getPlan()['name'],
                        'billing_cycle' => value(function() use($user, $sub){
                            if($sub){
                                return $user->planIsYearly() ? 'Yearly' : 'Monthly';
                            }
                            return null;
                        }),
                        'company' => $user->company,
                        'stripe_customer_id' => $user->stripe_id,
                        'stripe_subscription_id' => value(function() use($sub, $user){
                            if($sub && !$user->trashed()){
                                try {
                                    return $sub->asStripeSubscription()->id;
                                } catch (\Exception $exception){ }
                            }
                            return null;
                        }),
                        'persona' => $user->getOption('persona', ''),
                        'joined_teams' => $user->joinedTeams()->count(),
                        'acquisition_channel' => $user->getOption('acquisition_channel', ''),
                    ],
                ]
            ]
        ]);
    }

    /**
     * @param User $user
     * @param bool $really
     * @return bool
     * @throws \Exception
     */
    public function syncUserContact(User $user, $really = false){

        if( !app()->environment('production') || session()->has('support_login') ){
            return true;
        }

        if(!$really){
            // dispatch it to be done later
            dispatch((new SyncEmailListContact($user)));
            return true;
        }

        // we only sync if user is verified
        if(!$user->verified){
            return true;
        }

        $this->syncToMixpanel($user);

        $existingContact = $this->getUserContact($user);

        // create if it doesn't exist
        $maxRetries = 3;
        $attempt = 0;
        while (!$existingContact && $attempt < $maxRetries) {
            try {
                $existingContact = $this->addUserContact($user);
            } catch (\Exception $e) {
                $attempt++;
                if ($attempt >= $maxRetries) {
                    throw new \Exception('Failed to create contact.');
                }
                sleep(5); 
            }
        }
        
        if (!$existingContact) {
            throw new \Exception('Failed to create contact');
        }

        $contactId = $existingContact->id;

        if($user->getOption(self::$CONTACT_ID_KEY) !== $contactId) {
            // save in user if needed
            $user->setOption(self::$CONTACT_ID_KEY, $contactId);
        }


        // fix/change email if needed
        if($existingContact && strtolower($user->email) !== strtolower($existingContact->email)){
            // make sure no contact exists with the target email already
            try {
                $this->deleteContactByEmail($user->email); // just to be sure
            } catch (\Exception $exception){}
        }

        // sync contact by id
        $existingContact = $this->addUserContact($user, $contactId);

        $contactId = $existingContact->id; // update just to be sure, in cases where a contact with contactId is deleted and re created

        $tagsToAdd = $this->getTagsToAdd($user);

        // subscribe to list
        $this->subscribeContactToList($contactId);

        $contactTags = collect($this->getContactTags($contactId));

        $curContactTagIds = $contactTags->map(function ($ct){
            return (int) $ct->tag;
        })->toArray();

        foreach ($curContactTagIds as $tagId){
            if(!in_array($tagId, $tagsToAdd)){
                // delete this tag
                $contactTagId = $contactTags->firstWhere('tag', $tagId)->id;
                $this->deleteContactTag($contactTagId);
            }
        }

        foreach ($tagsToAdd as $tagId){
            if(!in_array($tagId, $curContactTagIds)){
                // add this tag
                $this->addTagToContact($contactId, $tagId);
            }
        }

        return true;
    }

    /**
     * @param User $user
     * @return object
     * @throws \Exception
     */
    public function getUserContact(User $user){

        $contactId = $user->getOption(self::$CONTACT_ID_KEY);

        if($contactId) {
            return $this->getContactById($contactId);
        } else {
            $old_emails = (array)$user->getOption('internal.pending_email_change');

            foreach ($old_emails as $email) {
                // find contact id by old email
                try {
                    $contactByOldEmail = $this->getContactByEmail($email);
                    if ($contactByOldEmail) {
                        return $contactByOldEmail;
                    }
                } catch (\Exception $exception) {
                    report($exception);
                }
            }

            return $this->getContactByEmail($user->email);
        }

    }

    /**
     * @param $email
     * @return object|null
     * @throws \Exception
     */
    public function getContactByEmail($email){
        $res = null;
        try {

            $response = $this->client->get('/api/3/contacts?email=' . urlencode($email), [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ]
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::error($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res->contacts && !empty($res->contacts) ) {
            return $res->contacts[0];
        } else if(empty($res->contacts)){
            return null;
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }

    /**
     * @throws \Exception
     */
    public function saveDemoForm($data)
    {
        $contact = $this->getContactByEmail($data['email']);
        if (!$contact) {
            $contactData = [
                'contact' => [
                    'email' => $data['email'],
                    'firstName' => $data['first_name'],
                    'lastName' => $data['last_name'],

                    'fieldValues' => [
                        [
                            'field' => self::$FIELD_COMPANY,
                            'value' => $data['where_do_you_work'],
                        ],
                        [
                            'field' => self::$FIELD_PERSONA,
                            'value' => $data['what_do_you_do'],
                        ],
                    ],
                ],
            ];
            try {
                $response = $this->client->post('/api/3/contacts', [
                    'headers' => [
                        'Api-Token' => $this->apiKey
                    ],
                    'json' => $contactData // Send contact data as JSON
                ]);
                // Parse the response
                $res = json_decode($response->getBody(), true);
            } catch (\Exception $e) {
                \Log::error('Error creating contact: ' . $e->getMessage());
                throw $e;
            }

            // Add the contact to the list
            $contactId = $res['contact']['id'];
            $contactListData = [
                'contactList' => [
                    'list' => 3, 
                    'contact' => $contactId,
                    'status' => 1 
                ],
            ];

            try {
                $response = $this->client->post('/api/3/contactLists', [
                    'headers' => [
                        'Api-Token' => $this->apiKey
                    ],
                    'json' => $contactListData
                ]);

                $listRes =  json_decode($response->getBody(), true);

            } catch (\Exception $e) {
                \Log::error('Error adding contact to list: ' . $e->getMessage());
                throw $e;
            }
            //Add note for contact 
            $noteData = [
                'note' => [
                    "note" => 'Scheduled demo call details:' . ' • ' .
                        'Email Address: '   . $data['email'] . ' • ' .
                        'First Name: ' . $data['first_name'] . ' • ' .
                        'Last Name: ' . $data['last_name'] . ' • ' .
                        'Where do you work: ' . $data['where_do_you_work'] . ' • ' .
                        'What do you do: ' . $data['what_do_you_do'] . ' • ' .
                        'How can we help: ' . $data['how_can_we_help'],
                    "relid" => $contactId,
                    "reltype" => "Subscriber"
                ]
            ];

            try {
                $responseNote = $this->client->post('/api/3/notes', [
                    'headers' => [
                        'Api-Token' => $this->apiKey,
                    ],
                    'json' => $noteData
                ]);
                $noteRes = json_decode($responseNote->getBody(), true);
            } catch (\Exception $e) {
                \Log::error('Error adding note: ' . $e->getMessage());
                throw $e;
            }

            $contactTagData = [
                'contactTag' => [
                    'contact' => $contactId,
                    'tag' => 21
                ]
            ];
         try{
            $responseTag = $this->client->post('/api/3/contactTags', [
                'headers' => [
                    'Api-token' => $this->apiKey,
                ],
                'json' => $contactTagData
            ]);
            $tagsData = json_decode($responseNote->getBody(), true);
         } catch (\Exception $e) {
            \Log::error('Error adding note: ' . $e->getMessage());
            throw $e;
        }
        } else {
            $contactId = $contact->id;
            // Add tag contact 
            $contactTagData = [
                'contactTag' => [
                    'contact' => $contactId,
                    'tag' => 21
                ]
            ];
         try{
            $responseTag = $this->client->post('/api/3/contactTags', [
                'headers' => [
                    'Api-token' => $this->apiKey,
                ],
                'json' => $contactTagData
            ]);
            $tagsData = json_decode($responseTag->getBody(), true);
         } catch (\Exception $e) {
            \Log::error('Error adding note: ' . $e->getMessage());
            throw $e;
        }

        // Add the contact to the list
        $contactListData = [
            'contactList' => [
                'list' => 3, 
                'contact' => $contactId,
                'status' => 1 
            ],
        ];

        try {
            $response = $this->client->post('/api/3/contactLists', [
                'headers' => [
                    'Api-Token' => $this->apiKey
                ],
                'json' => $contactListData
            ]);

            $listRes =  json_decode($response->getBody(), true);

        } catch (\Exception $e) {
            \Log::error('Error adding contact to list: ' . $e->getMessage());
            throw $e;
        }
        //Add note for contact
            $noteData = [
                'note' => [
                    "note" => 'Scheduled demo call details:' . ' • ' .
                        'Email Address: '   . $data['email'] . ' • ' .
                        'First Name: ' . $data['first_name'] . ' • ' .
                        'Last Name: ' . $data['last_name'] . ' • ' .
                        'Where do you work: ' . $data['where_do_you_work'] . ' • ' .
                        'What do you do: ' . $data['what_do_you_do'] . ' • ' .
                        'How can we help: ' . $data['how_can_we_help'],
                    "relid" => $contactId,
                    "reltype" => "Subscriber"
                ],
            ];

            try {
                $responseNote = $this->client->post('/api/3/notes', [
                    'headers' => [
                        'Api-Token' => $this->apiKey,
                    ],
                    'json' => $noteData
                ]);
                $noteRes = json_decode($responseNote->getBody(), true);
            } catch (\Exception $e) {
                \Log::error('Error adding note: ' . $e->getMessage());
                throw $e;
            }
        }
}


    /**
     * @param $id
     * @return object|null
     * @throws \Exception
     */
    public function getContactById($id){
        $res = null;
        try {

            $response = $this->client->get('/api/3/contacts/' . urlencode($id), [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ]
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = $exception->getResponse();
            if($res){
                if($res->getStatusCode() === 404){
                    // not found
                    return null;
                }
            }
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( isset($res->contact) ) {
            return $res->contact;
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }

    /**
     * @return object|null
     * @throws \Exception
     */
    public function getCustomFields(){
        $res = null;
        try {

            $response = $this->client->get('/api/3/fields?limit=100', [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ]
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        return $res;
    }

    /**
     * @param User $user
     * @return bool
     * @throws \Exception
     */
    public function deleteUserContact(User $user){
        return $this->deleteContactByEmail($user->email);
    }

    /**
     * @param $email
     * @return bool
     * @throws \Exception
     */
    public function deleteContactByEmail($email){

        $contact = $this->getContactByEmail($email);

        if(!$contact){
            return false; // probably already deleted/notfound
        }

        $res = null;
        try {

            $this->client->delete('/api/3/contacts/' . $contact->id, [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ]
            ]);
            return true;
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res && $res->message ) {
            throw new \Exception($res->message);
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }


    /**
     * @param User $user
     * @param null $contactId
     * @param bool $try
     * @return object
     * @throws \Exception
     */
    public function addUserContact(User $user, $contactId = null, $try = false){

        $res = null;

        $name_parts = explode(' ', $user->name);
        $fname = $name_parts[0];
        $lname = null;
        if(count($name_parts) === 2){
            $lname = $name_parts[1];
        } else if(count($name_parts) > 2){
            unset($name_parts[0]);
            $lname = implode(' ', $name_parts);
        }

        $sub = $user->getSubscription();

        $contactJson = array_filter([
            'email' => $user->email,
            'firstName' => $fname,
            'lastName' => $lname,
            'phone' => $user->getPhone(),
            'fieldValues' => [
                [
                    'field' => self::$FIELD_PLAN_NAME,
                    'value' => $user->getPlan()['name'],
                ],
                [
                    'field' => self::$FIELD_BILLING_CYCLE,
                    'value' => value(function() use($user, $sub){
                        if($sub){
                            return $user->planIsYearly() ? 'Yearly' : 'Monthly';
                        }
                        return null;
                    }),
                ],
                [
                    'field' => self::$FIELD_COMPANY,
                    'value' => $user->company,
                ],
                [
                    'field' => self::$FIELD_CUSTOMER_ID,
                    'value' => $user->stripe_id,
                ],
                [
                    'field' => self::$FIELD_SUBSCRIPTION_ID,
                    'value' => value(function() use($sub, $user){
                        if($sub && !$user->trashed()){
                            try {
                                return $sub->asStripeSubscription()->id;
                            } catch (\Exception $exception){

                            }
                        }
                        return null;
                    }),
                ],
                [
                    'field' => self::$FIELD_COUNTRY,
                    'value' => $user->getOption('last_location.country'),
                ],
                [
                    'field' => self::$FIELD_IP,
                    'value' => $user->getOption('last_location.ip'),
                ],
                [
                    'field' => self::$FIELD_JOINED_TEAMS,
                    'value' => $user->joinedTeams()->count(),
                ],
                [
                    'field' => self::$FIELD_PERSONA,
                    'value' => $user->getOption('persona', ''),
                ],
                [
                    'field' => self::$FIELD_ACQUISITION_CHANNEL,
                    'value' => $user->getOption('acquisition_channel', ''),
                ],
            ],
        ]);

        try {
            if(!$contactId) {
                $response = $this->client->post('/api/3/contacts', [
                    RequestOptions::JSON => [
                        'contact' => $contactJson,
                    ],
                    RequestOptions::HEADERS => [
                        'Api-Token' => $this->apiKey,
                    ],
                ]);
            } else {

                $response = $this->client->put('/api/3/contacts/' . $contactId, [
                    RequestOptions::JSON => [
                        'contact' => $contactJson,
                    ],
                    RequestOptions::HEADERS => [
                        'Api-Token' => $this->apiKey,
                    ],
                ]);
            }
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {

            $httpRes = $exception->getResponse();
            if(!$try && $httpRes && $httpRes->getStatusCode() === 404){
                // not found, so we force recreate
                // only needed to handle unexpected deleted contacts
                return $this->addUserContact($user, null, true);
            }

            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res && isset($res->contact) ) {

            // set id in options
            if($user->getOption(self::$CONTACT_ID_KEY) !== $res->contact->id) {
                $user->setOption(self::$CONTACT_ID_KEY, $res->contact->id);
            }

            return $res->contact;
        } else {
            if(isset($res->errors) && count($res->errors) > 0){
                // something went wrong
                $error = $res->errors[0];
                if($error->code === 'duplicate'){
                    // already exists, so we just return the existing contact
                    return $this->getUserContact($user);
                }
            }
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }

    /**
     * @param $contactId
     * @param $tagId
     * @return object
     * @throws \Exception
     */
    public function addTagToContact($contactId, $tagId){

        $res = null;

        try {

            $response = $this->client->post('/api/3/contactTags', [
                RequestOptions::JSON => [
                    'contactTag' => array_filter([
                        'contact' => $contactId,
                        'tag' => $tagId,
                    ]),
                ],
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ],
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res && isset($res->contactTag) ) {
            return $res->contactTag;
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }

    /**
     * @param $contactTagId
     * @return boolean
     * @throws \Exception
     */
    public function deleteContactTag($contactTagId){

        $res = null;

        try {

            $this->client->delete('/api/3/contactTags/' . $contactTagId, [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ],
            ]);

            return true;
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        if( $res && $res->message ) {
            if(Str::contains(strtolower($res->message), 'no result')){
                // assume its already done
                return true;
            }
            throw new \Exception($res->message);
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }


    /**
     * @param $contactId
     * @return bool
     * @throws \Exception
     */
    public function subscribeContactToList($contactId){

        $res = null;

        try {

            $response = $this->client->post('/api/3/contactLists', [
                RequestOptions::JSON => [
                    'contactList' => array_filter([
                        'list' => self::$LIST,
                        'contact' => $contactId,
                        'status' => 1,
                    ]),
                ],
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ],
            ]);
            $res = json_decode($response->getBody());
            return true;
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }
            throw $exception;
        }

        \Log::info(json_encode($res));
        throw new \Exception('Invalid response.');

    }

    /**
     * @param int $id
     * @return array
     * @throws \Exception
     */
    public function getContactTags(int $id){

        $res = null;
        try {

            $response = $this->client->get('/api/3/contacts/' . $id . '/contactTags', [
                RequestOptions::HEADERS => [
                    'Api-Token' => $this->apiKey,
                ]
            ]);
            $res = json_decode($response->getBody());
        } catch (ClientException $exception) {
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (ServerException $exception){
            $res = @json_decode($exception->getResponse()->getBody());
            if(!$res){
                // something serious
                throw $exception;
            }
        } catch (\Exception $exception){
            if($exception instanceof ClientException || $exception instanceof ServerException){
                \Log::info($exception->getResponse()->getBody());
            }

            throw $exception;
        }

        if( isset($res->contactTags) ){
            return $res->contactTags;
        } else {
            \Log::info(json_encode($res));
            throw new \Exception('Invalid response.');
        }
    }

    /**
     * @param User $user
     * @return array Ids of tags
     */
    public function getTagsToAdd(User $user)
    {

        $tags = [];

        if($user->trashed()){
            $tags[] = self::$TAG_DELETED;
        } else {

            if($user->onTrial()){
                $tags[] = self::$TAG_TRIAL;
            } elseif($user->getPlan(true) !== 'free'){
                $tags[] = self::$TAG_PAID;

                // check if its a deal user
                if($user->getActiveDeal()){
                    $tags[] = self::$TAG_DEAL;
                }

            } else {
                if($user->getPlan(true) === 'free'){
                    $tags[] = self::$TAG_FREE;
                }
                if(!$user->subscribed() && $user->subscriptions()->exists()){
                    $tags[] = self::$TAG_CANCELLED;
                }
            }

            $sub = $user->subscription();
            if($sub && $sub->onGracePeriod()){
                $tags[] = self::$TAG_PENDING_CANCELLATION;
            }

        }
        return $tags;
    }
}
