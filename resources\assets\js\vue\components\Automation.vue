<template>
    <div class="row">
        <div class="col-12">
            <div class="text-center" v-if="!loaded">
                <div class="alert alert-danger" v-if="error">{{ error }}</div>
                <div class="alert text-muted" v-else>
                    <i class="ph ph-circle-notch ph-spin  ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                </div>
            </div>
            <div class="row rounded-md" v-else>
                <div class="col-12 mb-3">
                    <div class="row">
                        <div class="col-md-9 col-12">
                            <input title="What will this automation do?" type="text" class="input-seamless automation-title-input h-100 border-none form-control font-weight-700 px-0 py-0 mb-0 text-dark" style="padding-left: 0px !important;" v-model="description" placeholder="What will this automation do?" />
                        </div>
                        <div class="col-md-3 col-12 d-none d-sm-flex align-items-center justify-content-end">
                            <button type="button" class="btn btn-outline-secondary btn-sm mr-2" @click="goBack">
                                <i class="ph ph-arrow-left"></i>
                                Back
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" title="Show Log"
                                v-tooltip @click="showLog">
                                Show Log
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-12 mb-4 pb-1" v-if="team">
                    <h5 class="mb-0">
                        <i class="ph ph-users ph-lg"></i>
                        Team: {{ team.name }} | Created by: {{ created_by }}
                    </h5>
                </div>

                <div class="col-12">
                    <div class="row">
                        <div class="col-12">
                            <div ref="eventOptionsContainer">
                                <h5>When</h5>
                                <div class="row mb-5 pb-md-0 pb-4">
                                    <div class="col-12 col-md-5 pr-md-2 mb-md-0 mb-3"
                                        v-tour:atmvue_select_event="'Every Automation is connected to an event. When that event occurs, the Automation gets executed.'">
                                        <select title="Select an event" class="form-control" required
                                                v-model="event" v-selectpicker>
                                            <option
                                                    v-for="event in availableEvents" :value="event.type" v-if="!event.type.split('.')[1]">
                                                {{ event.description }}
                                            </option>
                                            <optgroup label="Facebook">
                                                <option
                                                        v-for="event in availableEvents" :value="event.type" v-if="event.type.split('.')[0] === 'facebook'">
                                                    {{ event.description }}
                                                </option>
                                            </optgroup>
                                            <optgroup label="Twitter">
                                                <option
                                                        v-for="event in availableEvents" :value="event.type" v-if="event.type.split('.')[0] === 'twitter'">
                                                    {{ event.description }}
                                                </option>
                                            </optgroup>
                                            <optgroup label="Instagram">
                                                <option
                                                        v-for="event in availableEvents" :value="event.type" v-if="event.type.split('.')[0] === 'instagram'">
                                                    {{ event.description }}
                                                </option>
                                            </optgroup>
                                        </select>
                                    </div>
                                    <div class="col-12 col-md-5 pl-md-2" :class="{'has-error has-feedback': showErrors && eventInputHasError()}"
                                         v-tour:atmvue_event_opts="'Many events have further options'"
                                         v-if="event && availableEvents[event] && availableEvents[event].options">
                                        <div v-if="availableEvents[event].options.indexOf('url') > -1">
                                            <input title="URL" class="form-control" type="url" required
                                                   v-model="event_data.url" :placeholder="event === 'rss_new_item' ? 'RSS Feed URL' : 'URL'" v-focus />
                                            <i class="ph ph-x ph-md form-control-feedback"
                                               v-if="showErrors && eventInputHasError()"></i>
                                        </div>
                                        <div v-if="availableEvents[event].options.indexOf('webhook_url') > -1">
                                            <input class="form-control" title="Select URL and copy" type="text" readonly onclick="this.select()" required
                                                   :value="webhook_url" v-tooltip v-focus />
                                            <i class="ph ph-x ph-md form-control-feedback"
                                               v-if="showErrors && eventInputHasError()"></i>
                                        </div>
                                        <div v-if="availableEvents[event].options.indexOf('account') > -1">
                                            <div>
                                                <select class="form-control w-100" id="filterAccount" required v-model.number="event_data.account" title="Select account" v-selectpicker>
                                                    <optgroup v-for="(network, i) in filterNetworks" :label='network.title' :key="network.title + '_' + i">
                                                        <option v-for="account in getAccountsByType(network.type)" :key="network.type + account.id" :data-content="`<img class='border rounded-circle border-white position-relative network-icon' src='${getIconForAccount(account.type, 'circular')}'><span class='p-2'>${account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name}</span>`" :value="account.id"></option>
                                                    </optgroup>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="ml-4" v-if="['account_post'].includes(event)">
                                        <div v-if="event === 'account_post'">
                                            This works only for posts published using SocialBu.
                                        </div>
                                        
                                    </small>
                                </div>
                                
                            </div>
                            <div class="mb-5 pb-md-0 pb-4" v-if="event && availableEvents[event]">
                                <div class="d-flex flex-md-row flex-column justify-content-between align-items-md-center align-items-start mb-5">
                                    <h5 class="font-weight-500 mb-md-0 mb-3">Perform these actions</h5>
                                    <a href="#" class="btn btn-sm btn-outline-secondary"
                                       v-tour:atmvue_placeholders="'Every Automation has data that you can usually use in your actions. This data is available through the data placeholders. <br/><br/> These data placeholders also depend on the event you select. Different events will have different data.'"
                                       @click.prevent="openPlaceholdersModal()">
                                        Data Placeholders
                                    </a>
                                </div>
                                <div class="mb-3 _actionGroup"
                                     v-tour:atmvue_actions="'When this Automation executes, you can set multiple actions to be performed.<br/><br/>These actions are grouped and you always have at-least 1 action group.'"
                                     v-for="(actionGroup, agIndex) in rules">
                                        
                                        <div class="_actions mb-5 position-relative" v-sortable="{onSort: e=>orderActions(agIndex, e) , handle: '.drag-handle', draggable: '._action'}">
                                            <div class="p-4 rounded-md bg-light">
                                                <div class="d-flex justify-content-end">
                                                    <a role="button" title="Delete action group" class="btn btn-outline-light p-2"
                                                        v-if="rules.length > 1" @click="deleteActionGroup(agIndex)" v-tooltip>
                                                            <i class="ph ph-trash ph-md"></i>
                                                    </a>
                                                </div>
                                                <div class="item _action border-bottom-0 pb-0 mb-4 bg-none"
                                                    v-for="(action, index) in actionGroup.actions">
                                                    <div class="row">
                                                        <div class="d-flex align-items-center flex-column pl-2 pr-4" v-if="actionGroup.actions.length > 1">
                                                            <i class="icon ph ph-trash ph-md mb-3" title="Delete" @click="deleteAction(agIndex, index)" v-tooltip></i>
                                                            <i title="Reorder" class="icon ph ph-dots-six ph-bold ph-lg drag-handle"></i>
                                                        </div>
                                                        <div class="col-md-8 col-12">
                                                            <div class="row action-options">
                                                                <div class="col-12 col-md-6 action-option px-2 pb-2">

                                                                    <select title="Select action" class="form-control"
                                                                            v-model="action.type" v-selectpicker @change="initActionData(action.type, agIndex, index)">
                                                                        <option v-for="action in availableActions" :value="action.type">
                                                                            {{ action.description }}
                                                                        </option>
                                                                    </select>

                                                                </div>
                                                                <template
                                                                        v-if="availableActions[action.type] && availableActions[action.type].input">
                                                                    <template
                                                                            v-for="(input, inpIndex) in availableActions[action.type].input" v-if="['image'].indexOf(input.type) === -1">
                                                                        <div class="clearfix col-12" v-if="input.newline"></div>
                                                                        <div
                                                                        :class="'action-option col-12 mb-2 px-2'  + (input.hide ? ' d-none' : '') + ' col-md-' + (input.width ? input.width : 6) + (showErrors && actionInputHasError(input, action.data[input.name]) ? ' has-error has-feedback': '')">
                                                                            <div v-if="input.type === 'text'">

                                                                                <!-- check box for setting custom timestamp -->
                                                                                <div class="form-check pb-2" v-if="!input.required && input.timestamp">

                                                                                    <input :id="'checkbox_action_' + agIndex + '_' + index + '_' + input.name" type="checkbox" class="form-check-input" v-model.boolean="action.data[input.name + '_show']" title="Show / Hide Timestamp"
                                                                                        @change="!$event.target.checked ? (action.data[input.name] = '') : ''"
                                                                                    />
                                                                                    <label class="form-check-label"
                                                                                        :for="'checkbox_action_' + agIndex + '_' + index + '_' + input.name">
                                                                                        {{ input.timestampName || 'Set timestamp' }}
                                                                                    </label>
                                                                                </div>

                                                                                <input type="text" class="mb-2" :class="'form-control' + (input.timestamp && !action.data[input.name + '_show'] ? ' d-none' : '')"
                                                                                    :required="input.required" :placeholder="input.description || input.name" :title="input.description || input.name" v-model.trim="action.data[input.name]"/>
                                                                                <i class="ph ph-x ph-md form-control-feedback" :title="actionInputHasError(input, action.data[input.name])"
                                                                                   v-tooltip v-if="!input.timestamp && showErrors && actionInputHasError(input, action.data[input.name])"></i>
                                                                                   <span v-if="input.timestamp && action.data[input.name + '_show']" v-html="validateTimestamp(action.data[input.name], true)"></span>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'textarea'">
                                                                                <textarea class="form-control"
                                                                                        :required="input.required" :placeholder="input.description || input.name" :title="input.description || input.name" v-model.trim="action.data[input.name]"></textarea>
                                                                                    <i class="ph ph-x ph-md form-control-feedback" :title="actionInputHasError(input, action.data[input.name])"
                                                                                    v-tooltip v-if="showErrors && actionInputHasError(input, action.data[input.name])"></i>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'email'">
                                                                                <input type="email" class="form-control" placeholder="<EMAIL>"
                                                                                    v-popover="{title: 'Example', content: '<EMAIL>', trigger: 'focus'}" :multiple="input.multiple" :required="input.required" v-model.trim="action.data[input.name]" />
                                                                                <i class="ph ph-x ph-md form-control-feedback" :title="actionInputHasError(input, action.data[input.name])"
                                                                                v-tooltip v-if="showErrors && actionInputHasError(input, action.data[input.name])"></i>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'html'">
                                                                                <quill-editor class="quill-feedback"
                                                                                            :class="{'has-error': showErrors && actionInputHasError(input, action.data[input.name])}" :options="quillOptionsHtml" v-model.trim="action.data[input.name]">
                                                                                </quill-editor>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'account'">
                                                                                <div :key="'account' + agIndex + '' + index">
                                                                                    <select class="form-control" title="Select account"
                                                                                            :multiple="input.multiple" v-model.number="action.data[input.name]" v-selectpicker>
                                                                                        <option
                                                                                                v-for="account in publishableAccounts" :value="account.id">
                                                                                            {{ account.name }} ({{ account.type.split(".")[0] }})
                                                                                        </option>
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'queue'">
                                                                                <div :key="'queue' + agIndex + '' + index">
                                                                                    <select class="form-control" title="Select queue"
                                                                                            v-model.number="action.data[input.name]" v-selectpicker>
                                                                                        <option
                                                                                                v-for="queue in queues" :value="queue.id">
                                                                                            {{ queue.name }}
                                                                                        </option>
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'url'">

                                                                                <input type="url" class="form-control" placeholder="https://domain.com/endpoint"
                                                                                    :required="input.required" v-model.trim="action.data[input.name]" />
                                                                                <i class="ph ph-x ph-md form-control-feedback" :title="actionInputHasError(input, action.data[input.name])"
                                                                                v-tooltip v-if="showErrors && actionInputHasError(input, action.data[input.name])"></i>

                                                                            </div>
                                                                            <div v-else-if="input.type === 'dropdown'">
                                                                                <div>
                                                                                    <select class="form-control" :title="input.description || input.name"
                                                                                            :multiple="input.multiple" v-model.trim="action.data[input.name]" v-selectpicker>
                                                                                        <option v-for="v in input.options" :value="v">
                                                                                            {{ v }}
                                                                                        </option>
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'request_body'">
                                                                                <textarea class="form-control"
                                                                                    :required="input.required" :placeholder="input.description || input.name" :title="input.description || input.name" v-model.trim="action.data[input.name]"></textarea>
                                                                                <i class="ph ph-x ph-md form-control-feedback" :title="actionInputHasError(input, action.data[input.name])"
                                                                                v-tooltip v-if="showErrors && actionInputHasError(input, action.data[input.name])"></i>
                                                                            </div>
                                                                            <div v-else-if="input.type === 'checkbox'">
                                                                                <div class="form-check">
                                                                                    <input type="checkbox" class="form-check-input"
                                                                                        :id="'checkbox_action_' + agIndex + '_' + index + '_' + input.name" v-model.boolean="action.data[input.name]" :title="input.description || input.name" :required="input.required" />
                                                                                    <label class="form-check-label"
                                                                                        :for="'checkbox_action_' + agIndex + '_' + index + '_' + input.name">
                                                                                        {{  input.description || input.name }}
                                                                                    </label>
                                                                                </div>
                                                                                <div class="pt-2"
                                                                                    v-if="input.media_dropdown && action.data[input.name]">
                                                                                    <!-- if attach media selected, check if we need to have the user select/input the media -->

                                                                                    <div v-if="mediaPlaceholders.length > 1">
                                                                                        <input type="text" class="form-control" placeholder="Media URL" title="Media URL: use a placeholder here"
                                                                                            v-model.trim="action.data[input.name + '_url']"/>
                                                                                    </div>
                                                                                    <div v-else-if="mediaPlaceholders.length === 0">
                                                                                        <input type="text" class="form-control" placeholder="Media URL" title="Media URL"
                                                                                            v-model.trim="action.data[input.name + '_url']"/>
                                                                                    </div>

                                                                                </div>
                                                                            </div>
                                                                            <div v-else>
                                                                                I don't know how to render {{ input.type }}
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                    <div class="help-block" v-if="(action.type && availableActions[action.type]) && (availableActions[action.type].exports || action.type === 'webhook_request')">
                                                                        <template v-if="action.type === 'webhook_request' && false">
                                                                            For details about this action, <a href="#">check this article</a>.
                                                                        </template>
                                                                        <template v-if="availableActions[action.type].exports">
                                                                            <button class="btn btn-sm btn-outline-secondary"
                                                                                    @click="openPlaceholdersModal(action, 'action_' + (agIndex + 1) + '_' + (index + 1) + '.')" v-tooltip="'See data placeholders available from this action'">
                                                                                Data Placeholders
                                                                            </button>
                                                                        </template>
                                                                    </div>
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                </div>
                                                <div class="rounded-md" :class="actionGroup.actions.length > 1 ? 'ml-6' : 'ml-0'">
                                                    <a role="button" class="btn btn-light btn-sm"
                                                    @click="addAction(agIndex)">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ph-bold ph-plus mr-2"></i> {{ "Add" + (actionGroup.actions.length ? " another":"") + " action" }}
                                                    </div>
                                                    </a>
                                                </div>
                                            </div>

                                            <h5 class="font-weight-500 mb-0 mt-5">If these conditions are met</h5>
                                            <div class="mb-5 rounded-md bg-light p-4"
                                             v-tour:atmvue_conditions="'Every action group can have conditions. If you set conditions, these conditions must be fulfilled for the actions to be performed. <br/><br/> You can add a condition or a condition group. A condition group can have a condition or (an inner or nested) condition group.'"
                                             v-if="Object.keys(availableConditions).length">
                                            
                                            <div class="btn-group btn-group-sm" v-if="hasNestedConditionGroup(agIndex) && 1===2">
                                                <button class="btn btn-outline-secondary active disabled">
                                                    ALL
                                                </button>
                                            </div>
                                            <condition class="_conditions"
                                                :action-group-index="agIndex" :data="actionGroup.conditions" :update="conds => setConditions(agIndex, conds)" />
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a role="button" class="btn btn-light btn-sm"
                                       @click="addActionGroup" title="Adds another action group. This is useful when you want to perform different actions based on different conditions." v-tooltip>
                                        <div class="d-flex align-items-center">
                                            <i class="ph-bold ph-plus plus-icon mr-2"></i> Add another action group
                                        </div>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="my-6">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" :checked="!ignore_failure" @click="ignore_failure = !ignore_failure">
                                            <label for="" class="form-check-label">Disable this automation when an action is failed</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- automation logs rendered in slideout modal -->
                        <portal target-el="#automation_logs"
                                v-if="logs.show">
                            <div v-if="logs.loading" class="text-center">
                                <i class="ph ph-circle-notch ph-spin ph-lg"></i>
                            </div>
                            <div v-else>
                                <template v-if="logs.error">
                                    <div class="alert alert-danger text-center">
                                        {{ logs.error }}
                                    </div>
                                </template>

                                <template v-else>
                                    <div class="list-group list-group-flush border-bottom" v-if="logs.data.length">
                                        <div class="list-group-item py-4 px-0"
                                             v-for="log in logs.data" :class="{'list-group-item-error': log.props && log.props.type ==='error'}">
                                            <p class="font-weight-500 mb-1">
                                                {{ $momentUTC(log.created_at).format('MMM DD, YYYY - h:mm a') }}
                                            </p>
                                            <p class="mb-0">
                                                {{ log.message }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="card border" v-else>
                                        <div class="card-body">
                                            <div>
                                                No records
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </portal>
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-between mt-2">
                    <div>
                        <button class="btn btn-outline-danger btn-md" @click="deleteAutomation" title="Delete" data-toggle="tooltip">
                            Delete
                        </button>
                    </div>
                    
                    <div>
                        <span style="position: relative; right: 10px;cursor: pointer;"
                            v-tour:atmvue_enabledisable="'You can also enable or disable your Automation. This is useful if you want an Automation to stop working without deleting  it.'">
                            <i class="ph ph-circle-notch ph-spin ph-lg" v-if="saving || statusSaving"></i>
                            <template v-else>
                                <i class="ph ph-toggle-right ph-fill ph-2xl text-primary" title="Disable"
                                    @click="toggleStatus" v-tooltip v-if="active" key="toggleOff"></i>
                                <i class="ph ph-toggle-left ph-2xl" title="Enable"
                                    @click="toggleStatus" v-tooltip v-else key="toggleOn"></i>
                            </template>
                        </span>
                        <button class="btn btn-primary btn-md"
                            @click="save" :disabled="saving">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- modal for placeholders -->
        <div class="modal" id="modal_placeholders" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-long modal-lg mx-md-auto mx-0" role="document">
                <div class="modal-content rounded-xl">
                    <div class="modal-header pt-6 pb-2">
                        <h5 class="modal-title">
                            Data Placeholders
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true"><i class="ph ph-x ph-md"></i></span>
                        </button>
                    </div>
                    <div class="modal-body pb-6">
                        <div>
                            <p class="mb-4">
                                The following placeholders are available for the current scenario. These placeholders can be used in your actions. The placeholder will be replaced by the actual data it represents.
                            </p>
                        </div>
                        <div class="mt-1">
                            <div class="table-responsive table-border rounded-md">

                                <table class="table mb-0">
                                    <thead class="card-header">
                                        <tr>
                                            <th class="pl-4 py-2">
                                                Placeholder
                                            </th>
                                            <th class="pl-4 py-2">
                                                Description
                                            </th>
                                            <th class="pl-4 py-2">
                                                Type
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(value, key) in (actionPlaceholders ? actionPlaceholders : availablePlaceholders)">
                                            <td>
                                                <code class="text-primary" @click="selectText">{{ "{" + "{ " + key + " }" + "}" }}</code>
                                                <span class="cursor-pointer copy-icon p-1 rounded">
                                                    <i :ref="key" class="ph ph-copy ph-md" @click="copyContent(key)"></i>
                                                </span>
                                            </td>
                                            <td>
                                                {{ value.description }}
                                            </td>
                                            <td>
                                                {{ Array.isArray(value.type) ? value.type.join("|") : value.type }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div v-if="actionPlaceholders">
                                <p class="alert alert-warning">
                                    These placeholders are only available subsequent to this action. Conditions in subsequent action groups can also use these placeholders.
                                    <br/>
                                    In case of JSON responses, you can access any json node like <code>{{ '{' + '{' + 'action_x_x.response.some.node.here' + '}' + '}' }}</code>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import $ from "jquery";
import _ from "lodash";
import isEmail from "validator/lib/isEmail";
import { alertify, axios, axiosErrorHandler,getIconForAccount } from "../../components";
import Condition from "./automation/Condition";
import config from "./automation/config";
import SlideoutModal from "./../../slideout_modal";
import Quill from "quill";
import { quillEditor } from "vue-quill-editor";
import copy from "copy-to-clipboard";
import { getNetworks } from '../../data.js'

// force inline styles
const forceInlineFor = ["background", "color", "size", "direction", "font", "align"];
forceInlineFor.forEach(t => Quill.register(Quill.import("attributors/style/" + t), true));

let logsModal = null; // so we can also destroy when needed

export default {
    name: "Automation",
    components: { Condition, quillEditor },
    data() {
        return {
            id: null,
            active: false,
            webhook_url: null,
            description: null,
            user_id: null,
            created_by:'',
            team_id: null,
            team: null,
            event: null,
            event_data: {},
            rules: [
                {
                    actions: [
                        {
                            type: null, // action type
                            data: {} // action data
                        }
                    ] /*
                        conditions: [
                            {
                                conditions: [
                                    {
                                        key: null,
                                        operator: null, // IS, IS_NOT, CONTAINS, CONTAINS_NOT,
                                        value: null
                                    }
                                ],
                                operator: null // AND, OR
                            },
                            {
                                key: null,
                                operator: null,
                                value: null
                            }
                        ]*/,
                    conditions: []
                }
            ],

            ignore_failure: false,

            allAccounts: [],
            queues: [],

            networks:[],

            error: null,
            loaded: false,

            logs: {
                show: false,
                error: null,
                loading: false,
                data: null
            },
            showErrors: false,
            saving: false,
            statusSaving: false,
            quillOptionsHtml: {
                modules: {
                    toolbar: [
                        ["bold", "italic", "underline", "strike"], // toggled buttons
                        ["blockquote"],

                        [{ header: 1 }, { header: 2 }], // custom button values
                        [{ list: "ordered" }, { list: "bullet" }],
                        [{ script: "sub" }, { script: "super" }], // superscript/subscript
                        [{ direction: "rtl" }], // text direction

                        [{ header: [1, 2, 3, 4, 5, 6, false] }],

                        [{ color: [] }, { background: [] }], // dropdown with defaults from theme
                        [{ font: [] }],
                        [{ align: [] }],

                        ["clean"] // remove formatting button
                    ]
                }
            },

            // action placeholders for showing placeholders for specific actions
            actionPlaceholders: null
        };
    },
    watch: {
        event(newVal, oldVal) {
            // reset
            if (oldVal) {
                this.event_data = {};
            }
            const fixConditions = conds => {
                conds.forEach(c => {
                    if (c.conditions) {
                        fixConditions(c.conditions);
                    } else {
                        if (!this.availableConditions[c.key]) {
                            c.key = c.operator = c.value = null;
                        }
                    }
                });
            };
            this.rules.forEach(actionGrp => {
                if (Object.keys(this.availableConditions).length === 0) {
                    // reset conditions if there are no available conditions after the event type changes
                    actionGrp.conditions = [];
                    return;
                }
                fixConditions(actionGrp.conditions);
            });
        }
    },
    computed: {
        config: () => config,
        accounts() {
            if(this.team) {
                return this.allAccounts.filter(a => a.team_id === this.team.id);
            } else return this.allAccounts.filter(a => !a.team_id);
        },
        filteredAccounts() {
            if (!this.accounts) return [];
            return this.accounts.filter(a => {
                if (this.event && (this.event === "incoming_message")) {
                    return ["facebook.page", "twitter.profile", ].includes(a.type);
                }
                if (this.event && this.event === "instagram.new_comment") {
                    return ["instagram.api"].includes(a.type);
                }
                if (this.event && this.event === "facebook.post_reply") {
                    return ["facebook.page"].includes(a.type);
                }
                if (this.event && this.event === "facebook.review") {
                    return ["facebook.page"].includes(a.type);
                }
                return true;
            });
        },
        filterNetworks(){
            // only return networks that we have in accounts
            return this.networks.filter(n => {
                return this.filteredAccounts.find(a => a.type === n.type);
            });
        },
        publishableAccounts() {
            if (!this.accounts) return [];
            return this.accounts.filter(a => {
                return true; // all accounts are publishable; not sure if we really need the filter
                // return [
                //     "facebook.page",
                //     "facebook.group",
                //     "twitter.profile",
                //     "instagram.api",
                //     "linkedin.org",
                //     "linkedin.brand",
                //     "linkedin.profile",
                //     "google.location",
                //     "mastodon.profile",
                // ].includes(a.type);
            });
        },
        availableActions() {
            const genericActions = this.config.actions.generic;
            const specificActions = this.event ? this.config.actions[this.event] || [] : [];
            const actionsObj = {};
            [...specificActions, ...genericActions].forEach(a => (actionsObj[a.type] = a));
            return actionsObj;
        },
        availableEvents() {
            const eventsObj = {};
            this.config.events.forEach(e => (eventsObj[e.type] = e));
            return eventsObj;
        },
        availableConditions() {
            let evParts = (this.event || "").split("."),
                evType = this.event;
            if (evParts.length > 1) {
                evType = evParts[0] + ".*";
            }
            const genericConditions = this.config.conditions.generic;
            const specificConditions = evType ? [...(this.config.conditions[evType] || [])] : [];
            if (evType !== this.event && this.config.conditions[this.event]) {
                // add event specific conditions even when general network-specific conditions will be added
                specificConditions.push(...this.config.conditions[this.event]);
            }
            if (this.event_data.account && this.account && evType === "incoming_message") {
                // include network specific conditions for these events
                if (this.account.type === "facebook.page") {
                    specificConditions.push(...this.config.conditions["facebook.*"]);
                } else if (this.account.type === "twitter.profile") {
                    specificConditions.push(...this.config.conditions["twitter.*"]);
                } else if (this.account.type === "instagram.direct") {
                    specificConditions.push(...this.config.conditions["instagram.*"]);
                }
            }
            if (this.account && this.account.type) {
                const networkType = this.account.type.split(".")[0];
                if (this.config.conditions[evType + "." + networkType]) {
                    // network specific conditions for cross-network events
                    specificConditions.push(...this.config.conditions[evType + "." + networkType]);
                }
            }
            const conditionsObj = {};
            [...specificConditions, ...genericConditions].forEach(c => (conditionsObj[c.key] = c));
            return { ...conditionsObj, ...this.availableConditionsFromActions };
        },
        availableConditionsFromActions() {
            const allExports = {};
            this.rules.forEach((actionGroup, grpIndex) => {
                actionGroup.actions.forEach((action, index) => {
                    if (!action.type || !this.availableActions[action.type]) return;
                    const exports = this.availableActions[action.type].exports;
                    if (exports) {
                        // something is exported
                        Object.keys(exports).forEach(k => {
                            const key = "action_" + (grpIndex + 1) + "_" + (index + 1) + "." + k;
                            allExports[key] = {
                                groupIndex: grpIndex,
                                actionIndex: index,
                                key,
                                ...exports[k]
                            };
                        });
                    }
                });
            });
            return allExports;
        },
        availableConditionsFromActionsGrouped() {
            const exportsByGroups = [];
            Object.keys(this.availableConditionsFromActions).forEach(k => {
                const d = this.availableConditionsFromActions[k];
                exportsByGroups[d.groupIndex] = exportsByGroups[d.groupIndex] || [];
                exportsByGroups[d.groupIndex].push(d);
            });
            return exportsByGroups;
        },
        availablePlaceholders() {
            const placeholders = {};
            const actionPlaceholders = this.availableConditionsFromActions;
            Object.keys(this.availableConditions).forEach(k => {
                if (actionPlaceholders[k]) return; // don't include action placeholders
                const v = this.availableConditions[k];
                placeholders[v.key] = {
                    description: v.description,
                    type:
                        v.value && v.value.type.includes("text")
                            ? ["string"]
                            : [v.value && v.value.type ? v.value.type : "---"]
                };
                const varName = v.key.split(".").pop();
                if (varName.startsWith("has_") || varName.startsWith("is_")) {
                    placeholders[v.key].type = "boolean";
                }
            });
            return placeholders;
        },
        mediaPlaceholders() {
            return Object.keys(this.availablePlaceholders).filter(k => ["image", "media_url"].includes(k));
        },
        filterSocialNetwork() {
            if (this.event && this.event.includes(".")) {
                // only show those network specific accounts
                return this.event.split(".")[0];
            }
            return null;
        },
        account() {
            if (this.event_data.account && this.accounts) {
                let acc = null;
                this.accounts.forEach(a => {
                    if (a.id === this.event_data.account) acc = a;
                });
                return acc;
            }
            return {};
        }
    },
    methods: {
        getIconForAccount,
        goBack(){
            window.location.href = `/app/automations`; //history.go(-1) wouldn't refresh the page completely
        },
        getAccountsByType(type) {        
            return this.filteredAccounts.filter(account => account.type === type);
        },
        initialize(data) {
            try {
                _.extend(this, data.automation);
                this.allAccounts = data.accounts;
                this.queues = data.queues;
                this.loaded = true;
                this.$nextTick(() => {
                    this.$forceUpdate(); // so that every element is graphically updated according to data (i.e. selectpicker)
                });
            } catch (e) {
                this.error = e.message || "An error occurred";
            }
        },
        actionInputHasError(input, value) {
            // validate input and return error
            if (input.required && !value) {
                return "Please fill out this field.";
            }
            if (!value) return;
            if (input.type === "email") {
                if (
                    value.split(",").some(e => {
                        if (!isEmail(e.trim())) {
                            return true;
                        }
                    })
                ) {
                    return "Invalid email.";
                }
            }
        },
        validateTimestamp(ts, retMsg) {
            let ret = false;
            let msg =
                "Enter a relative timestamp string. Example: <code>+2 days</code> for publishing after 2 days (supported: minutes, hours, days, weeks, months, years) or a <code>{{ placeholder }}</code> (for using a placeholder)";

            if (ts) {
                if (ts.includes("{{") && ts.includes("}}")) {
                    const match = ts.match(/{{(.*)}}/);
                    if (match) {
                        ret = true;
                        msg =
                            '<span class="text-warning">Please make sure that this placeholder has unix timestamp when the automation gets executed</span>';
                    }
                } else if (ts.includes("+")) {
                    const match = ts.match(/\+(\d+)\s*?(\w+)/);
                    if (match) {
                        if (
                            [
                                "mins",
                                "minutes",
                                "minute",
                                "min",
                                "hr",
                                "hour",
                                "hours",
                                "hrs",
                                "day",
                                "days",
                                "week",
                                "weeks",
                                "month",
                                "months",
                                "year",
                                "years",
                                "yrs",
                                "yr"
                            ].includes(match[2])
                        ) {
                            ret = true;
                            msg =
                                '<span class="text-warning">Will be scheduled for publishing after ' +
                                match[1] +
                                " " +
                                match[2] +
                                "</span>";
                        }
                    }
                }

                if (!ret) {
                    msg = '<span class="text-danger">' + msg + "</span>";
                }
            }

            if (retMsg) {
                return msg;
            }

            return ret;
        },
        eventInputHasError() {
            const $eventContainer = $(this.$refs.eventOptionsContainer);
            let hasError = false;
            $eventContainer.find(":input[required]").each((i, input) => {
                if (!$(input).val()) hasError = true;
            });
            return hasError;
        },
        validate() {
            // return false if there is validation error

            // validate events, actions - conditions are not validated for now
            return ![
                this.rules.some(actGrp =>
                    actGrp.actions.some(act => {
                        let actCfg = this.availableActions[act.type];
                        return (actCfg || { input: [] }).input.some(inp => {
                            return !!this.actionInputHasError(inp, act.data[inp.name]);
                        });
                    })
                ),
                this.eventInputHasError()
            ].some(v => v);
        },
        async toggleStatus() {
            this.statusSaving = true;
            try {
                const res = await axios.patch("/app/automations/" + this.id + "/status", {
                    active: !this.active
                });
                this.active = !this.active;
            } catch (e) {
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
            }
            this.statusSaving = false;
        },
        showLog: (function() {
            return async function() {
                if (!logsModal) {
                    logsModal = await SlideoutModal.create("Automation Logs", {
                        type: "right",
                        modalConfig: {}, // so it can be closed by clicking outside and esc key
                        onClose: (el, modal) => {
                            this.logs.show = false;
                            console.log("close");
                        },
                        onAdded(el) {
                            $(el)
                                .find(".modal-body")
                                .empty()
                                .append($("<div id='automation_logs' />"));
                        }
                    });
                }
                if (this.logs.loading) return;
                if (!this.logs.show) {
                    this.logs.loading = true;
                    this.logs.show = true;
                    logsModal.open();
                    try {
                        const res = await axios.get("/app/automations/" + this.id + "/log");
                        this.logs.data = res.data;
                    } catch (e) {
                        this.logs.error = e.message;
                    }
                    this.logs.loading = false;
                } else {
                    this.logs.error = null;
                    this.logs.data = null;
                    this.logs.show = false;
                }
            };
        })(),
        async save() {
            if (this.validate()) {
                this.saving = true;
                // ok
                // post to server
                try {
                    const res = await axios.patch("/app/automations/" + this.id, {
                        description: this.description,
                        event: this.event,
                        event_data: this.event_data,
                        rules: this.rules,
                        ignore_failure: this.ignore_failure,
                    });
                    alertify.success("Successfully saved.");
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                }
                this.saving = false;
            } else {
                // show errors
                this.showErrors = true;
                alertify.error("Please fix the form errors to continue.");

                // temporary workaround. ideally, it should hide errors one by one after user starts inputting valid data in form fields
                const blink = n => {
                    n = n || 0;
                    if (n === 10) {
                        this.showErrors = false;
                        return;
                    }
                    this.showErrors = !this.showErrors;
                    this.$nextTick(() => {
                        setTimeout(() => blink(n + 1), 500);
                    });
                };
                blink();
            }
        },
        async deleteAutomation() {
            alertify.delete("Are you sure you want to delete this automation?", async () => {
                try {
                    const res = await axios.delete(`/app/automations/${this.id}/delete`);
                    document.location.href = "/app/automations";
                } catch (error) {
                    axiosErrorHandler(error);
                }
            });
        },
        selectText(e) {
            const elem = e.target;
            const range = document.createRange();
            range.selectNode(elem);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
        },
        copyContent(key){            
            copy("{{ " + key + " }}" );
            let ref = this.$refs[key][0];
            ref.classList.value = 'ph ph-check ph-md'
            setTimeout(() => {
                ref.classList.value = 'ph ph-copy ph-md';
            },800);
        },
        // placeholders
        openPlaceholdersModal(currAction = null, keyPrefix = null) {
            if (currAction) {
                const exports = this.availableActions[currAction.type].exports;
                const placeholders = {};
                Object.keys(exports).forEach(k => {
                    placeholders[keyPrefix + k] = exports[k];
                });
                this.actionPlaceholders = placeholders;
            } else {
                this.actionPlaceholders = null;
            }
            this.$nextTick(() => {
                $("#modal_placeholders").modal("show");
            });
        },

        // action group
        addActionGroup() {
            this.rules.push({
                actions: [
                    {
                        type: null,
                        data: {}
                    }
                ],
                conditions: []
            });
        },
        deleteActionGroup(index) {
            this.rules.splice(index, 1);
        },

        // action
        initActionData(actionType, agIndex, index) {
            this.rules[agIndex].actions[index].data = {};
            if (actionType === "new_post") this.rules[agIndex].actions[index].data["accounts"] = [];
        },
        addAction(actionGroupIndex) {
            this.rules[actionGroupIndex].actions.push({
                type: null,
                data: {}
            });
        },
        deleteAction(actionGroupIndex, index) {
            this.rules[actionGroupIndex].actions.splice(index, 1);
        },
        orderActions(actionGroupIndex, e) {
            this.rules[actionGroupIndex].actions.splice(
                e.newIndex,
                0,
                this.rules[actionGroupIndex].actions.splice(e.oldIndex, 1)[0]
            );
            const _actions = this.rules[actionGroupIndex].actions;
            this.rules[actionGroupIndex].actions = [];
            this.$nextTick(() => {
                this.rules[actionGroupIndex].actions = _actions;
                this.$nextTick(this.$forceUpdate); // so selectpicker will update its ui
            });
        },

        // conditions
        setConditions(index, conditions) {
            this.rules[index].conditions = conditions;
        },
        hasNestedConditionGroup(index) {
            return this.rules[index].conditions.some(cond => {
                if (cond.conditions) return true;
            });
        }
    },
    async mounted() {
        this.networks = await getNetworks(true);
    },
    beforeDestroy() {
        logsModal && logsModal.destroy();
    }
};
</script>

<style lang="scss">
@import "~quill/dist/quill.core.css";
@import "~quill/dist/quill.snow.css";
@import "~quill/dist/quill.bubble.css";

.form-control-feedback {
    // fix alignment so it works without the need of specifying <label> for the input
    right: 15px;
    top: 10px;
}

._actionGroup {
    // delete button for action group
    .del-btn {
        position: relative;
        z-index: 1;
        border-radius: 50px;
        float: right;
        visibility: hidden;
        left: 10px;
        bottom: 10px;
        &.grp {
            left: 35px;
            bottom: 32px;
        }
    }
    .action-row-multiple {
        margin-left: 76px;
    }
    input,
    textarea {
        max-width: 100%;
    }
}


// timeline-type item
.item {
    position: relative;
    border-bottom: 1px dashed #d3e0e9;
    padding-left: 10px;
    &.supports-nested {
        margin-left: 20px;
        padding-left: 20px;
    }
    > .icon {
        position: absolute;
        width: 40px;
        height: 40px;
        font-size: 20px;
        text-align: center;
        line-height: 40px;
        border-radius: 100%;
        left: -20px;
        top: 0;
    }
}


// quill editor
.quill-feedback {
    background: #fff;
    border: 1px solid transparent;
    &.has-error {
        border: 1px solid #a94442;
    }
}
.copy-icon:hover{
    background-color: #F1F3F5;
}
.automation-title-input{
    font-size: 28px !important;
}
</style>
