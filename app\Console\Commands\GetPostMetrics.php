<?php

namespace App\Console\Commands;

use App\Helpers\ApiHelper;
use App\Helpers\LinkedInClient;
use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Console\Command;
use App\Post;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Revolution\Mastodon\MastodonClient;

class GetPostMetrics extends Command
{
    private static $skipErrorsContaining = [
        'limit reached',
        'timed out',
        'connection refused',
        'connection reset',
        'unreachable',
        'unknown error',
        'retry your request',
        'connection timeout',
        'does not exist', // when cannot access a post/object
        'unknown ssl protocol',
        'permission missing',
        'not enough',
        'Upstream Failure',
        'bad gateway',
        '504 gateway',
    ];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posts:metrics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get posts metrics';

    // prevent getting rate-limited by reddit
    private $redditRequests = 0;

    // prevent fetching metrics of posts by same account multiple times
    private $accountsSeen = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // get all posts which are published minimum 1hr ago
        $postsQuery = Post::published()
            ->whereRaw('`published_at` <= now() - interval 1 hour') // at least 1 hr has passed
            ->where(function(/** @var Builder $query */ $query){
                // where last fetched was x hrs ago at least
                return $query->whereRaw('`insights_fetched_at` <= now() - interval 24 hour')
                    ->orWhere('insights_fetched_at', null);
            })
            ->where('published_at', '>', now()->subMonths(1)) //fetch for posts published in last 1 month
            ->where('external_id', '<>', null)
            ->whereHas('account', function(/** @var Builder $query */ $query){
                return $query
                    ->whereIn('type', [
                        'facebook.page',
                        'instagram.api',
                        'twitter.profile',
                        'linkedin.org',
                        'linkedin.brand',
                        'linkedin.profile',
                        'mastodon.profile',
                        'google.youtube',
                        'pinterest.profile',
                        'threads.profile',
                        'bluesky.profile',
                    ])
                    ->where('active', true);
            })
            ->orderBy('insights_fetched_at') // so we get rows that need update first
            ->with('account'); // also fetch account with it

        $postsQuery
            ->chunk(1000, function (/** @var Post[]|Collection $posts */ $posts, $page){
                $posts->each(function(/** @var Post $post */ $post) {

                    if(!$post->account){
                        // not sure how
                        $post->update([
                            'insights_fetched_at' => now()
                        ]);
                        return;
                    }

                    // if account->type has reddit, and we have already made 60 requests, skip this post
                    if(Str::contains($post->account->type, 'reddit') && $this->redditRequests >= 60){
                        // we don't need to avoid it; we use external api for reddit metrics now
                        // return;
                    }

                    if(!$post->getOption('deleted') && !$post->getOption('cant_fetch')){ // if post is not deleted externally
                        // if we have already fetched metrics for this account, skip
                        if(isset($this->accountsSeen[$post->account_id])){
                            $post->update([
                                'insights_fetched_at' => now()->subMinutes((23 * 60) + 50), // retry after 10min
                            ]);
                            return;
                        }

                        $this->accountsSeen[$post->account_id] = $post->account_id;
                        $this->getMetrics($post);
                    } else {
                        // can never fetch metrics
                        $post->update([
                            'insights_fetched_at' => now(),
                        ]);
                    }
                });

                if($posts->count() >= 1000){
                    \Log::info('GetPostMetrics: we have a lot of posts to fetch metrics for (>1000)');
                }

                // enough for now
                return false;
            });
    }

        /**
     * Insert stats / engagements data
     * @param Post $post
     */
    private function getMetrics(Post $post){
        $account = $post->account;

        if(!$account) return;

        try {
            $metrics = $this->{'process' . studly_case(str_replace('.', '_', $account->type))}($post);

            if(is_array($metrics) && count($metrics) > 0){
                $metrics = array_filter($metrics, function($value){
                    return $value !== null;
                });

                $rows = [];
                foreach ($metrics as $metric => $value) {
                    $rows[] = [
                        'post_id' => $post->id,
                        'account_id' => $account->id,
                        'metric_type' => $metric,
                        'metric_value' => $value,
                    ];
                }
                // now insert new rows
                $insert = get_insights_db()->table('post_metrics')->insert($rows);
                if (!$insert) {
                    $this->error('Error while inserting rows');
                    report(new \Exception('Database insert failed for engagements data'));
                }
            }

            // update insights fetched at
            $post->insights_fetched_at = now();
            $post->save();

        } catch (\Exception $exception){

            if($exception instanceof ServerException){
                // do nothing
                return;
            }

            $account->refresh(); // just to be sure; because it can happen (token refresh, etc.)

            \Log::error('Error while fetching post metrics for post ID ' . $post->id . ': ' . $exception->getMessage());
            if($account->testConnection(true, 'Unable to fetch post metrics: ' . $exception->getMessage())){
                report($exception);
            }
        }
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception
     */
    public function processInstagramApi(Post $post){
        $account = $post->account;

        if(!isset($post->result['fb_id'])){
            // should not happen
            return [];
        }

        /** @var Facebook $fb */
        $fb = $account->getApi();

        $data = [];

        try {
            $res = $fb->get('/' . $post->result['fb_id'] . '?fields=like_count,comments_count')->getDecodedBody();
            if(!$res){
                return [];
            }
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        if(!isset($res['like_count'], $res['comments_count'])){
            // skip for now
            return [];
        }

        $data['likes'] = $res['like_count'];
        $data['comments'] = $res['comments_count'];

        if($post->getOption('no_insights_access')){
            // if we don't have access to insights, we can't get any more data
            return $data;
        }

        $metrics = [
            'carousel'=> [
                'reach',
                'saved',
            ],
            'photo'=> [
                'reach',
                'saved'
            ],
            'video'=> [
                'reach',
                'saved',
            ],
            'reel'=>[
                'reach',
                'saved',
                'shares',
                'total_interactions'
            ],
        ];

        $isCarousel = count($post->getAttachments()) > 1;

        $isReel = $post->type === 'video' && $post->getOption('post_as_reel', true);

        if($isCarousel){
            try {
                $res = $fb->get('/' . $post->result['fb_id'] . '/insights?period=lifetime&metric=' . implode(',', $metrics['carousel']))->getDecodedBody();
                if($res){
                    foreach($res['data'] as $metricItem){
                        $metric = $metricItem['name'];
                        $value = $metricItem['values'][0]['value'];
                        $data[$metric] = $value;
                    }
                }
            } catch(\Exception $exception){
                if(Str::contains($exception->getMessage(), 'does not have permission')){
                    $post->setOption('no_insights_access', true);
                } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    report($exception);
                }
            }
        } else if($isReel){
            try {
                $res = $fb->get('/' . $post->result['fb_id'] . '/insights?period=lifetime&metric=' . implode(',', $metrics['reel']))->getDecodedBody();
                if($res){
                    foreach($res['data'] as $metricItem){
                        $metric = $metricItem['name'];
                        $value = $metricItem['values'][0]['value'];
                        $data[$metric] = $value;
                    }
                }
            } catch(\Exception $exception){
                if(Str::contains($exception->getMessage(), 'does not have permission')){
                    $post->setOption('no_insights_access', true);
                } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    report($exception);
                }
            }
        } else if($post->type === 'video'){
            try {
                $res = $fb->get('/' . $post->result['fb_id'] . '/insights?period=lifetime&metric=' . implode(',', $metrics['video']))->getDecodedBody();
                if($res){
                    foreach($res['data'] as $metricItem){
                        $metric = $metricItem['name'];
                        $value = $metricItem['values'][0]['value'];
                        $data[$metric] = $value;
                    }
                }
            } catch(\Exception $exception){
                if(Str::contains($exception->getMessage(), 'does not have permission')){
                    $post->setOption('no_insights_access', true);
                } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    report($exception);
                }
            }
        } else {
            try {
                $res = $fb->get('/' . $post->result['fb_id'] . '/insights?period=lifetime&metric=' . implode(',', $metrics['photo']))->getDecodedBody();
                if($res){
                    foreach($res['data'] as $metricItem){
                        $metric = $metricItem['name'];
                        $value = $metricItem['values'][0]['value'];
                        $data[$metric] = $value;
                    }
                }
            } catch(\Exception $exception){
                if(Str::contains($exception->getMessage(), 'does not have permission')){
                    $post->setOption('no_insights_access', true);
                } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    report($exception);
                }
            }
        }

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     * @throws FacebookSDKException
     * @throws \Exception
     */
    public function processFacebookPage(Post $post){

        if($post->getOption('post_as_story')){
            return [];
        }

        $account = $post->account;

        /** @var Facebook $fb */
        $fb = $account->getApi();

        $result = $post->result;

        // post->type bug: it is not 'image' in some cases (text), so set as image
        if($post->type === 'text' && count($post->getAttachments()) > 0) {
            $post_type = $post->type;
            foreach ($post->getAttachments() as $attachment) {
                $post_type = in_array($attachment['type'], ['mp4', 'mov', 'qt', 'avi',]) ? 'video' : 'image';
                break;
            }
            $post->type = $post_type;
            $post->save();
        }

        // we have no post_id for video, so no 'shares', we only get shares for page post id
        $post_id = $post->external_id;

        if(isset($result['post_id'])){
            // for photo, we have post id in the result
            $post_id = $result['post_id'];
        }

        // ensure the post_id is in format pageID_postID
        // GET /v23.0/{page-id}_{post-id}
        if(!Str::contains($post_id, '_')){
            // if post_id is not in format pageID_postID, we need to prepend the account id
            $post_id = $account->account_id . '_' . $post_id;
        }

        $data = [];

        // get reactions count
        try {
            $res = $fb->get('/' . $post_id . '/reactions?summary=total_count')->getDecodedBody();
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), 'does not exist')){
                // post not found
                $post->setOption('deleted', true);
                return [];
            } else if($exception->getMessage() === null || Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                return [];
            } else if(Str::contains(strtolower($exception->getMessage()), 'unsupported request') && $post->type === 'video'){
                // can't fetch this video possibly due to it using copyrighted music
                $post->setOption('cant_fetch', true);
                return [];
            }
        }
        if(isset($res['summary'])){
            $data['reactions'] =  $res['summary']['total_count'];
        }

        // get comments count
        try {
            $res = $fb->get('/' . $post_id . '/comments?summary=total_count')->getDecodedBody();
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            } else if(Str::contains(strtolower($exception->getMessage()), 'unsupported request') && $post->type === 'video'){
                // can't fetch this video possibly due to it using copyrighted music
                $post->setOption('cant_fetch', true);
                return [];
            }else if(Str::contains(strtolower($exception->getMessage()), ['permission', 'page public content access'])){
                return [];
            }

            throw $exception;
        }
        if(isset($res['summary'])){
            $data['comments'] =  $res['summary']['total_count'];
        }

        // get shares count for text posts
        if($post->type === 'text') { // for images, shares doesnt exist
            try {
                $res = $fb->get('/' . $post_id . '?fields=id,shares')->getDecodedBody();
            } catch (\Exception $exception){
                if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    return [];
                }
                throw $exception;
            }
            if(isset($res, $res['shares'])){
                $data['shares'] = $res['shares']['count'];
            }
        }

        // fetch video insights
        if($post->type === 'video'){
            // the following requires read_insights permission, but works even without that permission (returns empty data)
            $metrics = [
                // views
                'total_video_views',
                'total_video_views_paid',
                'total_video_views_organic',
                'total_video_complete_views',
                'total_video_10s_views', // number of times your Page's videos played for at least 10 seconds
                'total_video_avg_time_watched',  //The average watch time, in milliseconds,

                // impressions
                'total_video_impressions',
                'total_video_impressions_paid',
                'total_video_impressions_organic',
                'total_video_impressions_fan',
            ];
            try {
                $res = $fb->get('/' . $post->external_id . '/video_insights?period=lifetime&metric=' . implode(',', $metrics))->getDecodedBody();
            } catch (\Exception $exception){
                if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    return [];
                }
                throw $exception;
            }
            $MAP = [
                'total_video_impressions' =>'impressions',
                'total_video_impressions_paid' =>'impressions_paid',
                'total_video_impressions_organic' => 'impressions_organic', 
                'total_video_impressions_fan'=>'impressions_fan',
            ];
            foreach ($res['data'] as $metricItem){
                $metric = $metricItem['name'];
                if(isset($MAP[$metricItem['name']])){
                    $metric = $MAP[$metricItem['name']];
                } else {
                    $metric = str_replace('total_', '', $metric);
                }
                $value = $metricItem['values'][0]['value'];
                if(!is_array($value)) $data[$metric] = $value;
            }
        } else if($post_id){
            // fetch post insights
            $metrics = [
                // impressions
                'post_impressions', //number of times a post entered a person's screen
                'post_impressions_fan', // impressions by fans
                'post_impressions_paid',
                'post_impressions_organic',

                // engagements
                'post_clicks', // times people clicked on anywhere in your posts
            ];
            try {
                $res = $fb->get('/' . $post_id . '/insights?period=lifetime&metric=' . implode(',', $metrics))->getDecodedBody();
            } catch (\Exception $exception){
                if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                    // skip for now
                    return [];
                }
                if(Str::contains($exception->getMessage(), ['#190'])){
                    // weird fb token error
                    // (#190) This method must be called with a Page Access Token
                    if($account->testConnection(true, 'Facebook bug: ' . $exception->getMessage())){
                        // \Log::info('tokenRes: ' . $fb->getDefaultAccessToken());
                        // throw new \Exception('Account#' . $account->id . ': ' . $exception->getMessage(), 0, $exception);
                        // just ignore
                        $res = [];
                        $res['data'] = [];
                    } else {
                        return [];
                    }
                } else {
                    throw $exception;
                }
            }
            if(!isset($res['data'])){
                // happens when fb server crashes
                return [];
            }
            foreach ($res['data'] as $metricItem){
                $metric = $metricItem['name'];
                $metric = str_replace('post_', '', $metric);

                $value = $metricItem['values'][0]['value'];
                if(!is_array($value)) $data[$metric] = $value;
            }
        }

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     */
    public function processTwitterProfile(Post $post){

        $data = [];

        // rate-limited, use unofficial api
        $scraper = ApiHelper::getRapidAPI('twitter241.p.rapidapi.com');

        try {
            // fetch tweet statistics
            $res = $scraper->get('/tweet-v2?pid=' . urlencode($post->external_id));

            $resJson = json_decode($res->getBody()->getContents(), true);

            if (!isset($resJson['result']['tweetResult']['result'])) {
                \Log::error('Unknown response from RapidAPI - probably tweet not found: ' . json_encode($resJson));
                // probably deleted
                return []; // skip for now
            }

            $legacyTweet = $resJson['result']['tweetResult']['result']['legacy'];
            $viewsObj = $resJson['result']['tweetResult']['result']['views'];

            $data['bookmarks'] = $legacyTweet['bookmark_count'];
            $data['retweets'] = $legacyTweet['retweet_count'];
            $data['likes'] = $legacyTweet['favorite_count'];
            $data['replies'] = $legacyTweet['reply_count'];
            $data['quote_tweets'] = $legacyTweet['quote_count'];
            $data['impressions'] = (int)$viewsObj['count'];

            return $data;
        } catch (\Exception $exception) {

            if (Str::contains(strtolower($exception->getMessage()), ['undefined'])) {
                // strange error, skip for now
                return [];
            } else if (Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)) {
                // temp. errors
                return [];
            }

            report($exception);
            return []; // skip for now but this needs fix
        }
    }

    /**
     * @param Post $post
     * @param int $tries
     * @return array
     * @throws \Exception
     */
    public function processMastodonProfile(Post $post){
        $account = $post->account;

        /** @var MastodonClient $m */
        $m = $account->getApi();

        try {
            $res = $m->get("/statuses/{$post->external_id}");
            if(!$res){
                return [];
            }
        } catch (\Exception $exception){
            $msg = strtolower($exception->getMessage());
            if(Str::contains(strtolower($exception->getMessage()), ['connection refused', 'timed out', 'connection timeout',])){
                return [];
            } else if(Str::contains($msg, ['not found', 'not found.', 'not found:'])){
                $post->setOption('deleted', true);
                return [];
            } else if(Str::contains($msg, ['403', '503', '500'])){
                return [];
            }
            throw $exception;
        }

        $data = [];
        $data['replies'] = $res['replies_count'];
        $data['reblogs'] = $res['reblogs_count'];
        $data['favourites'] = $res['favourites_count'];

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception|\Throwable
     */
    public function processLinkedinOrg(Post $post){
        $account = $post->account;

        /** @var LinkedInClient $li */
        $li = $account->getApi()->useRest();

        $accountUrn = 'urn:li:organization:' . $account->account_id;
        $endpointType = Str::contains($post->external_id, 'ugcPost') ? 'ugcPosts' : 'shares';

        try {
            $res = $li->get('organizationalEntityShareStatistics?q=organizationalEntity&organizationalEntity=' . urlencode($accountUrn) . '&' . $endpointType . '='  . ('List(' . urlencode($post->external_id) . ')'));
        } catch (\Exception $exception){
            if( Str::contains(strtolower($exception->getMessage()), ['404', 'do not have']) ){
                // Unable to get activityIds from any of the given shares. Either the shares/ugcPosts do not have corresponding activities or the organizational entity did not post them. request contained organizationalEntity: urn:li:organization:3199385, shareUrns: [urn:li:share:7237381495928061953] , ugcPostUrns: []
                // or 404 error
                // post not found
                $post->setOption('deleted', true);
                return [];
            } else if( Str::contains(strtolower($exception->getMessage()), ['503', '504', '500', 'json_decode error', 'null', 'bad gateway']) ){
                return [];
            }
            if($exception instanceof \LinkedIn\Exception){
                if( Str::contains(strtolower($exception->getDescription()), ['Unable to get activityIds', 'shares/ugcPosts do not have corresponding activities']) ){
                    return [];
                }
                if($exception->getDescription()){
                    throw new \Exception($exception->getDescription(), $exception->getCode(), $exception);
                }
            }
            throw $exception;
        } catch (\Throwable $throwable){
            if(!Str::contains($throwable->getMessage(), ['null', 'json_decode error'])){
                throw $throwable;
            } else {
                return [];
            }
        }

        if(empty($res['elements'])){
            return [];
        }

        $liData = $res['elements'][0]['totalShareStatistics'];

        $data = array_filter([
            'clicks' => Arr::get($liData, 'clickCount'),
            'comments' => Arr::get($liData, 'commentCount'),
            'engagement' => Arr::get($liData, 'engagement') ? Arr::get($liData, 'engagement') * 100 : null, // this is percentage so we need to multiply by 100
            'impressions' => Arr::get($liData, 'impressionCount'),
            'likes' => Arr::get($liData, 'likeCount'),
            'shares' => Arr::get($liData, 'shareCount'),
            'unique_impressions' => Arr::get($liData, 'uniqueImpressionsCount'),
        ]);

        // get video views for video posts
        if($post->type === 'video' && Str::contains($post->external_id, 'ugcPost')){
            try {
                $res = $li->get('videoAnalytics?q=entity&entity='.urlencode($post->external_id) .'&type=VIDEO_VIEW');
                if(isset($res['elements'])){
                    $data['video_views'] = $res['elements'][0]['value'];
                }
            } catch (\Throwable $exception){}
        }

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception|\Throwable
     */
    public function processLinkedinBrand(Post $post){
        return $this->processLinkedinOrg($post);
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception|\Throwable
     */
    public function processLinkedinProfile(Post $post){
        $account = $post->account;

        /** @var LinkedInClient $li */
        $li = $account->getApi()->useRest();

        try {
            $res = $li->get('socialActions/' . urlencode($post->external_id));
            if(!$res){
                return [];
            }
        } catch (\Throwable $exception){
            if( Str::contains($exception->getMessage(), ['404']) ){
                // post not found
                $post->setOption('deleted', true);
                return [];
            } else if(Str::contains($exception->getMessage(), ['403', '503', '504', '500', 'json_decode error', 'null'])){
                // temp. errors
                return [];
            }
            throw $exception;
        }

        $data = [];

        if(isset($res['commentsSummary'])){
            $data['comments'] = $res['commentsSummary']['aggregatedTotalComments'];
        }
        if(isset($res['likesSummary'])){
            $data['likes'] = $res['likesSummary']['totalLikes'];
        }

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception
     */
    public function processGoogleYoutube(Post $post){

        /** @var \Google\Service\YouTube $service */
        $service = $post->account->getApi();

        try {
            $res = $service->videos->listVideos('statistics', ['id' => $post->external_id]);
            if(!$res){
                return [];
            }
        } catch (\Exception $exception){
            if( Str::contains($exception->getMessage(), ['404']) ){
                // post not found
                $post->setOption('deleted', true);
                return [];
            } else if(Str::contains($exception->getMessage(), ['403', '503', '504', '500', 'json_decode error', 'null'])){
                // temp. errors
                return [];
            }
            throw $exception;
        }

        $data = [];

        if(count($res->getItems()) === 0){
            // not found
            $post->setOption('deleted', true);
            return [];
        }

        $video = $res->getItems()[0];

        $data['comments'] = $video->getStatistics()->getCommentCount();
        $data['likes'] = $video->getStatistics()->getLikeCount();
        $data['dislikes'] = $video->getStatistics()->getDislikeCount();
        $data['views'] = $video->getStatistics()->getViewCount();

        return $data;
    }

    /**
     * @param Post $post
     * @return array
     * @throws \Exception
     */
    public function processPinterestProfile(Post $post){
        $service = $post->account->getApi();        
        $data = [];
        try{
            $start_date = Carbon::now()->format('Y-m-d');
            $end_date = Carbon::now()->format('Y-m-d');

            if($post->type === 'video'){
                $metric_types = "IMPRESSION,PIN_CLICK,SAVE,VIDEO_MRC_VIEW,VIDEO_AVG_WATCH_TIME,VIDEO_10S_VIEW,TOTAL_REACTIONS,TOTAL_COMMENTS";
            }else {
                $metric_types = "IMPRESSION,PIN_CLICK,SAVE,TOTAL_REACTIONS,TOTAL_COMMENTS";
            }

            $res = $service->get('v5/pins/'. $post->external_id .'/analytics', [
                'query' => [
                   'start_date' => $start_date,
                   'end_date' => $end_date, 
                   'metric_types' => $metric_types,
                ]
             ]);
            $json_res = json_decode($res->getBody()->getContents(), true);
            $summary_metrics = $json_res['all']['summary_metrics'];
            $lifetime_metrics = $json_res['all']['lifetime_metrics'];
            

            if(isset($summary_metrics['SAVE']))
                $data['saved'] = $summary_metrics['SAVE'];
            if(isset($summary_metrics['IMPRESSION']))
                $data['impressions'] = $summary_metrics['IMPRESSION'];
            if(isset($summary_metrics['PIN_CLICK']))
                $data['pin_clicks'] = $summary_metrics['PIN_CLICK'];
            if(isset($lifetime_metrics['TOTAL_REACTIONS']))
                $data['reactions'] = $lifetime_metrics['TOTAL_REACTIONS'];
            if(isset($lifetime_metrics['TOTAL_COMMENTS']))
                $data['comments'] = $lifetime_metrics['TOTAL_COMMENTS'];

            if($post->type === 'video'){
                if(isset($summary_metrics['VIDEO_MRC_VIEW']))
                    $data['video_views'] = $summary_metrics['VIDEO_MRC_VIEW'];
                if(isset($summary_metrics['VIDEO_AVG_WATCH_TIME']))
                    $data['video_avg_watch_time'] = $summary_metrics['VIDEO_AVG_WATCH_TIME'];
                if(isset($summary_metrics['VIDEO_10S_VIEW']))
                    $data['video_10s_views'] = $summary_metrics['VIDEO_10S_VIEW'];
            }
        }catch(\Exception $exception){

            if (Str::contains($exception->getMessage(), ['not found', '404'])) {
                // post not found
                $post->setOption('deleted', true);
                return [];
            } else if(Str::contains($exception->getMessage(), ['403', '503', '504', '500', 'json_decode error', 'null'])){
                // temp. errors
                return [];
            }

            throw $exception;
        }

        return $data;
    }

    /**
     * @throws \Exception
     */
    public function processRedditSubreddit(Post $post){

        // we will use a third party api to get post data so our own rate limit is not affected
        /** @var Client $reddit */
        $scraper = ApiHelper::getRapidAPI('reddit-scraper2.p.rapidapi.com');

        $result = $post->result;
        $permalink = $result['permalink'];

        $permalink = Str::replaceLast('.json', '', $permalink); // just in case

        try {
            $res = $scraper->get('/post_info?post=' . urlencode($permalink));
        } catch (\Exception $exception){
            if(Str::contains($exception->getMessage(), $this::$skipErrorsContaining)){
                // temp. errors
                return [];
            }
            throw $exception;
        }

        $body = $res->getBody();

        $resJson = json_decode($body, true);

        if(!$resJson){
            return [];
        }

        if(!isset($resJson['data'])){
            // something went wrong
            throw new \Exception('Reddit API unexpected response: ' . $body);
        }

        $post = Arr::get($resJson, 'data');
        if(!$post){
            return [];
        }

        $data = [];

        $data['comments'] = $post['comments'];
        $data['score'] = $post['score'];

        return $data;
    }

    /**
     * @throws \Exception
     */
    public function processRedditProfile(Post $post){
        return $this->processRedditSubreddit($post);
    }

    /**
     * @param Post $post
     * @param int $tries
     * @return array
     * @throws \Exception
     */
    public function processThreadsProfile(Post $post, int $tries = 0){
        $account = $post->account;

        /** @var Client $threads */
        $threads = $account->getApi();

        try {

            $query = Arr::query([
                'metric' => 'views,likes,replies,reposts,quotes',
            ]);

            $res = $threads->get($post->external_id . "/insights?" . $query);

            $json = json_decode($res->getBody(), true);

            $resData = isset($json['data']) ? $json['data'] : [];

            $data = [];
            foreach($resData as $metric){
                $data[$metric['name']] = $metric['values'][0]['value'];
            }
            return $data;

        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), ['connection refused', 'timed out', 'connection timeout'])){
                return [];
            } else if(Str::contains(strtolower($exception->getMessage()), ['404'])){
                $post->setOption('deleted', true);
                return [];
            }
            throw $exception;
        }

    }

    /**
     * @param Post $post
     * @param int $tries
     * @return array
     * @throws \Exception
     */
    public function processBlueskyProfile(Post $post, int $tries = 0){
        $account = $post->account;

        /** @var Client $bluesky */
        $bluesky = $account->getApi();

        try {

            $query = Arr::query([
                'uri' => $post->external_id,
                'depth' => 1,
                'parentHeight' => 1,
            ]);

            $res = $bluesky->get("app.bsky.feed.getPostThread?" . $query);

            $json = json_decode($res->getBody(), true);

            $thread = $json['thread'];

            if(!isset($thread['post'])){
                $post->setOption('deleted', true);
                return [];
            }

            $data = [];

            $data['likes'] = $thread['post']['likeCount'];
            $data['replies'] = $thread['post']['replyCount'];
            $data['reposts'] = $thread['post']['repostCount'];

            return $data;

        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                return [];
            } else if(Str::contains(strtolower($exception->getMessage()), ['404', 'not found',])){
                $post->setOption('deleted', true);
                return [];
            }
            throw $exception;
        }

    }
}
