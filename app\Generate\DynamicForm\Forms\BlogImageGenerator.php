<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class BlogImageGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'description',
                'label' => 'Describe Your Blog Topic',
                'placeholder' => 'Enter a detailed description of the image you want to create',
                'type' => 'text',
                'rules' => 'required|string|max:400',
                'class' => 'col-md-12',
            ],
        ];
    }

    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'POST',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json',
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "Generate a concise, single-sentence prompt (max 20 words) for a blog featured image based on the user's input: {{form.description}}.",
                                    "It should describe a hyperrealistic, camera-shot-like central subject with an implied action or concept.", 
                                    "a complementary background, and a relevant artistic style if not hyperrealistic.",
                                ])),
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Prompt:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ]),
                ],
            ],
            [
                'step' => 'http',
                'input' => [
                    'method' => 'POST',
                    'url' => 'https://api.openai.com/v1/images/generations',
                    'type' => 'json',
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'timeout' => 1000,
                    'data' => array_filter([
                        'model' => 'gpt-image-1',
                        'prompt' => '{{step1.data.choices.0.message.content}}.',
                        'n' => 1,
                        'size' => '1536x1024',
                        'quality' => 'medium',
                        'user' => user() ? (string) user()->id : null,
                    ]),
                ],
            ],
        ];
    }

    public function outputData(): array
    {
        return [
            'image_url' => 'data:image/png;base64,{{step2.data.data.0.b64_json}}',
        ];
    }

    public function outputComponents(): array
    {
        return [];
    }
}