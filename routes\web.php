<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of the routes that are handled
| by your application. Just tell <PERSON><PERSON> the URIs it should respond
| to using a Closure or controller method. Build something great!
|
*/

/** pages anyone can visit **/

use App\Http\Middleware\RequireOnboardingCompleted;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// common routes
Route::get('/', 'CommonController@index');
Route::get('/about/', 'CommonController@about');
Route::get('/about/privacy', 'CommonController@privacy');
Route::get('/about/cookies', 'CommonController@cookies');
Route::get('/about/terms', 'CommonController@terms');
Route::get('/about/tos', 'CommonController@tos');
Route::get('/about/partners', 'CommonController@partners');
// Route::get('/about/careers', 'CommonController@careers');
// Route::get('/about/team', 'CommonController@team');

Route::get('/features', 'CommonController@features');


Route::get('/love','CommonController@wallOfLove');
Route::redirect('/fame', '/love');

//wordpress glossary posts
Route::get('/social-media-glossary', 'WordPressController@glossary')->name('glossary');
Route::get('/social-media-glossary/{slug}', 'WordPressController@glossaryItem')->name('glossary.item');

# publish
Route::get('/publish', 'CommonController@publish');
Route::get('/schedule-posts', 'CommonController@schedulePosts');
Route::get('/schedule-stories', 'CommonController@scheduleStories');
Route::get('/schedule-reels', 'CommonController@scheduleReels');
Route::get('/story-scheduling', 'CommonController@storySchedule');
Route::get('/bulk-scheduling', 'CommonController@bulkSchedule');
Route::get('/social-calendar', 'CommonController@calendar');
Route::get('/post-recycling', 'CommonController@postRecycling');
Route::get('/post-previews', 'CommonController@postPreviews');
Route::get('/custom-queues', 'CommonController@customQueues');
#curate
Route::get('/curate', 'CommonController@curate');
Route::get('/assistant', 'CommonController@assistant');

Route::get('/startups','CommonController@startups');
# generate
Route::get('/generate-content', 'CommonController@generateContent');
Route::get('/generate','CommonController@generate');
# respond
Route::get('/respond', 'CommonController@respond');
# monitor
Route::get('/social-media-monitoring', 'CommonController@socialMediaMonitoring');
Route::get('/monitor', 'CommonController@monitor');

# automate
Route::get('/automate', 'CommonController@automate');
Route::get('/post-from-rss', 'CommonController@postFromRss');
Route::get('/intelligent-replies', 'CommonController@intelligentReplies');
Route::get('/handle-reviews', 'CommonController@handleReviews');
Route::get('/webhook-integrations', 'CommonController@webhookIntegrations');

# collaborate
Route::get('/team-collaboration', 'CommonController@teams');

Route::get('/collaborate', 'CommonController@collaborate');
# analyze
Route::get('/analyze', 'CommonController@analyze');

# download
Route::get('/mobile', 'CommonController@mobile');
Route::get('/browser', 'CommonController@browser');

# tools
Route::get('/tools', 'ToolsController@index');
Route::get('/tools/generate-captions', 'ToolsController@generateCaptions');
Route::get('/tools/generate-posts', 'ToolsController@generatePosts');
Route::get('/tools/generate-prompt-text2img', 'ToolsController@generatePromptForText2Img');
Route::get('/tools/generate-blog-image', 'ToolsController@generateBlogImage');
Route::get('/tools/generate-quote-image', 'ToolsController@generateQuoteImage');
Route::get('/tools/generate-meme', 'ToolsController@generateMeme');
Route::post('/tools/_content_generator', 'ToolsController@contentGenerator')->middleware('throttle:10,2');
Route::get('/tools/generate-username/{slug?}', 'ToolsController@generateUsername');
Route::get('/tools/ai-post-generator', 'ToolsController@generateAIPosts');
Route::get('/tools/linkedin-post-generator', 'ToolsController@generateLinkedinPosts');
Route::get('/tools/tweet-generator', 'ToolsController@generateTweets');
Route::get('/tools/facebook-post-generator', 'ToolsController@generateFacebookPosts');
Route::get('/tools/instagram-caption-generator', 'ToolsController@generateInstagramCaptions');
Route::get('/tools/tiktok-caption-generator', 'ToolsController@generateTiktokCaptions');
Route::get('/tools/viral-hook-generator', 'ToolsController@generateViralHook');
Route::get('/tools/caption-expander', 'ToolsController@captionExpander');
Route::get('/tools/emoji-and-symbol-generator', 'ToolsController@generateEmoji');

// network specific pages
Route::get('/{network}', 'CommonController@network')->where('network', '(facebook|twitter|instagram|google-my-business|google-business-profile|linkedin|mastodon|reddit|tiktok|youtube|pinterest|bluesky|threads)');

Route::get('/pricing', 'CommonController@pricing');

Route::get('/demo', 'CommonController@demo');
Route::post('/demo/save_form', 'CommonController@saveDemoForm');

Route::get('/affiliates', 'CommonController@affiliates');
Route::post('/affiliates', 'CommonController@affiliates');

Route::get('/ask', 'CommonController@askSocialBuAnything');
Route::post('/ask', 'CommonController@askSocialBuAnythingForm');

Route::get('/api', 'CommonController@api');

// promos
// Route::get('/promos/christmas', 'CommonController@christmasPromo');
Route::get('/promos/christmas', 'CommonController@christmasPromo')->name('christmas-yearly-sale');
 Route::get('/promos/black-friday', 'CommonController@blackFridayPromo');

// api docs
Route::get('/developers/docs', 'CommonController@apiDocs');
Route::redirect('/developers', '/developers/docs');

// help center
Route::group(['prefix' => '/help'], function(){
    Route::get('/', 'HelpController@redirectToKB');
});

// social media tools (from csv)
Route::group(['prefix' => '/social-media-scheduling-tools'], function(){
    Route::get('/', 'SocialMediaTools@list');
    Route::get('/cheapest', 'SocialMediaTools@cheapest');
    Route::get('/free', 'SocialMediaTools@free');
    Route::get('/{name}-alternatives', 'SocialMediaTools@toolAlternatives')->where('name', '[A-Za-z0-9_\-]+');
    Route::get('/{name1}-vs-{name2}', 'SocialMediaTools@toolVsTool')->where('name1', '[A-Za-z0-9_\-]+')->where('name2', '[A-Za-z0-9_\-]+');
    Route::get('/offering-live-support', 'SocialMediaTools@offeringLiveSupport');
    Route::get('/for-{industry}', 'SocialMediaTools@forIndustry')->where('industry', '[A-Za-z0-9_\-]+');
    Route::get('/facebook', 'SocialMediaTools@facebook');
    Route::get('/twitter', 'SocialMediaTools@twitter');
    Route::get('/instagram', 'SocialMediaTools@instagram');
    Route::get('/linkedin', 'SocialMediaTools@linkedin');
    Route::get('/{network}', 'SocialMediaTools@networkSpecific')->where('network', '[A-Za-z0-9_\-]+');
});
Route::group(['prefix' => '/compare'], function(){
    Route::get('/', 'ToolCompareController@index');
    Route::get('/{name1}-vs-{name2}', 'ToolCompareController@toolVsTool')->where('name1', '[A-Za-z0-9_\-]+')->where('name2', '[A-Za-z0-9_\-]+');
    Route::get('/{name1}-alternative', 'ToolCompareController@toolAlternative')->where('name1', '[A-Za-z0-9_\-]+');
    Route::get('/{name}', 'ToolCompareController@toolCompareList')->where('name', '[A-Za-z0-9_\-]+');
});

// email confirm
Route::get('/auth/confirm/{token}', 'Auth\LoginController@confirmEmail');

// for mobile app login
Route::post('/auth/login_app', 'Auth\LoginController@appLogin');

// for getting personal access token
Route::post('/auth/access_token', 'Auth\LoginController@getToken'); // for backward compatibility; moved to api routes

// for quick login through a link; this is not used anywhere; but for a partner
Route::get('/auth/quick_login', 'Auth\LoginController@quickLogin');

// api connect redirect
Route::get('/auth/accounts/connect', 'User\AccountsController@redirectToProviderApi');
Route::get('/auth/accounts/callback', 'User\AccountsController@apiConnect')->name('accounts.api-connect');

Route::get('app/accounts/add/{provider}/callback', 'User\AccountsController@handleProviderCallback')->middleware('auth.account-connect')->name('accounts.auth.callback');
Route::post('app/accounts/add/{provider}', 'User\AccountsController@addAccountFromRequest')->middleware('auth.account-connect')->name('accounts.add_from_req');

/** pages which only guests can visit **/
Route::group(['middleware' => 'guest'], function () {

    Route::get('/auth/login', 'Auth\LoginController@showLoginForm')->name('login');
    Route::post('/auth/login', 'Auth\LoginController@login')->middleware('throttle:10,1');

    Route::get('/auth/register', 'Auth\RegisterController@showRegistrationForm')->name('register');
    Route::post('/auth/register', 'Auth\RegisterController@register')->middleware('throttle:10,1');

    Route::get('/auth/reset_password', 'Auth\ForgotPasswordController@showLinkRequestForm');
    Route::post('/auth/reset_password', 'Auth\ForgotPasswordController@sendResetLinkEmail')->middleware('throttle:10,1');

    Route::get('/auth/reset_password/reset/{token}', 'Auth\ResetPasswordController@showResetForm');
    Route::post('/auth/reset_password/reset', 'Auth\ResetPasswordController@reset');

    Route::get('/auth/{provider}', 'Auth\SocialAuthController@redirectToProvider');
    Route::get('/auth/{provider}/callback', 'Auth\SocialAuthController@handleProviderCallback');

});

/** pages which only users can visit **/
Route::post('/auth/logout', 'Auth\LoginController@logout')->name('logout')->middleware('auth');
Route::group(['middleware' => ['auth', RequireOnboardingCompleted::class], 'prefix' => '/app'], function () {

    // onboarding
    Route::get('/onboarding', 'User\OnboardingController@showPage')->name('onboarding');
    Route::get('/onboarding/data', 'User\OnboardingController@getData');
    Route::post('/onboarding/verify_email', 'User\OnboardingController@verifyEmail')->middleware('throttle:3,1');
    Route::post('/onboarding/set_password', 'User\OnboardingController@setPassword');
    Route::post('/onboarding/user_details', 'User\OnboardingController@userDetails');
    Route::post('/onboarding/set_timezone', 'User\OnboardingController@setTimezone');
    Route::post('/onboarding/select_plan', 'User\OnboardingController@selectPlan');
    Route::post('/onboarding/add_accounts', 'User\OnboardingController@addAccounts');
    Route::post('/onboarding/remove_checklist', 'User\OnboardingController@removeChecklist');

    // home
    Route::get('/', 'User\HomeController@index')->name('home');

    // get sidebar menu / general data
    Route::get('/info', 'User\HomeController@info');

    /** these routes will require user email to be verified **/
    Route::group(['middleware' => \App\Http\Middleware\RequireEmailVerified::class], function () {

        // feedback page
        Route::get('give_review', 'User\HomeController@review');

        // roadmap page
        Route::get('roadmap', 'User\HomeController@roadmap')->name('roadmap');

        // referrals system
        Route::get('referrals/', 'User\ReferralsController@index')->name('referrals.index');

        // accounts
        Route::get('accounts/add', 'User\AccountsController@add')->name('accounts.add');
        Route::get('accounts/add/{provider}', 'User\AccountsController@redirectToProvider')->name('accounts.auth');
        // Route::post('accounts/add/{provider}', 'User\AccountsController@addAccountFromRequest')->name('accounts.add_from_req');
        // Route::get('accounts/add/{provider}/callback', 'User\AccountsController@handleProviderCallback')->name('accounts.auth.callback');
        Route::post('accounts/{id}/test', 'User\AccountsController@testConnection')->name('accounts.test_connection');
        Route::resource('accounts', 'User\AccountsController', ['except' => [
            'create', 'store', 'edit', 'show'
        ]]);

        // teams
        Route::post('teams/{id}/invite', 'User\TeamsController@respondToInvite');
        Route::resource('teams', 'User\TeamsController', ['except' => [
            'create', 'show', 'edit'
        ]]);
        // publish
        Route::get('publish/posts', 'User\PostPublishController@index')->name('publish.posts');
        Route::post('publish/posts', 'User\PostPublishController@post');
        Route::patch('publish/posts/{hash}', 'User\PostPublishController@update');
        Route::delete('publish/posts/{hash}', 'User\PostPublishController@destroy');

        Route::post('publish/posts/bulk_delete', 'User\PostPublishController@bulkDelete');
        Route::post('publish/posts/bulk_edit', 'User\PostPublishController@bulkEdit');

        Route::get('publish/drafts', 'User\PostPublishController@getDrafts')->name('publish.drafts');
        Route::get('publish/awaiting_approval', 'User\PostPublishController@getPostsAwaitingApproval')->name('publish.awaiting_approval');
        Route::get('publish/history', 'User\PostPublishController@getHistory')->name('publish.history');
        Route::get('publish/bulk_upload', 'User\BulkUploadController@index')->name('publish.bulk_upload');
        Route::get('publish/bulk_upload/progress', 'User\BulkUploadController@getProgress')->name('publish.bulk_upload.progress');
        Route::post('publish/bulk_upload/reset', 'User\BulkUploadController@reset')->name('publish.bulk_upload.reset');
        Route::post('publish/bulk_upload', 'User\BulkUploadController@import');
        Route::get('publish/calendar', 'User\CalendarController@index')->name('publish.calendar');
        Route::get('publish/calendar/posts', 'User\CalendarController@posts')->name('publish.calendar.posts');

        // publish queues
        Route::get('publish/queues', 'User\QueueController@index')->name('publish.queues.index');
        Route::post('publish/queues', 'User\QueueController@addQueue')->name('publish.queues.add');
        Route::get('publish/queues/{id}', 'User\QueueController@show')->name('publish.queues.show');
        Route::delete('publish/queues/{id}', 'User\QueueController@destroy')->name('publish.queues.destroy');
        Route::get('publish/queues/{id}/posts', 'User\QueueController@getPosts')->name('publish.queues.getPosts');
        Route::post('publish/queues/{id}/posts', 'User\QueueController@addPost')->name('publish.queues.addPost');
        Route::delete('publish/queues/{id}/posts/{postId}', 'User\QueueController@deletePost')->name('publish.queues.deletePost');
        Route::patch('publish/queues/{id}/posts/{postId}', 'User\QueueController@updatePost')->name('publish.queues.updatePost');
        Route::patch('publish/queues/{id}', 'User\QueueController@update')->name('publish.queues.update');
        Route::patch('publish/queues/{id}/change_order', 'User\QueueController@updateOrder');
        Route::patch('publish/queues/{id}/status', 'User\QueueController@updateStatus');
        Route::post('publish/queues/{id}/bulk_delete', 'User\QueueController@bulkDelete');
        Route::post('publish/queues/{id}/shuffle', 'User\QueueController@shuffleOrder');
        Route::post('publish/queues/{id}/cleanup', 'User\QueueController@cleanupPosts');


        // curate
        Route::get('curate', 'User\CurateController@index')->name('curate.index');

        // generate
        Route::get('generate', 'User\GenerateController@index')->name('generate.index');
        Route::get('generate/tweets', 'User\GenerateController@tweets')->name('generate.tweets');
        Route::get('generate/linkedin_posts', 'User\GenerateController@linkedinPosts')->name('generate.linkedin_posts');
        Route::get('generate/instagram_captions', 'User\GenerateController@instagramCaptions')->name('generate.instagram_captions');
        Route::get('generate/tiktok_captions', 'User\GenerateController@tiktokCaptions')->name('generate.tiktok_captions');
        Route::get('generate/facebook_posts', 'User\GenerateController@facebookPosts')->name('generate.facebook_posts');
        Route::get('generate/youtube_video_descriptions', 'User\GenerateController@youtubeVideos')->name('generate.youtube_video_descriptions');
        Route::get('generate/reddit_posts', 'User\GenerateController@redditPosts')->name('generate.reddit_posts');
        Route::get('generate/pinterest_pins', 'User\GenerateController@pinterestPins')->name('generate.pinterest_pins');
        Route::get('generate/google_business_profile_posts', 'User\GenerateController@googleBusinessProfilePosts')->name('generate.google_business_profile_posts');
        Route::get('generate/mastodon_posts', 'User\GenerateController@mastodonPosts')->name('generate.mastodon_posts');
        Route::get('generate/generic_posts', 'User\GenerateController@genericPosts')->name('generate.generic_posts');
        Route::get('generate/{formId}', 'User\GenerateController@dynamicForm')->name('generate.dynamic_form');

        // dashboard
        Route::get('dashboard', 'User\HomeController@dashboard')->name('dashboard');

        // feeds
        Route::get('respond/feeds/{id}/posts', 'User\FeedsController@getPosts');
        Route::get('respond/feeds/{id}/posts/{postId}', 'User\FeedsController@getPost');
        Route::delete('respond/feeds/{id}/posts/{postId}', 'User\FeedsController@deletePost');
        Route::patch('respond/feeds/{id}/posts/{postId}/mark_read', 'User\FeedsController@markRead');
        Route::post('respond/feeds/{id}/posts/{postId}/reply', 'User\FeedsController@reply');
        Route::post('respond/feeds/{id}/posts/{postId}/note', 'User\FeedsController@note');
        Route::post('respond/feeds/{id}/posts/{postId}/like', 'User\FeedsController@like');
        Route::post('respond/feeds/{id}/posts/{postId}/retweet', 'User\FeedsController@retweet');
        Route::resource('respond/feeds', 'User\FeedsController', ['except' => [
            'create', 'edit',
        ]]);

        // automations
        Route::get('automations/list', 'User\AutomationsController@getAutomations');
        Route::patch('automations/{id}/status', 'User\AutomationsController@setStatus');
        Route::get('automations/{id}/log', 'User\AutomationsController@getLog');
        Route::delete('automations/{id}/delete', 'User\AutomationsController@destroy');
        Route::resource('automations', 'User\AutomationsController', ['except' => [
            'create',
        ]]);
        Route::patch('automations/{id}/duplicate', 'User\AutomationsController@duplicate')->name('automations.duplicate');

        // insights
        Route::get('analyze/', 'User\AnalyzeController@index')->name('analyze.index');
        Route::any('analyze/followers', 'User\AnalyzeController@followers')->name('analyze.followers');
        Route::any('analyze/top_posts', 'User\AnalyzeController@topPosts')->name('analyze.top_posts');

        Route::get('analyze/content-performance', 'User\AnalyzeController@content')->name('analyze.content-performance');
        Route::get('analyze/account-performance', 'User\AnalyzeController@account')->name('analyze.account-performance');
        Route::get('analyze/network-performance', 'User\AnalyzeController@network')->name('analyze.network-performance');
        Route::get('analyze/team-performance', 'User\AnalyzeController@team')->name('analyze.team-performance');

        // link shorteners
        Route::get('link_shorteners/{provider}/connect', 'User\LinkShortenersController@redirectToProvider')->name('link_shorteners.auth');
        Route::get('link_shorteners/{provider}/callback', 'User\LinkShortenersController@handleProviderCallback');
        Route::post('link_shorteners/{id}/test', 'User\LinkShortenersController@testConnection');
        Route::resource('link_shorteners', 'User\LinkShortenersController', ['except' => [
            'create', 'show', 'edit'
        ]]);

    });

    // for browser extension
    Route::get('publish/extension_editor', 'User\ExtensionController@getEditor');
    
    Route::get('extension-modal-status', 'User\ExtensionController@getExtensionModalStatus');
    Route::patch('extension-modal-status', 'User\ExtensionController@updateExtensionModalStatus');

    // for share popup
    Route::get('publish/share', 'User\ExtensionController@getEditor');

    // for fetching file to attach for both share popup and browser ext, also used for canva
    Route::get('publish/extension_editor/get_media', 'User\ExtensionController@getMedia');

    // json endpoints
    Route::get('json/search/{type?}', 'Json\JsonSearchController@serve')->name('json.search');
    Route::get('json/data/{type}/{id?}', 'Json\JsonDataController@serve')->name('json.data');
    Route::get('json/collection/{type}', 'Json\JsonCollectionController@serve')->name('json.collection');
    Route::post('json/collection/{type}', 'Json\JsonCollectionController@serve')->name('json.collection');

    // misc
    // get fb user profile pic
    Route::get('_facebook/picture/{account}/{id}', 'User\MiscController@facebookProfilePic');
    // save onboarding tour steps
    Route::post('onboarding/tour_steps', 'User\MiscController@saveOnBoardingTourProgress');
    // proxy request for account images
    Route::get('external/image/{id}', 'User\MiscController@proxyRequestToUrl');
    // forward event to crm
    Route::post('_record_event', 'User\MiscController@recordEventInCrm');
    // flash msg and redirect
    Route::get('_redirect', 'User\MiscController@flashAndRedirect');

    // user settings
    Route::get('settings', 'User\SettingsController@showPage')->name('settings');
    Route::match(['put', 'patch'], 'settings/{type}', 'User\SettingsController@update')->name('settings.update');
    Route::post('settings/resend_verification_email', 'User\SettingsController@resendVerificationEmail')->name('settings.resend_verification_email');
    Route::get('settings/invoices/{invoice}', 'User\SettingsController@downloadInvoice');
    Route::post('settings/delete_account', 'User\SettingsController@deleteAccount');
    Route::post('settings/create_payment_intent', 'User\SettingsController@createPaymentIntent');
    Route::post('settings/regenerate_api_token', 'User\SettingsController@regenerateApiToken')->name('settings.regenerate_api_token');

    // login as a specific user by ID
    Route::get('login_as/{id}', 'Admin\SupportController@loginAs');
});
