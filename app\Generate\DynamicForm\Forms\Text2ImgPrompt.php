<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class Text2ImgPrompt implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'description',
                'label' => 'Describe your image',
                'placeholder' => '',
                'type' => 'text',
                'rules' => 'required|string|max:400',
                'class' => 'col-md-12',
            ],
        ];
    }

    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'POST',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json',
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "You're an expert AI prompt generator for text-to-image models (Stable Diffusion, DALL-E, Midjourney, Jasper Art).", 
                                    "Your job is to turn the provided image description into a short, highly specific, and effective prompt.",
                                    "Prioritize the subject, action, dominant visuals, and atmospheric elements for conciseness. You can include an art style or medium if it enhances the prompt. Ensure the generated prompt avoids unethical content (e.g., hate speech, explicit content, illegal activities). Don't add any quotes around the prompt.",
                                    "Image description: {{form.description}}",
                                    "Generate a concise, effective prompt for the image description above:",
                                ])),
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Prompt:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ]),
                ],
            ],
        ];
    }

    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];
    }

    public function outputComponents(): array
    {
        return [];
    }
}
