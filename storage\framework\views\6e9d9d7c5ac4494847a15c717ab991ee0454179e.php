
<!DOCTYPE html>
<html lang="en"<?php echo $__env->yieldContent('html_tag_str'); ?>>
<head>
    <?php echo $__env->make('layout.includes.default.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body>
<?php echo $__env->make('layout.includes.common_body', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('layout.partials.default.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->yieldContent('content'); ?>

<?php if(request()->path() !== 'demo'): ?>
    <?php echo $__env->make('common.internal.demo-form-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<?php echo $__env->make('layout.partials.default.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('layout.includes.default.footer_html', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->yieldPushContent('footer_html'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/full_width.blade.php ENDPATH**/ ?>