@extends('layout.default')
@php

    $title = 'Free AI Caption Expander';
    $description = 'Develops short social captions into longer, more detailed versions with calls to action.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/caption-expander';
    $form = new \App\Generate\DynamicForm\Form('caption_expander');
    $fields = $form->getFields();
@endphp
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />
    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush

@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Caption',
                    type: 'caption_expander',
                    splitDataBy:'',
                });
            });
        });
        
    </script>
@endpush
@section('content')
    <header class="header">
        <div class="container">
            <div class="row align-items-center h-100">
                <div class="col-md-12 text-center">
                    <h4 class="d-md-block d-none display-4 mb-3 pb-0">
                        Caption Expander 
                    </h4>
                    <h2 class="d-md-none display-6">
                        Caption Expander
                    </h2>
                    <p class="mb-6 pb-2">Expand captions, amplify messages, inspire action. No sign up required!</p>                    
                </div>
                <div class="col-md-8 mx-auto w-100 mh-350">
                    <div id="generate-tool-output">
                        <div class="text-center">
                            <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <div class="container-fluid bg-dark-1">
            <section class="section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2 class="text-white display-3 mb-6 pb-2">Expand Your Captions Using AI in<br class="d-md-block d-none"> 5 Simple Steps</h2>
                        </div>
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-241 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">1</h2>
                                    <h5 class="text-white mb-3">Add a Short Caption</h5>
                                    <p class="text-white opacity-75 mb-md-5 mb-0">
                                        Start with a short, punchy phrase or sentence that summarizes your core message or idea.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-241 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">2</h2>
                                    <h5 class="text-white mb-3">Define the Key Message</h5>
                                    <p class="text-white opacity-75 mb-0">
                                        Specify what you want your expanded caption to communicate — such as a motivational story or product insight.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-4">
                            <div class="card h-241 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">3</h2>
                                    <h5 class="text-white mb-3">Select Word Limit</h5>
                                    <p class="text-white opacity-75 mb-md-5 mb-0">
                                        Choose the maximum length for your expanded caption. This helps tailor it for concise or long-form content.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 pt-1 mb-md-0 mb-4">
                            <div class="card h-241 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">4</h2>
                                    <h5 class="text-white mb-3">Choose Platform</h5>
                                    <p class="text-white opacity-75 mb-0">
                                        The AI adapts tone and format based on your platform — be it a short tweet, Instagram caption, or LinkedIn post.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 pt-1 mb-md-0 mb-4">
                            <div class="card h-241 bg-transparent-6 rounded-lg">
                                <div class="card-body p-5">
                                    <h2 class="display-1 bg-gradient-1">5</h2>
                                    <h5 class="text-white mb-3">Generate and Reset</h5>
                                    <p class="text-white opacity-75 mb-0">Click "Generate Caption" to create your caption instantly.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>
@endsection
