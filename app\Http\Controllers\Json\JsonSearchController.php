<?php

namespace App\Http\Controllers\Json;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Account;
use App\Helpers\ApiHelper;
use App\Helpers\Hashtags;
use App\Helpers\Instagram;
use App\Team;
use App\User;
use Auth;
use Exception;
use Facebook\Facebook;
use GuzzleHttp\Psr7\UriResolver;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use stdClass;

class JsonSearchController extends Controller
{

    /**
     * Serve the request
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function serve(Request $request, $type = null)
    {

        if(!in_array($type, ['accounts', 'teams', 'member', 'meta_locations', 'linkedin_companies', 'twitter_users', 'ig_users', 'suggested_hashtags', 'url_data']))
            abort(404);

        if(!$request->has('q'))
            abort(400, 'Query is not provided.');

        $result = $this->{$type}($request);

        return response()->json([
            'items' => $result,
            'total_count' => count($result),
        ]);
    }

    /**
     * Accounts search
     * @param Request $request
     * @return array
     */
    public function accounts(Request $request)
    {
        $result = Account::ofUser()->where('active', true)->where('name', 'like','%' . $request->input('q') . '%')->get();
        return $result->map(function ($a){
            return Account::transform($a);
        });
    }

    /**
     * Teams search
     * @param Request $request
     * @return array|Collection
     */
    public function teams(Request $request)
    {
        $result = user()->joinedTeams()->where('name', 'like', '%' . $request->input('q') . '%')->get();
        return $result->map(function($t){
           return Team::transform($t);
        });
    }

    /**
     * Members match. Check if a member with specified email exists
     * @param Request $request
     * @return array
     */
    public function member(Request $request)
    {
        $result = User::where('email', $request->input('q'))->where('email', '<>', Auth::user()->email)->get();
        if($result->count() === 0){
            if (filter_var($request->input('q'), FILTER_VALIDATE_EMAIL) && trim($request->input('q')) !== Auth::user()->email) {
                return [
                    [
                        'id' => $request->input('q'),
                        'name' => $request->input('q'),
                        'email' => $request->input('q'),
                        'image' => null,
                        'invite' => true,
                    ]
                ];
            }
        }
        return $result->map(function ($u){
            return User::transform($u);
        });
    }

    /**
     * IG Locations search
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function ig_locations(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string',
        ]);
        try {
           return []; // todo: implement
        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

    /**
     * FB Locations search
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function meta_locations(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string',
            'account_id' => 'required|exists:accounts,id',
        ]);
        /** @var Account $acc */
        $acc = user()->getAvailableAccounts()->firstWhere('id', $request->input('account_id'));
        if(!$acc) abort(400, 'Account not found');

        // $access = json_decode($acc->token);
        // $accessToken = $access->token;

        try {
            /** @var Facebook $fb */
            $fb = $acc->getApi();
            $data = $fb->get('/pages/search?q=' . urlencode($request->input('q')) . '&fields=id,name,location,link')->getGraphEdge()->asArray();
            $all = [];
            foreach ($data as $datum){
                $all[] = [
                    'title' => $datum->name,
                    'subtitle' => implode(', ', [$datum->location->street, $datum->location->city, $datum->location->state, $datum->location->country]),
                    'lat' => $datum->location->latitude,
                    'lng' => $datum->location->longitude,
                    'id' => $datum->id,
                    'place_id' => $datum->id,
                ];
            }
            return $all;
        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

    /**
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function linkedin_companies(Request $request){
        $this->validate($request, [
            'q' => 'required|string|min:3',
            'account_id' => 'required|exists:accounts,id',
        ]);

        /** @var Account $acc */
        $account = user()->getAvailableAccounts()->firstWhere('id', $request->input('account_id'));
        if(!$account) abort(400, 'Account not found');

        $linkedin = $account->getApi();
        try {
            $data = $linkedin->get('companySearch?q=search&query='. urlencode($request->input('q')) .'&projection=(elements*(entity~(id,localizedName,logoV2(original~:playableStreams))),paging)');
            $all = [];
            if(!isset($data["elements"]) || !is_array($data["elements"])) return [];
            foreach ($data["elements"] as $i) {
                if(isset($i["entity~"]) && isset($i["entity~"]["logoV2"])){
                    $i["image"] = $i["entity~"]['logoV2']['original~']['elements'][0]['identifiers'][0]['identifier'];
                } else {
                    $i["image"] = '/images/no-image.png';
                }
                $i['title'] = $i["entity~"]['localizedName'];
                $all[] = $i;
            }
            return $all;
        }catch (Exception $exception){
            report($exception);
            return [];
        }
        
    }

        /**
     * Twitter Users search
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function twitter_users(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string',
            'account_id' => 'required|exists:accounts,id',
        ]);
        /** @var Account $acc */
        $acc = user()->getAvailableAccounts()->firstWhere('id', $request->input('account_id'));
        if(!$acc) abort(400, 'Account not found');

        try {
            /** @var TwitterOAuth $twitter */
            $twitter = $acc->getApi();
            $data = $twitter->get('users/search', [
                'q' => $request->input('q'),
                'count' => 10,
            ]);

            if(!is_array($data) && isset($data->errors)){
                // some error
                return [];
            }

            $all = [];
            foreach ($data as $datum){
                $all[] = [
                    'title' => $datum->name,
                    'screen_name' => $datum->screen_name,
                    'id' => $datum->id,
                    'id_str' => $datum->id_str,
                    'image' => $datum->profile_image_url_https,
                    'verified' => $datum->verified
                ];
            }
            return $all;
        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

    /**
     * IG Locations search
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function ig_users(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string',
            'account' => 'required|integer'
        ]);
        try {
            return []; // todo: implement
        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

    /**
     * Get suggested hashtags from text
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function suggested_hashtags(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string',
            'accounts.*' => 'required|integer',
        ]);
        // get accounts
        $accounts = user()->getAvailableAccounts()->whereIn('id', $request->input('accounts', []));
        try {
            return Hashtags::getSuggested($accounts, $request->input('q'));
        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

    /**
     * Get url metadata (used for preview in ui)
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function url_data(Request $request)
    {
        $this->validate($request, [
            'q' => 'required|string|url|active_url',
        ]);

        try {

            $meta_tags = \Cache::remember('url_data.' . md5($request->input('q')), now()->addDays(4), function () use($request) {
                return extract_meta_tags($request->input('q'));
            });

            // load image from link's meta
            $image = null;
            try {
                foreach (['og:image', 'twitter:image', 'image',] as $tag) {
                    if(isset($meta_tags[$tag]) && !empty($meta_tags[$tag])){
                        /** @var string $image */
                        $image = $meta_tags[$tag];
                        // convert to abs url if it is relative
                        if(!starts_with($image, 'http')){
                            $image = (string) UriResolver::resolve( \GuzzleHttp\Psr7\uri_for($request->input('q')), \GuzzleHttp\Psr7\uri_for($image) );
                        }
                        break;
                    }
                }
            } catch (\Exception $exception){
                report($exception);
            }
            if($image && strlen($image) > 0){
                $decodedImg = htmlspecialchars_decode($image);
                if( $decodedImg !== $image ){
                    $image = $decodedImg;
                }
            }

            // get description
            $description = null;
            foreach (['og:description', 'twitter:description', 'description'] as $tag){
                if(isset($meta_tags[$tag])){
                    $description = $meta_tags[$tag];
                    break;
                }
            }

            // load title
            $title = '';
            foreach (['title', 'og:title', 'twitter:title'] as $tag){
                if(isset($meta_tags[$tag])){
                    $title = $meta_tags[$tag];
                    break;
                }
            }

            return [ // has to be an array
                [
                    'title' => htmlspecialchars_decode($title),
                    'description' => htmlspecialchars_decode($description),
                    'image' => $image,
                ]
            ];

        } catch (Exception $exception){
            report($exception);
            return [];
        }
    }

}
