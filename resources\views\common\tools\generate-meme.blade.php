@extends('layout.default')
@php($title = 'AI Meme Generator - Generate Memes From Text')
@php($description = 'Quickly generate new memes using AI. This tool will generate a meme from your text content. You can use this tool to generate memes for your social media posts.')
@php($image = 'https://socialbu.com/images/site/robot_working_on_paper.png')
@php($url = 'https://socialbu.com/tools/generate-meme')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')
    <header class="header pb-0 bg-socialbu-light">
        <div class="container">
            <div class="row">
                <div class="col-md-8 mx-auto">

                    <div class="d-flex align-items-start">
                        <div title="This AI Writer is your friend" class="border-right border-secondary w-100px h-100px d-flex align-items-center justify-content-center" data-toggle="tooltip">
                            <i class="ph ph-magic-wand ph-3xl socialbu-color"></i>
                        </div>
                        <div class="ml-4 pl-4">
                            <h1 class="display-1">
                                AI Meme Generator
                            </h1>
                            <p class="text-muted">
                                Generate memes quickly using AI.
                            </p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="section py-6 bg-socialbu-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-8 offset-md-2">
                        <div class="card border border-secondary shadow">
                            <div class="card-body">
                                <form method="POST" id="generate_content_form">
                                    <div class="form-group">
                                        <label>Context</label>
                                        <input type="text" name="content" class="form-control"
                                                  placeholder="Provide context (text) for generating the meme" required />
                                        <input type="hidden" name="type" value="meme_text2img" />
                                        <small class="form-text text-muted">
                                            The meme will be generated for this text context.
                                        </small>
                                    </div>
                                    <div class="form-group text-center">
                                        <button class="btn btn-primary btn-sm" type="submit">
                                            <i class="ph ph-gear-sixs"></i> Generate
                                        </button>
                                    </div>
                                </form>
                                <hr/>
                                <div class="form-group">
                                    <div class="card border border-primary">
                                        <div class="card-body">
                                            <div class="position-absolute bg-white" style="top: 10px; right: 10px;">
                                                <button class="btn btn-outline-primary btn-sm" data-name="Meme" id="download_btn" style="display: none;">
                                                    <i class="ph ph-download-simple"></i>
                                                    <span class="copy-text">Download</span>
                                                </button>
                                            </div>
                                            <div class="lead text-dark pt-3" id="generated_content">
                                                Generated content will show here.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section py-7 bg-socialbu-light border-bottom border-top mb-6" id="more_features" style="display:none;">
            <div class="container-fluid">
                <header class="section-header mb-7">
                    <h2 class="display-2">There is always more.</h2>
                </header>
                <div class="row">
                    <div class="col">
                        <div class="_placeholder p-0">

                        </div>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="row">
                    <div class="col text-center py-7 pt-7">

                        <a class="btn btn-lg btn-primary btn-round" href="/auth/register">
                            Try now
                        </a>
                        <a class="btn btn-lg btn-light btn-round" href="/pricing">
                            Compare Plans
                        </a>

                    </div>
                </div>
            </div>
        </section>

        @include('common.internal.testimonials-block')

        @include('common.internal.join-us-block')


    </main>

@endsection
