<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class TiktokCaptionGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'topic',
                'label' => 'Topic',
                'placeholder' => 'Enter your description',
                'type' => 'text',
                'rules' => 'required|string|max:60',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'tone',
                'label' => 'Tone',
                'type' => 'select',
                'options' => [
                    ['value' => 'funny', 'label' => 'Funny','selected' => false],
                    ['value' => 'aesthetic', 'label' => 'Aesthetic', 'selected' => false],
                    ['value' => 'cool', 'label' => 'Cool', 'selected' => false],
                    ['value' => 'professional', 'label' => 'Professional', 'selected' => false],
                ],
                'rules' => 'required|in:funny,aesthetic,cool,professional',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'variant_count',
                'label' => 'Variant Count',
                'type' => 'select',
                'options' => [
                    ['value' => '2', 'label' => '2', 'selected' => false],
                    ['value' => '3', 'label' => '3', 'selected' => false],
                    ['value' => '4', 'label' => '4', 'selected' => false],
                    ['value' => '5', 'label' => '5', 'selected' => false],
                ],
                'rules' => 'required|in:2,3,4,5',
                'class' => 'col-md-6',
            ],
           [
               'id' => 'hashtag',
               'label' => 'Add Hashtags',
               'type' => 'checkbox',
               'rules' => 'boolean',
                'class' => 'col-md-12',
           ],
           [
               'id' => 'emoji',
               'label' => 'Emojis',
               'type' => 'checkbox',
               'rules' => 'boolean',
                'class' => 'col-md-12',
           ],
        ];
    }


    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'Post',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json', // can be json, form, or multipart
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "Generate {{form.variant_count}} creative and engaging TikTok captions with a {{form.tone}} tone.",
                                    "The topic or subject is: {{form.topic}}",
                                    "Each caption should be optimized for TikTok's format - short, catchy, and designed to drive engagement.",
                                    "{% if form.hashtag == true %} Include relevant hashtags.{% endif %}",
                                    "{% if form.emoji == true %} Add appropriate emojis.{% endif %}",
                                    "Caption requirements:",
                                    "- Hook viewers in the first 3 seconds",
                                    "- Keep under 150 characters",
                                    "- Use trending TikTok language and style",
                                    "- Be scroll-stopping and shareable",
                                    "Do not include any additional text or explanations.",
                                    'Separate each caption with "---END_CAPTION---" except the last one.',
                                ]))
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Posts:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ])
                ],
            ],

        ];
    }


    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];

    }

    public function outputComponents(): array
    {
        return [];
    }
}