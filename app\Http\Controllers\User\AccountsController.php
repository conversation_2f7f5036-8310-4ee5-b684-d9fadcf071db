<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\Helpers\ApiHelper;
use App\Helpers\Instagram;
use App\User;
use Facebook\GraphNodes\GraphEdge;
use Facebook\GraphNodes\GraphPage;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\SendHTTPRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\View\View;
use InvalidArgumentException;
use Laravel\Socialite\Two\AbstractProvider;
use Laravel\Socialite\Two\InvalidStateException;

class AccountsController extends Controller
{

    static $PROVIDERS = ['facebook', 'twitter', 'instagram', 'linkedin', 'google', 'mastodon', 'tiktok', 'reddit', 'youtube', 'gmb', 'pinterest', 'threads', 'bluesky'];

    static public function getConnectRequestData($key = null, $default = null){
        $data = session()->get('accounts.connect_request', null);

        if($data && isset($data['timestamp'])){
            // if it's older than 1 hr, remove it
            if(time() - $data['timestamp'] > 3600){
                session()->remove('accounts.connect_request');
                return $default;
            }
        }

        if (!$data) {
            return $default;
        }

        if($key){
            return Arr::get($data, $key, $default);
        } else {
            return $data;
        }
    }

    public function removeConnectRequestData(){
        session()->remove('accounts.connect_request');
    }

    /**
     * Redirect for use via API
     * @param Request $request
     * @return Response|View
     * @throws \Exception
     */
    public function redirectToProviderApi(Request $request){
            
        $connectToken = $request->input('connect_token');

        if(!$connectToken){
            abort(400, 'Invalid request');
        }

        // decrypt the connect token
        try {
            $connectToken = decrypt($connectToken);
        } catch (\Exception $exception){
            abort(400, 'Invalid request');
        }

        // connect token has the user id and the provider
        $connectTokenParts = explode(':', $connectToken);

        if(count($connectTokenParts) !== 2){
            abort(400, 'Invalid request');
        }

        $userId = $connectTokenParts[0];

        $provider = $connectTokenParts[1];

        if (!in_array($provider, self::$PROVIDERS)){
            abort(400, 'Provider not supported');
        }

        if(!$userId){
            abort(400, 'Invalid user id');
        }

        $user = User::find($userId);
        if(!$user){
            abort(404, 'User not found');
        }

        $postbackUrl = $request->input('postback_url');

        // now, store data in session
        session()->put('accounts.connect_request', [
            'user_id' => $user->id,
            'provider' => $provider,
            'postback_url' => $postbackUrl,
            'return_url' => $request->input('return_url'),
            'account_id' => $request->input('account_id'), // if reconnecting an existing account
            'timestamp' => time(),
        ]);

        if($provider === 'mastodon'){
            return $this->apiConnect($request, [
                'open_add_mastodon_modal'  => true,
            ]);
        } else if($provider === 'bluesky'){
            return $this->apiConnect($request, [
                'open_add_bluesky_modal'  => true,
            ]);
        }

        // redirect to the provider
        return $this->redirectToProvider($request, $provider);
    }
    /**
     * Start authorize process for the social account
     *
     * @param Request $request
     * @param String|null $provider
     * @param int $attempt
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     * @throws \Exception
     */
    public function redirectToProvider(Request $request, string $provider = null, int $attempt = 1)
    {
        if($provider === 'youtube'){
            $provider = 'google';
            $request->merge(['service' => 'youtube']);
        } else if($provider === 'gmb'){
            $provider = 'google';
            $request->merge(['service' => 'gmb']);
        }

        if (!in_array($provider, self::$PROVIDERS))
            abort(404);

        $isAPIRequest = !!self::getConnectRequestData();

        $redirectRoute = $isAPIRequest ? 'accounts.api-connect' : 'accounts.index';

        // set callback url
        config([
            'services.' . $provider . '.redirect' => route('accounts.auth.callback', ['provider' => $provider])
        ]);

        if($provider === 'mastodon') {
            $account_id = $request->input('account_id');

            if ($account_id) {
                $account = Account::ofUser($isAPIRequest ? self::getConnectRequestData('user_id') : user()->id)->findOrFail($account_id);
                $token = json_decode($account->token, true);

                $request->merge(['domain' => $token['domain']]); // set the domain in the request; mastodon requires domain too
            }

            // make sure the domain is passed in the request
            $this->validate($request, [
                'domain' => 'required',
            ]);

            // get the domain without the protocol
            $domain = $request->input('domain');

            // if $domain is actually mastodon username, get domain from it
            if (Str::contains($domain, '@')) {
                // get domain after the last @
                $domain = Arr::last(explode('@', $domain));
            }

            // trim to be sure
            $domain = trim($domain);

            // if $host is url, get host from it
            if (!filter_var($domain, FILTER_VALIDATE_URL)) {
                $domain = 'https://' . $domain;
            }

            // make sure the host is not empty
            if (empty($domain)) {
                flash('Invalid domain for mastodon', 'error');
                return redirect()->route($redirectRoute);
            }

            try {
                $credentials = ApiHelper::getMastodonCredentials($domain);
            } catch (\Exception $exception) {
                flash('Error creating mastodon app: ' . $exception->getMessage() . '. Make sure the domain entered is valid and working.', 'error');
                return redirect()->route($redirectRoute);
            }

            // we have credentials now, set them in config
            config([
                'services.mastodon.client_id' => $credentials['client_id'],
                'services.mastodon.client_secret' => $credentials['client_secret'],
                'services.mastodon.domain' => $domain,
            ]);

            // store the domain in session, so we can use it later
            session(['mastodon_domain' => $domain]);

        } else if($provider === 'bluesky'){

            // just redirect with accounts.open_add_bluesky_modal = true
            return redirect()->route($redirectRoute)->with('accounts.open_add_bluesky_modal', true);

        } else {
            // check if socialite config exists
            if (!config('services.' . $provider)) {

                $account_id = $request->input('account_id');

                if($account_id){
                    flash('Re-add the account to reconnect it', 'error');
                } else {
                    flash('Provider not supported', 'error');
                }

                return redirect()->route($redirectRoute);

            }
        }

        $social_driver = \Socialite::driver($provider);

        // set scopes for each provider where needed
        if ($provider == 'facebook') {
            $social_driver->scopes(config('services.facebook.required_scopes'));
        } else if ($provider == 'linkedin') {
            $social_driver->scopes(config('services.linkedin.required_scopes'));
        } else if ($provider == 'google') {
            $service = $request->input('service', 'gmb');

            if(!in_array($service, ['gmb', 'youtube'])){
                abort(400);
            }

            $scopes = config('services.google.required_scopes.' . $service);
            if(!$scopes) $scopes = config('services.google.required_scopes.gmb');

            $social_driver->scopes($scopes);

            // set the service in session, so we can use it later
            session(['accounts.connect.google_service' => $request->input('service')]);

            $social_driver->with([
                'access_type' => 'offline', // needed so we get refresh token too, which will be used to auto-renew access token
                'prompt' => 'consent select_account' // show accounts selector and login screen every time
            ]);
        } else if ($provider == 'mastodon') {
            $social_driver->scopes(config('services.mastodon.required_scopes'));
        } else if($provider === 'tiktok'){
            $social_driver->scopes(config('services.tiktok.required_scopes'));
        } else if($provider === 'twitter'){
            $social_driver->scopes(config('services.twitter.required_scopes'));
        } else if($provider === 'reddit'){
            $social_driver->scopes(config('services.reddit.required_scopes'));
            $social_driver->with([
                'duration' => 'permanent', // so we get a refresh token too
            ]);
        } else if($provider === 'pinterest'){
            $social_driver->scopes(config('services.pinterest.required_scopes'));
        } else if ($provider == 'threads') {
            $social_driver->scopes(config('services.threads.required_scopes'));
        } else if ($provider == 'instagram') {
            $social_driver->scopes(config('services.instagram.required_scopes'));
        }

        try {
            return $social_driver->redirect();
        } catch (\Exception $exception){
            if($attempt > 5) {
                throw $exception;
            } else {
                // retry (workaround for random curl error 35: unknown ssl protocol)
                return $this->redirectToProvider($request, $provider, $attempt + 1);
            }
        }
    }

    /**
     * Start authorize process for the social account
     *
     * @param Request $request
     * @param String|null $provider
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     * @throws \Exception
     */
    public function handleProviderCallback(Request $request, string $provider = null)
    {
        if (!config('services.' . $provider) || $provider == null)
            abort(404);

        if (!in_array($provider, self::$PROVIDERS))
            abort(404);

        $isAPIRequest = !!self::getConnectRequestData();
        $redirectRoute = $isAPIRequest ? 'accounts.api-connect' : 'accounts.index';

        if($request->input('error_code', false)){
            flash('Error ' . $request->input('error_code') . ': ' . $request->input('error_message', 'Authentication error'), 'error');
            return redirect()->route($redirectRoute);
        } else if($request->has('error')){
            if($request->has('error_description')){
                flash('Error ' . $request->input('error') . ': ' . $request->input('error_description', 'Authentication error'), 'error');
            } else {
                flash('Error: ' . $request->input('error'), 'error');
            }
            return redirect()->route($redirectRoute);
        }

        if($isAPIRequest){
            // get user from session/connect request
            $user = User::find(self::getConnectRequestData('user_id'));

            if(!$user){
                // should not happen
                flash('User not found', 'error');
                return redirect()->route($redirectRoute);
            }

        } else {
            $user = user();
        }

        // set callback url; not sure if really needed here
        config([
            'services.' . $provider . '.redirect' => route('accounts.auth.callback', array_filter(['provider' => $provider,]))
        ]);


        if($provider === 'mastodon'){

            // get $domain from session
            $domain = session()->pull('mastodon_domain');

            // make sure the host is not empty
            if (empty($domain)) {
                flash('No valid authorization data for Mastodon. Session was likely expired. Please try again!', 'error');
                return redirect()->route($redirectRoute);
            }

            try {
                $credentials = ApiHelper::getMastodonCredentials($domain);
                // we have credentials now, set them in config
                config([
                    'services.mastodon.client_id' => $credentials['client_id'],
                    'services.mastodon.client_secret' => $credentials['client_secret'],
                    'services.mastodon.domain' => $domain,
                ]);
            } catch (\Exception $exception){
                flash('Error getting mastodon app: ' . $exception->getMessage(), 'error');
                return redirect()->route($redirectRoute);
            }
        } else if($provider === 'reddit') {
            // wait for like 5s
            sleep(30);
        }

        try {

            /** @var AbstractProvider|\Laravel\Socialite\One\AbstractProvider $driver */
            $driver = \Socialite::driver($provider);

            if($provider === 'reddit'){

                // user agent should be: <platform>:<app ID>:<version string> (by /u/<reddit username>)
                // example: android:com.example.myredditapp:v1.2.3 (by /u/kemitche)
                $userAgent = 'web:socialbu.com:v1.0 (by /u/usamaejazch)';

                // we choose a random proxy; because reddit can rate-limit us if we use the same IP
                $proxy = null;
                if(app()->environment('production')) {
                    $proxy = array_random(array_merge(config('app.proxies'), [null]));
                }

                // we need to set custom proxy and user agent for reddit
                $redditGuzzle = guzzle_client(array_filter([
                    'proxy' => $proxy,
                    'headers' => [
                        'User-Agent' => $userAgent,
                    ],
                ]));
                $driver->setHttpClient($redditGuzzle);
            }

            $social_user = $driver->user();
        } catch (InvalidStateException $exception){
            flash('Invalid (or expired) state. Please try again.', 'error');
            return redirect()->route($redirectRoute);
        } catch (InvalidArgumentException $exception){
            flash('Invalid request. Please try again.', 'error');
            return redirect()->route($redirectRoute);
        } catch (\Throwable $e) {

            if(Str::contains($e->getMessage(), ['null given', 'invalid_request',])){
                flash('Session state expired or invalid. Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            if($e instanceof ServerException){
                flash('Auth error: ' . $e->getResponse()->getStatusCode() . '. Please retry later.', 'error');
                return redirect()->route($redirectRoute);
            }

            if(Str::contains($e->getMessage(), '429')){
                // added for reddit
                $context = [
                    'user' => $user->id,
                ];

                if ($e instanceof ClientException && $e->getResponse()){
                    $context['body'] = $e->getResponse()->getBody()->getContents();
                    $context['headers'] = $e->getResponse()->getHeaders();
                }

                \Log::error('Reddit 429 error: ' . $e->getMessage(), $context);
                flash('The server is receiving too many requests. Please try some minutes later.', 'error');
                return redirect()->route($redirectRoute);
            }

            if(Str::contains($e->getMessage(), 'must be an instance of')){
                flash('Session state expired or invalid. Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            // for twitter
            if(Str::contains($e->getMessage(), ['feature is temporarily unavailable'])){
                // League\OAuth1\Client\Credentials\CredentialsException
                // Received HTTP status code [401] with message "This feature is temporarily unavailable" when getting token credentials.
                flash('Twitter is temporarily unavailable. Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            // if user is suspended (was added initially for Twitter)
            if($e instanceof ClientException){
                $body = $e->getResponse()->getBody()->getContents();

                if(Str::contains($body, ['is suspended'])){
                    flash('Your account looks suspended and cannot be added to SocialBu.', 'error');
                    return redirect()->route($redirectRoute);
                }
            }

            report($e);
            flash('Error: ' . $e->getMessage(), 'error');
            return redirect()->route($redirectRoute);
        }

        if ($provider === 'twitter') {
            // add the account to db
            $this->addToDb([
                'id' => $social_user->getId(),
                'user_id' => $user->id,
                'name' => $social_user->getName(),
                'token' => [
                    'token' => $social_user->token,
                    'refresh' => $social_user->refreshToken,
                    'expires_at' => time() + $social_user->expiresIn,
                    'username' => $social_user->getNickname(),
                ],
                'type' => $provider . '.profile',
                'login_via_new_x_api' => true,
            ]);
            if($isAPIRequest){
                $this->removeConnectRequestData();
            }
            return redirect()->route($redirectRoute);

        } elseif ($provider === 'instagram') {
            // add the account to db
            $this->addToDb([
                'id' => $social_user->getId(),
                'user_id' => $user->id,
                'name' => $social_user->getName(),
                'token' => [
                    'token' => $social_user->token,
                    'username' => $social_user->getName(),
                ],
                'type' => $provider . '.api',
                'via_instagram_login' => true,
            ]);

            return redirect()->route($redirectRoute);
        } elseif ($provider === 'facebook') {
            // pass to template and present popup to select which page or profile to add
            $fb = ApiHelper::getFacebook($social_user->token);

            $facebook_accounts_data = [];
            try{
                $fb_edge = $fb->get('/me/groups?admin_only=true')->getGraphEdge('GraphPage');
                do{
                    foreach ($fb_edge as $graphNode){
                        $facebook_accounts_data[$graphNode->getId()] = $graphNode->asArray();
                        $facebook_accounts_data[$graphNode->getId()]['_type'] = 'group';
                        $facebook_accounts_data[$graphNode->getId()]['_token'] = [
                            'token' => $social_user->token,
                            'user' => $social_user->getId(),
                            'name' => $graphNode->getName()
                        ];
                    }
                    $fb_edge = $fb->next($fb_edge);
                }while($fb_edge !== null);
            } catch(\Exception $exception){
                report($exception);
                return errorPage([
                    'title' => 'Some error while getting fb groups data',
                    'message' => $exception
                ]);
            }

            // get pages
            try {
                /** @var GraphEdge $fb_edge */
                $fb_edge = $fb->get('/me/accounts?fields=id,name,tasks,access_token,connected_instagram_account{id,username,ig_id}')->getGraphEdge('GraphPage');
                do {

                    if($user->getOption('debug')){
                        \Log::info(json_encode($fb_edge->asArray()));
                    }

                    foreach ($fb_edge as $graphNode) {
                        /** @var GraphPage $graphNode */

                        $hasPerms = true;

                        $tasks = $graphNode->getField('tasks');
                        if($tasks) {
                            $pagePerms = $tasks->asArray();
                        } else {
                            $pagePerms = config('services.facebook.page_perms'); // when tasks is null, this can happen when access is solely using business manager for example, and there is no link to the page with a personal profile
                        }
                        foreach (config('services.facebook.page_perms') as $requiredPerm){
                            if(!in_array($requiredPerm, $pagePerms))
                                $hasPerms = false;
                        }

                        if(!$hasPerms) {
                            continue;
                        }

                        $facebook_accounts_data[$graphNode->getId()] = $graphNode->asArray();
                        $facebook_accounts_data[$graphNode->getId()]['_type'] = 'page';
                        $facebook_accounts_data[$graphNode->getId()]['_token'] = [
                            'token' => $graphNode->getAccessToken(),
                            'user' => $social_user->getId(),
                        ];

                        if($graphNode->getField('connected_instagram_account')){

                            $igUser = $graphNode->getField('connected_instagram_account')->asArray();

                            // has an IG account linked
                            $facebook_accounts_data[$igUser['id']] = $igUser;
                            $facebook_accounts_data[$igUser['id']]['name'] = $igUser['username'];
                            $facebook_accounts_data[$igUser['id']]['_type'] = 'instagram';
                            $facebook_accounts_data[$igUser['id']]['_token'] = [
                                'token' => $social_user->token, // user token, it is
                                'user' => $social_user->getId(),
                                'ig_id' => $igUser['ig_id'],
                                'username' => $igUser['username'],
                                'page_token'=> $graphNode->getAccessToken(),
                                'page_id' => $graphNode->getId(),
                            ];
                        }

                    }

                    $fb_edge = $fb->next($fb_edge);

                } while ($fb_edge !== null);
            } catch (\Exception $e) {
                report($e);
                return errorPage([
                    'title' => 'Facebook returned an error',
                    'message' => $e
                ]);
            }

            session(['accounts.auth_fb_accounts' => $facebook_accounts_data]);

            return redirect()->route($redirectRoute);
        } elseif ($provider === 'linkedin') {
            // pass to template and present popup to select which page or profile to add
            $token = [
                'token' => $social_user->token,
                'expiresAt' => time() + $social_user->expiresIn,
                'refreshToken' => $social_user->refreshToken,
            ];

            $linkedin = ApiHelper::getLinkedIn($token);

            try {

                $profileDetails = $linkedin->get('me', [
                    'projection' => '(id,vanityName,localizedFirstName,localizedLastName,profilePicture(displayImage~:playableStreams))',
                ]);

                $linkedin_accounts_data = [
                    $social_user->getId() => [
                        'id' => $social_user->getId(),
                        'name' => $social_user->name,
                        'image' => Arr::get($profileDetails, 'profilePicture.displayImage~.elements[0].identifiers[0].identifier', '/images/no-image.png'),
                        '_type' => 'profile',
                        '_token' => $token,
                    ],
                ];

                $rolesToFetch = ['ADMINISTRATOR', 'CONTENT_ADMINISTRATOR',];

                foreach($rolesToFetch as $role){

                    $start = 0;
                    $count = 100;
                    do {
                        $res = $linkedin->get('organizationalEntityAcls', [
                            'q'=> 'roleAssignee',
                            'role' => $role,
                            'state' => 'APPROVED',
                            'projection' => '(elements*(*,roleAssignee~(localizedFirstName, localizedLastName), organizationalTarget~(id, localizedName, logoV2(original~:playableStreams,cropped~:playableStreams,cropInfo))))',
                            'count' => $count,
                            'start' => $start,
                        ]);

                        foreach ($res['elements'] as $acl) {

                            $orgId = $acl['organizationalTarget'];
                            $org = $acl['organizationalTarget~'];

                            $orgName = $org['localizedName'];

                            $linkedin_accounts_data[$org['id']] = [
                                'name' => $orgName,
                                'id' => $org['id'],
                                'image' => isset($org['logoV2']) ? $org['logoV2']['cropped~']['elements'][0]['identifiers'][0]['identifier'] : null,
                                '_type' => Str::contains($orgId, 'organizationBrand') ? 'brand' : 'org',
                                '_token' => array_merge(
                                    $token,
                                    [
                                        'user' => $social_user->getId(),
                                    ]
                                ),
                            ];
                        }

                        if(!isset($res['elements']) || count($res['elements']) < $count){
                            // no more next page
                            break;
                        } else {
                            $start += $count;
                        }

                    } while ($start < 100); // only max 10 pages
                }

            } catch (\Exception $e) {
                report($e);
                return errorPage([
                    'title' => 'LinkedIn returned an error',
                    'message' => $e
                ]);
            }

            session(['accounts.auth_linkedin_accounts' => $linkedin_accounts_data]);

            $u = user();
            if($u && $u->getOption('debug')){
                \Log::info('LinkedIn accounts data: ' . json_encode($linkedin_accounts_data));
            }

            return redirect()->route($redirectRoute);

        } elseif ($provider === 'google') {
            // pass to template and present popup to select which page or profile to add

            $service = session('accounts.connect.google_service');

            $token = [
                'token' => $social_user->token,
                'expiresAt' => time() + $social_user->expiresIn,
                'expiresIn' => $social_user->expiresIn,
                'refreshToken' => $social_user->refreshToken,
            ];

            $google_accounts_data = [
                'gmb' => [],
                'youtube' => [],
            ];

            if(!$service || $service === 'gmb'){
                // get all accounts for gmb
                $myBusinessService = ApiHelper::getGoogleMyBusiness($token);
                $myBusinessAccountManagementService = ApiHelper::getGoogleMyBussinessAccountManagement($myBusinessService->getClient());
                $myBusinessInformationService = ApiHelper::getGoogleMyBusinessBusinessInformation($myBusinessService->getClient());
                $myBusinessVerificationService = ApiHelper::getGoogleMyBusinessVerification($myBusinessService->getClient());
                try {
                    $gmbAccounts = [];

                    // get accounts available
                    $pageToken = null;
                    $count = 0;
                    do {

                        $res = $myBusinessAccountManagementService->accounts->listAccounts(array_filter([
                            'pageSize' => 20,
                            'pageToken' => $pageToken,
                        ]));
                        $pageToken = $res->getNextPageToken();
                        foreach ($res->getAccounts() as $account) {
                            $gmbAccounts[] = $account;
                        }

                        ++$count;

                    } while ($pageToken && $count < 100); // only max 100 pages
                    unset($count, $pageToken);

                    $google_accounts_data = [];

                    foreach($gmbAccounts as $gmbAccount){

                        $role = $gmbAccount->getRole();
                        if($gmbAccount->getType() !== 'PERSONAL' && !in_array($role, ['PRIMARY_OWNER', 'OWNER', 'CO_OWNER', 'MANAGER', 'COMMUNITY_MANAGER',])){
                            // if it's a company/org account and user has un-sufficient permissions
                            continue;
                        }

                        // get locations of this account
                        $accData = [
                            'account_id' => $gmbAccount->getName(),
                            'account_name' => $gmbAccount->getAccountName(),
                            'locations' => [],
                        ];

                        $accountLocationPageToken = null;
                        $count = 0;
                        // $readMask = "storeCode,regularHours,name,languageCode,title,phoneNumbers,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours";
                        do {
                            $res = $myBusinessInformationService->accounts_locations->listAccountsLocations($gmbAccount->getName(), [
                                'pageSize' => 100,
                                'orderBy' => 'title',
                                'pageToken' => $accountLocationPageToken,
                                'readMask' => "name,title,storeCode,phoneNumbers,websiteUri,storefrontAddress"
                            ]);
                            $accountLocationPageToken = $res->getNextPageToken();
                            ++$count;

                            $locationVerificationPageToken = null;
                            try {
                                $locations = $res->getLocations();

                                if(!$locations){
                                    $locations = [];
                                }
                            } catch (\Exception $e) {
                                report($e);
                                return errorPage([
                                    'title' => 'Issue fetching Google Locations',
                                    'message' => 'It seems there is some issue fetching your google locations, please try again and make sure your locations are verified and you have given the required permissions.'
                                ]);
                            }
                            foreach($locations as $location){
                                $res = $myBusinessVerificationService->locations_verifications->listLocationsVerifications($location->getName(),[
                                    'pageSize' => 100,
                                    'pageToken'=> $locationVerificationPageToken
                                ]);
                                $locationVerificationPageToken = $res->getNextPageToken();

                                $verified = false;
                                // foreach($res->getVerifications() as $verification){
                                //     if($verification->getState() !== 'COMPLETED'){
                                //         $verified = false;
                                //         break;
                                //     } else {
                                //         $verified = true;
                                //     }
                                // }
                                //only add location when all verifications are completed;
                                // if($verified){
                                $address = $location->getStorefrontAddress();
                                $addressLines = $address ? (array) $address->getAddressLines() : [];
                                $accData['locations'][] = [
                                    'name' => Str::limit($location->getTitle() . (!empty($addressLines) ? ' - ' . implode(', ', $addressLines) : ''), 100),
                                    'id' => $accData['account_id'] . '/' . $location->getName(), // should be in the form of accounts/{accountId}/locations/{locationId}
                                    '_addressLines' => (array) $address ? $address->getAddressLines() : [],
                                    '_type' => 'location',
                                    '_token' => array_merge(
                                        $token,
                                        [
                                            'user' => $social_user->getEmail(),
                                        ]
                                    ),
                                ];
                                // }
                            }
                        } while ($accountLocationPageToken && $count < 100); // only max 100 pages
                        unset($count, $accountLocationPageToken);
                        $google_accounts_data['gmb'][] = $accData;
                    }

                } catch (\Exception $e) {
                    report($e);
                    return errorPage([
                        'title' => 'Google returned an error',
                        'message' => $e
                    ]);
                }
            }

            if($service === 'youtube'){
                // now, get youtube channels
                $youtubeService = ApiHelper::getYouTube($token);
                $youtubeChannelsData = [];
                try {
                    $pageToken = null;
                    $count = 0;
                    do {
                        $res = $youtubeService->channels->listChannels('id,contentOwnerDetails,status,snippet,statistics', array_filter([
                            'mine' => true,
                            'maxResults' => 50,
                            'pageToken' => $pageToken,
                        ]));
                        $pageToken = $res->getNextPageToken();
                        foreach ( (array) $res->getItems() as $channel ) {

                            $categories = [];
                            $countryCode = $channel->getSnippet()->getCountry();

                            // fetch categories of this channel
                            if($countryCode){
                                $categoriesPageToken = null;
                                do {
                                    $res = $youtubeService->videoCategories->listVideoCategories('snippet', array_filter([
                                        'regionCode' => $countryCode,
                                        'pageToken' => $categoriesPageToken,
                                    ]));
                                    $categoriesPageToken = $res->getNextPageToken();
                                    foreach ( (array) $res->getItems() as $category ) {
                                        if($category->getSnippet()->getAssignable()){
                                            $categories[] = [
                                                'id' => $category->getId(),
                                                'name' => $category->getSnippet()->getTitle(),
                                            ];
                                        }
                                    }
                                } while ($categoriesPageToken);
                            }

                            $youtubeChannelsData[$channel->getId()] = [
                                'name' => $channel->getSnippet()->getTitle(),
                                'id' => $channel->getId(),
                                'image' => $channel->getSnippet()->getThumbnails()->getDefault()->getUrl(),
                                '_type' => 'youtube',
                                '_token' => array_merge(
                                    $token,
                                    [
                                        'user' => $social_user->getEmail(),
                                    ]
                                ),
                                '_extra_data' => [
                                    'categories' => $categories,
                                ],
                            ];
                        }
                        ++$count;
                    } while ($pageToken && $count < 100); // only max 100 pages
                    unset($count, $pageToken);

                    $google_accounts_data['youtube'] = $youtubeChannelsData;

                } catch (\Exception $e) {

                    if(Str::contains($e->getMessage(), 'PERMISSION_DENIED')){
                        // didn't get permission
                        flash('It seems you have not given the required permissions to access your YouTube account. Please ensure that you have granted the required permissions and try again.', 'error');
                        return redirect()->route($redirectRoute);
                    }

                    report($e);
                    return errorPage([
                        'title' => 'Issue fetching YouTube Channels',
                        'message' => 'It seems there is some issue fetching your youtube channels: ' . $e->getMessage()
                    ]);
                }
            }

            // set in session
            session(['accounts.auth_google_accounts' => $google_accounts_data]);

            return redirect()->route($redirectRoute);

        } elseif ($provider === 'mastodon') {
            // get server from acct by splitting using @
            $acctParts = explode('@', $social_user->acct);
            $server = $acctParts[1];
            $this->addToDb([
                'id' => $social_user->getId() . '@' . $server,
                'user_id' => $user->id,
                'name' => $social_user->getName(),
                'token' => [
                    'token' => $social_user->token,
                    'username' => $social_user->getNickname(),
                    'domain' => $social_user->server,
                    'acct' => $social_user->acct, // acct is the full username of a mastodon user e.g. username@domain
                ],
                'type' => $provider . '.profile'
            ]);
            if($isAPIRequest){
                $this->removeConnectRequestData();
            }
            return redirect()->route($redirectRoute);
        } elseif ($provider === 'tiktok'){
            $token = [
                'token' => $social_user->token,
                'expiresIn' => $social_user->expiresIn,
                'expiresAt' => time() + $social_user->expiresIn,
                'refreshToken' => $social_user->refreshToken,
                'timestamp' => time(),
            ];
            $this->addToDb([
                'id' => $social_user->getId(),
                'user_id' => $user->id,
                'name' => $social_user->getName(),
                'token' => $token,
                'type' => $provider . '.profile'
            ]);

            if($isAPIRequest){
                $this->removeConnectRequestData();
            }

            return redirect()->route($redirectRoute);

        }  elseif ($provider === 'reddit'){

            $token = [
                'userId' => $social_user->getId(),
                'username' => $social_user->getNickname(),
                'token' => $social_user->token,
                'expiresIn' => $social_user->expiresIn,
                'expiresAt' => time() + $social_user->expiresIn,
                'refreshToken' => $social_user->refreshToken,
                'timestamp' => time(),
            ];

            $reddit = ApiHelper::getReddit($token);

            // wait
            sleep(5);

            $totalSleepSeconds = 35; // already slept

            $reddit_accounts_data = [];
            // reddit profile as account destination
            $reddit_accounts_data['profile:' . $social_user->getId()] = [
                'name' => 'u/' . $social_user->getNickname(),
                'id' => $social_user->getId(),
                'image' => $social_user->getAvatar(),
                '_type' => 'profile',
                '_token' => array_merge($token, [
                    'subreddit' => 'u_' . $social_user->getNickname(),
                ]),
            ];

            // reddit subreddits as account destination
            foreach(['moderator', 'contributor', 'subscriber', ] as $type){

                if ($totalSleepSeconds >= 60) {
                    // too much sleep already
                    break;
                }

                $pageToken = null;
                do {
                    while(true){ // retry loop
                        try {
                            $res = $reddit->get('/subreddits/mine/' . $type, [
                                'query' => array_filter([
                                    'limit' => 100,
                                    'after' => $pageToken,
                                    'raw_json' => 1,
                                ]),
                            ]);
                        } catch (BadResponseException $e) {
                            // check if there is retry-after header
                            $retryAfter = $e->getResponse()->getHeader('retry-after');
                            if($retryAfter && count($retryAfter) > 0){
                                $retryAfter = (int) $retryAfter[0];
                                \Log::info('reddit subreddits/' . $type . ': retry after ' . $retryAfter);
                                sleep($retryAfter);
                                $totalSleepSeconds += $retryAfter;
                                continue;
                            }
                            // throw $e;
                        }
                        break;
                    }

                    if(!isset($res)){
                        sleep(5);
                        continue;
                    }

                    $resJson = json_decode($res->getBody()->getContents());

                    $pageToken = $resJson->data->after;
                    foreach ($resJson->data->children as $_subreddit) {

                        $subreddit = $_subreddit->data;

                        if(Str::contains($subreddit->display_name, 'u_')){
                            // skip if subreddit is user profile
                            continue;
                        }

                        // skip banned
                        if($subreddit->user_is_banned){
                            continue;
                        }

                        // check subreddit_type
                        if($subreddit->subreddit_type !== 'public'){
                            // only contributor or mod can post
                            if(!$subreddit->user_is_contributor && !$subreddit->user_is_moderator){
                                continue;
                            }
                        }

                        $reddit_accounts_data['subreddit:' . $subreddit->id] = [
                            'name' => $subreddit->display_name_prefixed,
                            'id' => $subreddit->id,
                            'image' => $subreddit->icon_img,
                            '_type' => 'subreddit',
                            '_token' => array_merge($token, [
                                'subreddit' => $subreddit->display_name,
                                'subreddit_id' => $subreddit->id,
                            ]),
                            '_extra_data' => [
                                'subreddit' => (array) $subreddit,
                            ],
                        ];
                    }

                    // add some delay to avoid reddit rate limit
                    sleep(5);
                    $totalSleepSeconds += 5;
                } while ($pageToken);
            }

            // set reddit accounts in session
            session(['accounts.auth_reddit_accounts' => $reddit_accounts_data]);
            return redirect()->route($redirectRoute);
        } elseif ($provider === 'pinterest'){
            $token = [
                'token' => $social_user->token,
                'expiresIn' => $social_user->expiresIn,
                'expiresAt' => time() + $social_user->expiresIn,
                'refreshToken' => $social_user->refreshToken,
                'timestamp' => time(),
            ];

            $pinterestClient = ApiHelper::getPinterest($token);

            // get pinterest boards
            $extraData = [];
            $bookmark = null;
            do {
                try {
                    $res = $pinterestClient->get('v5/boards?page_size=250' . ($bookmark ? '&bookmark=' . $bookmark : ''));
                    $jsonRes = json_decode($res->getBody()->getContents(), true);

                    $bookmark = $jsonRes['bookmark'] ?? null;
                    foreach($jsonRes['items'] as $res){
                        $extraData['boards'][] = [
                            'id' => $res['id'],
                            'name' => $res['name'],
                        ];
                    }

                } catch (\Exception $e) {
                    report($e);
                    return errorPage([
                        'title' => 'Some error while adding Pinterest account.',
                        'message' => $e,
                    ]);
                }
            } while($bookmark);

            $ret = $this->addToDb([
                'id' => $social_user->getId(),
                'user_id' => $user->id,
                'name' => $social_user->user['username'],
                'token' => $token,
                'type' => $provider . '.profile'
            ]);

            if($ret && !empty($extraData)){
                $ret->setOption('extra_data', $extraData);
            }
            
            return redirect()->route($redirectRoute);
        } elseif ($provider === 'threads'){
            $this->addToDb([
                'id' => $social_user->getId(),
                'user_id' => $user->id,
                'name' => $social_user->getName(),
                'token' => [
                    'token' => $social_user->token, // is short-lived token
                ],
                'type' => $provider . '.profile'
            ]);

            return redirect()->route($redirectRoute);
        }

        // shouldn't reach here
        return errorPage([
            'title' => 'Unknown error.',
            'message' => 'Something unexpected happened.',
        ]);
    }

    /**
     * Add the account from HTTP request
     * @param Request $request
     * @param string|null $provider
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     * @throws \Exception
     */
    public function addAccountFromRequest(Request $request, string $provider = null)
    {
        $isAPIRequest = !!self::getConnectRequestData();

        $redirectRoute = $isAPIRequest ? 'accounts.api-connect' : 'accounts.index';

        if ($request->has('cancel')) {

            $this->clearAccountsInSession();

            if($isAPIRequest){
                flash('You have not added any accounts', 'error');
                $this->removeConnectRequestData();
            }

            return redirect()->route($redirectRoute);
        }

        $user_id = $isAPIRequest ? self::getConnectRequestData('user_id') : user()->id;

        if ($provider == 'facebook') {

            $facebook_accounts = session('accounts.auth_fb_accounts');

            if (empty($facebook_accounts)) {
                \Log::info('empty facebook_accounts for user ' . $user_id);
                flash('Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            if ($request->has('account_ids')) {

                foreach($request->input('account_ids', []) as $a_id){

                    $obj = $facebook_accounts[$a_id];

                    if (!empty($obj)) {

                        $ret = $this->addToDb([
                            'id' => $obj['id'],
                            'name' => $obj['name'],
                            'user_id' => $user_id,
                            'type' => $obj['_type'] === 'instagram' ? 'instagram.api' : 'facebook.' . $obj['_type'],
                            'token' => $obj['_token']
                        ]);

                        if(!$ret){
                            break;
                        }

                    } else {
                        flash('$obj is empty - facebook account', 'error');
                        return redirect()->route($redirectRoute);
                    }

                }

                $this->clearAccountsInSession();

                if($isAPIRequest){
                    $this->removeConnectRequestData();
                }

                return redirect()->route($redirectRoute);

            } else {
                flash('account_ids is not set - facebook account', 'error');
                return redirect()->route($redirectRoute);
            }
        } else if($provider === 'linkedin'){

            $linkedin_accounts = session('accounts.auth_linkedin_accounts');

            if (empty($linkedin_accounts)) {
                \Log::info('empty linkedin accounts');
                flash('Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            if ($request->has('account_ids')) {

                foreach($request->input('account_ids', []) as $a_id) {

                    $obj = $linkedin_accounts[$a_id];

                    if (!empty($obj)) {

                        $ret = $this->addToDb([
                            'id' => $obj['id'],
                            'name' => $obj['name'],
                            'user_id' => $user_id,
                            'type' => 'linkedin.' . $obj['_type'],
                            'token' => $obj['_token']
                        ]);

                        if(!$ret){
                            break;
                        }

                    } else {
                        flash('$obj is empty - linkedin account', 'error');
                        return redirect()->route($redirectRoute);
                    }
                }

                $this->clearAccountsInSession();

                if($isAPIRequest){
                    $this->removeConnectRequestData();
                }

                return redirect()->route($redirectRoute);

            } else {
                flash('account_ids is not set - linkedin account', 'error');
                return redirect()->route($redirectRoute);
            }

        } else if ($provider === 'google'){

            $google_accounts = session('accounts.auth_google_accounts');

            if ( empty($google_accounts) ){
                // something is wrong
                \Log::info('empty google accounts');
                $this->clearAccountsInSession();
                flash('Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            if ($request->has('account_ids')) {

                foreach($request->input('account_ids', []) as $a_id) {

                    $obj = array_get($google_accounts, $a_id);

                    if ($obj) {

                        $ret = $this->addToDb([
                            'id' => $obj['id'],
                            'name' => $obj['name'],
                            'user_id' => $user_id,
                            'type' => 'google.' . $obj['_type'],
                            'token' => $obj['_token']
                        ]);

                        if($ret && isset($obj['_extra_data']) && !empty($obj['_extra_data'])){
                            $old = $ret->getOption('extra_data', []);
                            $ret->setOption('extra_data', array_merge($old, $obj['_extra_data']));
                        }

                        if(!$ret){
                            break;
                        }

                    } else {
                        flash('$obj is not set - google account', 'error');
                        return redirect()->route($redirectRoute);
                    }
                }

                $this->clearAccountsInSession();

                if($isAPIRequest){
                    $this->removeConnectRequestData();
                }

                return redirect()->route($redirectRoute);

            } else {
                flash('account_ids is not set - google account', 'error');
                return redirect()->route($redirectRoute);
            }
        } else if ($provider === 'reddit'){

            $reddit_accounts = session('accounts.auth_reddit_accounts', []);

            if ( empty($reddit_accounts) ){
                // something is wrong
                $this->clearAccountsInSession();
                flash('Please try again.', 'error');
                return redirect()->route($redirectRoute);
            }

            if ($request->has('account_ids')) {

                foreach($request->input('account_ids', []) as $a_id) {

                    $obj = Arr::get($reddit_accounts, $a_id);

                    if ($obj) {

                        $ret = $this->addToDb([
                            'id' => $obj['id'],
                            'name' => $obj['name'],
                            'user_id' => $user_id,
                            'type' => 'reddit.' . $obj['_type'],
                            'token' => $obj['_token']
                        ]);

                        if($ret && isset($obj['_extra_data']) && !empty($obj['_extra_data'])){
                            $old = $ret->getOption('extra_data', []);
                            $ret->setOption('extra_data', array_merge($old, $obj['_extra_data']));
                        }

                        if(!$ret){
                            break;
                        }

                    } else {
                        flash('Sent ID not found. Please try again.', 'warning');
                        return redirect()->route($redirectRoute);
                    }
                }

                $this->clearAccountsInSession();

                if($isAPIRequest){
                    $this->removeConnectRequestData();
                }

                return redirect()->route($redirectRoute);

            } else {
                flash('Please try again.', 'warning');
                return redirect()->route($redirectRoute);
            }
        } else if ($provider === 'bluesky'){

            $handle = $request->input('handle');
            $password = $request->input('password');
            $service = $request->input('service');

            // service should be a valid hostname
            if(!filter_var($service, FILTER_VALIDATE_DOMAIN)){
                flash('Invalid service', 'warning');
                return redirect()->route($redirectRoute);
            }

            if(Str::startsWith($handle, '@')){
                $handle = substr($handle, 1);
            }

            // check if this is email
            if(filter_var($handle, FILTER_VALIDATE_EMAIL)){
                flash('Please use your Bluesky handle (example: you.bsky.social) instead of email address.', 'warning');
                return redirect()->route($redirectRoute);
            }

            $client = guzzle_client([
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
            ]);

            try {
                $res = $client->post('https://' . $service . '/xrpc/com.atproto.server.createSession', [
                    'json' => [
                        'identifier' => $handle,
                        'password' => $password,
                    ]
                ]);

                $json = json_decode($res->getBody()->getContents(), true);

                if(!isset($json['accessJwt'])){
                    throw new \Exception('Invalid response from Bluesky');
                }

                // if inactive
                if(isset($json['active']) && !$json['active']){
                    flash('Your account is inactive. Please ensure your Bluesky account is active before connecting your account.', 'warning');
                    return redirect()->route($redirectRoute);
                }

                $token = array_merge($json, [
                    'token' => $json['accessJwt'],
                    'refreshToken' => $json['refreshJwt'],
                    'timestamp' => time(),
                    'service' => $service,
                ]);

                $this->addToDb([
                    'id' => $json['did'],
                    'user_id' => $user_id,
                    'name' => $json['handle'],
                    'token' => $token,
                    'type' => 'bluesky.profile'
                ]);

                $this->clearAccountsInSession();

                if($isAPIRequest){
                    $this->removeConnectRequestData();
                }

                return redirect()->route($redirectRoute);
            } catch (\Exception $e) {
                $err = '';

                if($e instanceof \GuzzleHttp\Exception\RequestException){
                    $res = $e->getResponse();
                    if($res){
                        $json = json_decode($res->getBody()->getContents(), true);
                        if(isset($json['error'])){
                            $err = $json['error'];
                        }
                        if(isset($json['message'])){
                            $err .= ': ' . $json['message'];
                        }

                    }
                }

                if(!$err) {
                    $err = $e->getMessage();
                }

                flash($err, 'warning');

                return redirect()->route($redirectRoute);
            }
        }

        abort(404);
        return response();
    }

    private function clearAccountsInSession(){
        // del from session
        session()->remove(
            'accounts.auth_fb_accounts'
        );
        session()->remove(
            'accounts.auth_linkedin_accounts'
        );
        session()->remove(
            'accounts.auth_google_accounts'
        );
        session()->remove(
            'accounts.auth_reddit_accounts'
        );
    }

    /**
     * Add the account to db
     * @param mixed $data
     * @return Account|null
     * @throws \Exception
     */
    public function addToDb($data)
    {
        if(!!self::getConnectRequestData()){
            $user = User::find(self::getConnectRequestData('user_id'));
        } else {
            $user = user();
        }

        if(!$user){
            return null;
        }

        $isAPIRequest = !!self::getConnectRequestData();

        $upgradeMessage = 'Please <a href="https://socialbu.com/app/settings#billing" target="_blank">upgrade</a> your subscription to add more accounts.';

        if($isAPIRequest){
            $upgradeMessage = 'Upgrade required. Please contact the service provider to add more accounts.';
        }

        // only tw and fb and mastodon allowed in free plan
        if($user->getPlan(true) === 'free' && !in_array($data['type'], ['twitter.profile', 'facebook.page', 'mastodon.profile'])){
            flash($upgradeMessage, 'warning');
            return null;
        }

        $acc = Account::where('user_id', $user->id)->where('account_id', $data['id'])->where('private', false)->first();
        $action_type = 'updated';
        if ($acc === null) {

            if(getUsage('accounts', $user) + 1 > getPlanUsage('accounts', $user)){
                // show error
                flash($upgradeMessage, 'warning');
                return null;
            }

            $action_type = 'added';
            $acc = new Account([
                'account_id' => $data['id'],
                'type' => $data['type'],
                'user_id' => $data['user_id'],
                'name' => Str::limit($data['name'], 140),
            ]);
            $acc->save();

            if(!$user->getOption('account_added', false)){ 
                $user->setOption('account_added', true);
            }
        }

        $acc->setToken($data['token']);

        if(isset($data['via_instagram_login']) && $data['via_instagram_login']){
            $acc->setOption('via_instagram_login', true);
        }else {
            $acc->removeOption('via_instagram_login'); //incase the account was previously added via instagram login and now via Facebook login
        }

        if(isset($data['login_via_new_x_api']) && $data['login_via_new_x_api']) {
            $acc->setOption('login_via_new_x_api', true);
        }

        if (!$acc->active) // if account not active, mark as active
            $acc->setActive(true);

        if( in_array($acc->type, ['facebook.page', 'instagram.api']) ){
            // setup webhook for fb page
            $tries = 0;
            while(true) {

                ++$tries;

                try {
                    $acc->setupWebhook();
                } catch (\Exception $e) {
                    // we previously deleted the account, but we should let the user add it
                    // but, we do need webhooks for our features like inbox, and so on to work

                    if ($tries < 5) {
                        sleep(1);
                        // retry
                        continue;
                    }

                    $acc->setOption('no_webhook', true);
                    // redirect back with error
                    if(Str::contains($e->getMessage(), 'permission')){
                        flash('Unable to setup facebook webhook events. Some features (such as social inbox) will not work unless you allow all permissions and re-add your page(s).', 'warning');
                    } else {
                        flash('Unable to setup facebook webhook events. Some features (such as social inbox) will not work.', 'error');
                        report($e);
                    }
                }

                break;
            }
        }

        // dispatch job setup account: this will fetch any necessary data
        dispatch(new \App\Jobs\SetupAccount($acc, true));

        if(self::getConnectRequestData()){
            $message = trans('accounts.' . $action_type) . ' You can now close this window.';
        } else {
            $message = trans('accounts.' . $action_type);
        }
        flash($message);

        // set session value, so we can also send postMessage to the parent window
        session()->flash('accounts.connect.send_window_message', true);

        // check if return_url is set for api connect process
        $returnUrl = self::getConnectRequestData('return_url');
        // save in session
        if($returnUrl){
            session()->flash('accounts.connect.return_url', $returnUrl); // will be later used in the view to redirect
        }

        //dispatch job to send success msg to callback
        $postbackUrl = self::getConnectRequestData('postback_url');
        if($postbackUrl){
            dispatch(new SendHTTPRequest('POST', $postbackUrl, [
                // guzzle options
                'json' => [
                    'account_action' => $action_type,
                    'account_id' => $acc->id,
                    'account_type' => $acc->type,
                    'account_name' => $acc->name,
                ],
            ]));

            // we also need to save this postback_url in options,
            // so we can later send the postback if the account is disconnected
            $acc->setOption('postback_url', $postbackUrl);
        }

        return $acc;
    }

    /**
     * Test the API connection
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection($id)
    {
        /** @var Account $acc */
        $acc = Account::ofUser()->findOrFail($id);
        return response()->json([
            'success' => $acc->testConnection() == true
        ]);
    }

    /**
     * Display add popup.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function add()
    {
        return redirect()->route('accounts.index')->with('accounts.open_add_account_modal', true);
    }

    /**
     * Display API Connect page
     * @param Request $request
     * @param array $vars
     * @return Application|Factory|View
     */
    public function apiConnect(Request $request, array $vars = [])
    {
        $dataArr = [
            'auth_facebook' => session('accounts.auth_fb_accounts'),
            'auth_linkedin' => session('accounts.auth_linkedin_accounts'),
            'auth_google' => session('accounts.auth_google_accounts'),
            'auth_reddit' => session('accounts.auth_reddit_accounts'),
        ];

        $totalAccounts = 0;
        foreach($dataArr as $data){
            if($data){
                $totalAccounts += count($data);
            }
        }

        $allData = array_merge($dataArr, [
            'open_add_mastodon_modal' => false,
            'accounts_count' => $totalAccounts,
        ]);

        return view('user.accounts.api-connect', array_merge($allData, $vars));
    }

    /**
     * Display a listing of the resource.
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        if ($request->input('show') == 'inactive')
            $all_accounts = Account::ofUser()->where('active', false)->orderBy('name')->paginate();
        else
            $all_accounts = Account::ofUser()->orderBy('name')->paginate();

        return view('user.accounts.index', [
            'accounts' => $all_accounts,
            'show' => ($request->input('show') == 'inactive' ? 'inactive' : 'all'),
            'open_add_account_modal' => (session('accounts.open_add_account_modal') == true),
            'open_add_instagram_modal' => (session('accounts.open_add_instagram_modal')),
            'open_add_bluesky_modal' => (session('accounts.open_add_bluesky_modal') == true),
            'auth_facebook' => session('accounts.auth_fb_accounts'),
            'auth_linkedin' => session('accounts.auth_linkedin_accounts'),
            'auth_google' => session('accounts.auth_google_accounts'),
            'auth_reddit' => session('accounts.auth_reddit_accounts'),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        $acc = Account::ofUser()->findOrFail($id);
        $this->validate($request, [
            'name' => 'required|max:255',
        ]);
        $acc->name = $request->input('name');
        $acc->save();
        return back()->with('message', trans('accounts.updated'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        /** @var Account $acc */
        $acc = Account::ofUser()->findOrFail($id);

        try {
            if (!$acc->delete()) {
                flash('Error. Please try again.', 'error');
                return response()->json([
                    'success' => true
                ]);
            }
        } catch (\Exception $e) {
            report($e);
            flash('Error. Please try again.', 'error');
            return response()->json([
                'success' => true
            ]);
        }

        flash(trans('accounts.deleted'));
        return response()->json([
            'success' => true
        ]);
    }
}
