<?php if(session()->has('message')): ?>
    <div class="alert alert-info alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        <?php echo session('message'); ?>

    </div>
<?php endif; ?>
<?php if(session()->has('status')): ?>
    <div class="alert alert-info alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        <?php echo session('status'); ?>

    </div>
<?php endif; ?>
<?php if(session()->has('warning')): ?>
    <div class="alert alert-warning alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        <?php echo session('warning'); ?>

    </div>
<?php endif; ?>
<?php if(session()->has('error')): ?>
    <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        <?php echo session('error'); ?>

    </div>
<?php endif; ?>
<?php if(session()->has('success')): ?>
    <div class="alert alert-success alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        <?php echo session('success'); ?>

    </div>
<?php endif; ?>
<?php ($pending_usage_events = session()->pull('pending_usage_events', [])); ?>
<?php if(!empty($pending_usage_events)): ?>
    <?php $__env->startPush('footer_html'); ?>
        <!-- record the events if any -->
        <script>
            if(window.__recordUsageEvent){
                <?php $__currentLoopData = $pending_usage_events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    window.__recordUsageEvent("<?php echo e($event['name']); ?>", <?php echo json_encode($event['data']); ?> , false);
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            }
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/partials/flash.blade.php ENDPATH**/ ?>