<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class CaptionExpander implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'caption',
                'label' => 'Short Caption',
                'placeholder' => '',
                'type' => 'text',
                'rules' => 'required|string|max:1000',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'message',
                'label' => 'Key Message',
                'type' => 'text',
                'rules' => 'required|string',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'length',
                'label' => 'Desire length (max words)',
                'type' => 'number',
                'rules' => 'required',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'platform',
                'label' => 'Platform',
                'type' => 'select',
                'options' => [
                    ['value' => 'instagram', 'label' => 'Instagram', 'selected' => false],
                    ['value' => 'snapchat', 'label' => 'Snapchat', 'selected' => false],
                    ['value' => 'tiktok', 'label' => 'TikTok', 'selected' => false],
                    ['value' => 'facebook', 'label' => 'Facebook', 'selected' => false],
                    ['value' => 'linkedin', 'label' => 'LinkedIn', 'selected' => false],
                    ['value' => 'x', 'label' => 'X', 'selected' => false],
                    ['value' => 'threads', 'label' => 'Threads', 'selected' => false],
                    ['value' => 'youtube', 'label' => 'YouTube', 'selected' => false],
                    ['value' => 'reddit', 'label' => 'Reddit', 'selected' => false],
                    ['value' => 'bluesky', 'label' => 'Bluesky', 'selected' => false],
                    ['value' => 'pinterest', 'label' => 'Pinterest', 'selected' => false],
                    ['value' => 'mastodon', 'label' => 'Mastodon', 'selected' => false],
                    ['value' => 'google-business-profile', 'label' => 'Google Business Profile', 'selected' => false],
                ],
                'rules' => 'in:instagram,snapchat,tiktok,facebook,youtube,linkedin,x,threads,reddit,bluesky,pinterest,mastodon,google-business-profile',
                'class' => 'col-md-6',
            ],
        ];
    }


    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'Post',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json', // can be json, form, or multipart
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "You will act as a Caption Expander to help me transform short social media captions into longer, more engaging, and detailed versions, incorporating a clear call to action. I will provide you with a short caption, a desired maximum word count, target social platform and the key message I want to convey. Your output should expand upon the provided caption while maintaining its original essence. The tone of the expanded caption should be inferred from the original short caption. You'll seamlessly integrate the key message; if it cannot be naturally connected, prioritize the essence of the short caption. Ensure the expanded caption is compelling, adheres strictly to the maximum word count, and includes the most fitting call to action based on the expanded text. If emojis or hashtags make sense within the context of the expanded caption, you may include them. Also, do not use em dashes (—) or en dashes (–) in the output text.

                                    short caption: {{form.caption}}
                                    Key Message: {{form.message}}
                                    desired maximum word count should not be greater than: {{form.length}}
                                    target social platfrom: {{form.platform}}{% if form.platform == 'x' %} (twitter){% endif %}

                                    I appreciate when information is presented efficiently without unnecessary jargon."
                                ]))
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Posts:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ])
                ],
            ],

        ];
    }


    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];

    }

    public function outputComponents(): array
    {
        return [];
    }
}
