<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class PostGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'post_description',
                'label' => 'Post Description',
                'placeholder' => 'Briefly describe your post',
                'type' => 'text',
                'rules' => 'required|string|max:60',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'tone',
                'label' => 'Tone',
                'type' => 'select',
                'options' => [
                    ['value' => 'funny', 'label' => 'Funny','selected' => false],
                    ['value' => 'aesthetic', 'label' => 'Aesthetic', 'selected' => false],
                    ['value' => 'cool', 'label' => 'Cool', 'selected' => false],
                    ['value' => 'professional', 'label' => 'Professional', 'selected' => false],
                ],
                'rules' => 'required|in:funny,aesthetic,cool,professional',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'variant_count',
                'label' => 'Variant Count',
                'type' => 'select',
                'options' => [
                    ['value' => '2', 'label' => '2', 'selected' => false],
                    ['value' => '3', 'label' => '3', 'selected' => false],
                    ['value' => '4', 'label' => '4', 'selected' => false],
                    ['value' => '5', 'label' => '5', 'selected' => false],
                ],
                'rules' => 'required|in:2,3,4,5',
                'class' => 'col-md-6',
            ],
           [
               'id' => 'keywords',
               'label' => 'Keywords',
               'placeholder' => 'Enter a main keyword for your post',
               'type' => 'text',
               'rules' => 'required|string|max:60',
                'class' => 'col-md-12',
           ],
        ];
    }


    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'Post',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json', // can be json, form, or multipart
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "Generate a list of {{form.variant_count}} creative and unique posts with a {{form.tone}} tone and with keywords like {{form.keywords}}.",
                                    "The posts should be catchy, and relevant to the category.",
                                    "Additional details: Description: {{form.post_description}}.",
                                    "Do not include any additional text.",
                                    'Separate each post with a "---END_POST---" except the last post.',
                                ]))
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Posts:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ])
                ],
            ],

        ];
    }


    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];

    }

    public function outputComponents(): array
    {
        return [];
    }
}