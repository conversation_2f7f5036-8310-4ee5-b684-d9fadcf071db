<?php

namespace App\Http\Controllers;

use App\Helpers\EmailListHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Swift_Message;

class CommonController extends Controller
{
    public function index(){
        return view('common.index');
    }
    public function about(){
        return view('common.about.index');
    }
    public function privacy(){
        return view('common.about.privacy');
    }
    public function cookies(){
        return view('common.about.cookies');
    }
    public function terms(){
        return view('common.about.tos');
    }
    public function tos(){
        return redirect('/about/terms');
    }
    public function partners(){
        return view('common.about.partners');
    }
    public function features(){
        return redirect('/pricing#features');
    }
    public function publish(){
        return view('common.publish');
    }
    public function curate(){
        return view('common.curate');
    }
    public function assistant(){
        return view('common.assistant');
    }
    public function careers(){
        return redirect('/about');
    }
    public function team(){
        return view('common.about.team');
    }
    public function startups(){
        return view('common.startups');
    }
    public function schedulePosts(){
        return redirect('/publish');
    }
    public function scheduleStories(){
        return view('common.schedule-stories');
    }
    public function scheduleReels(){
        return view('common.schedule-reels');
    }
    public function storySchedule(){
        return redirect('/schedule-stories');
    }
    public function bulkSchedule(){
        return view('common.bulk-scheduling');
    }
    public function calendar(){
        return view('common.social-calendar');
    }
    public function postRecycling(){
        return view('common.post-recycling');
    }
    public function postPreviews(){
        return view('common.post-previews');
    }
    public function customQueues(){
        return redirect('/publish');
    }
    public function generate(){
        return view('common.generate');
    }
    public function generateContent(){
        return redirect('/generate');
    }
    public function respond(){
        return view('common.respond');
    }
    public function monitor(){
        return view('common.monitor');
    }
    public function socialMediaMonitoring(){
        return redirect('/monitor');
    }
    public function automate(){
        return view('common.automate');
    }
    public function postFromRss(){
        return view('common.post-from-rss');
    }
    public function intelligentReplies(){
        return view('common.intelligent-replies');
    }
    public function handleReviews(){
        return view('common.handle-reviews');
    }
    public function webhookIntegrations(){
        return view('common.webhook-integrations');
    }
    public function analyze(){
        return view('common.analyze');
    }
    public function teams(){
        return redirect('/collaborate');
    }
    public function teamCollaboration(){
        return redirect('/collaborate');
    }
    public function collaborate(){
        return view('common.collaborate');
    }
    public function network($network = null){
        if($network === 'google-my-business'){
            return redirect('/google-business-profile');
        }
        return view('common.networks.' . $network);
    }
    public function pricing(){
        return view('common.pricing');
    }
    public function askSocialBuAnything(){
        return view('common.ask-socialbu-anything');
    }
    public function mobile(){
        return view('common.mobile');
    }
    public function browser(){
        return view('common.browser');
    }
    public function wallOfLove(){
        return view('common.wall-of-love');
    }

    public function demo(){
        return view('common.demo');
    }
    public function api(){
        return view('common.api');
    }

    public function apiDocs()
    {
        return view('common.developers.docs');
    }

    public function christmasPromo(Request $request)
    {
        $day = (int) date('d');
        $month = (int) date('m');
        $year = date('Y');
    
        // Show the page if the date is between December 20th and January 15th
        if (($month === 12 && $day >= 20) || ($month === 1 && $day <= 15)) {
            // Show page
            return view('common.promos.christmas');
        } else {
            abort(400);
        }
    }
    
    public function blackFridayPromo(Request $request){
        // show page
        return view('common.promos.black-friday');
    }

    public function saveDemoForm(Request $request)
    {
        try {
            $demoFormData =  $request->validate([
                'email' => 'required|email',
                'first_name' => 'nullable|string|max:255',
                'last_name' => 'nullable|string|max:255',
                'where_do_you_work' => 'nullable|string|max:2000',
                'what_do_you_do' => 'nullable|string|max:2000',
                'how_can_we_help' => 'nullable|string|max:2000',
            ]);
            EmailListHelper::getInstance()->saveDemoForm($demoFormData);
        } catch (\Exception $exception){
            abort(400, $exception->getMessage());
        }
    }

    public function askSocialBuAnythingForm(Request $request)
    {
        try {
            $message = 'Someone asked this question:'. "\n" .$request->input('message');
            $channel = 'product-marketing';

            $request->validate([
                'message' => 'required|string|max:500', 
            ]);

            notify_chat($message, $channel);

            return response()->json([
                'status' => 'success',
                'message' => 'Message sent successfully!'
            ]);
            
        } catch (\Exception $exception){
            abort(400, $exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Illuminate\Validation\ValidationException
     */
    public function affiliates(Request $request){
        $data = [];
        if($request->isMethod('POST')){
            $this->validate($request, [
                'name' => 'required|string',
                'email' => 'required|email',
                'message' => 'required|string',
            ]);

            // send email
            Mail::send([], [], function ($message) use($request) {
                /** @var Swift_Message $message */
                $message->to('<EMAIL>')
                    ->subject('Affiliate Sign Up')
                    ->setBody($request->input('message'))
                    ->setReplyTo($request->input('email'), $request->input('name'))
                    ->setFrom('<EMAIL>');
            });

            $data['received'] = true;
        }
        return view('common.affiliates', $data);
    }
}
