<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::group(['prefix' => '/v1'], function(){ // unprotected routes
    Route::post('/auth/get_token', 'Auth\LoginController@getToken');
    Route::post('/generate/forms/{form}', 'Api\GeneratedContentController@submitGenerateForm');
});
Route::group(['prefix' => '/v1', 'middleware' => 'auth:api',], function(){

    // for pinging and checking if api is reachable and auth is valid
    Route::get('/ping', 'Api\CommonController@ping');

    // for logging out from app
    Route::get('/logout', 'Api\CommonController@logout');

    // for api 
    Route::post('/auth/logout', 'Api\CommonController@logout');
    
    // current user
    Route::get('/user', 'Api\CommonController@user');

    // accounts
    Route::get('/accounts', 'Json\JsonCollectionController@accounts');
    Route::get('/accounts/{id}', 'Api\AccountsController@getAccount');
    Route::patch('/accounts/{id}', 'Api\AccountsController@update');
    Route::delete('/accounts/{id}', 'Api\AccountsController@destroy');
    // connect account
    Route::post('/accounts', 'Api\AccountsController@getAuthUrl');

    // teams
    Route::get('/teams', 'Json\JsonCollectionController@teams');
    Route::get('/teams/{id}', 'Api\TeamsController@getTeam');
    Route::post('/teams', 'Api\TeamsController@store');
    Route::put('/teams/{id}', 'Api\TeamsController@update');
    Route::delete('/teams/{id}', 'Api\TeamsController@destroy');
    
    // posts
    Route::post('/posts', 'Api\PostsController@post');
    Route::get('/posts', 'Api\PostsController@get');
    Route::get('/posts/{id}', 'Api\PostsController@getPost');
    Route::patch('/posts/{id}', 'Api\PostsController@patch');
    Route::post('/posts/{id}', 'Api\PostsController@patch');
    Route::post('/posts/{id}/publish', 'Api\PostsController@publish');

    Route::delete('/posts/{id}', 'Api\PostsController@destroy');
    Route::post('/posts/bulk_delete', 'Api\PostsController@bulkDelete');

    Route::post('/files/temporary/create', 'Api\PostsController@createTemporaryFile');
    Route::post('/files/temporary/delete', 'Api\PostsController@destroyTemporaryFile');

    // upload media
    Route::post('/upload_media', 'Api\PostsController@createTemporaryFile');
    Route::get('/upload_media/status', 'Api\PostsController@checkFileStatus');
    // calendar items
    Route::get('/calendar/posts', 'Api\CalendarController@posts');

    // generate content
    Route::get('/generated_content', 'Api\GeneratedContentController@index');
    Route::post('/generated_content', 'Api\GeneratedContentController@create')->middleware('throttle:20,1');
    Route::post('/generated_content/generate_by_post', 'Api\GeneratedContentController@createFromPost')->middleware('throttle:20,1');
    Route::post('/generated_content/autocomplete_post', 'Api\GeneratedContentController@autocompletePost')->middleware('throttle:20,1');
    Route::get('/generated_content/suggested_topics_by_account', 'Api\GeneratedContentController@suggestedTopicsByAccount');
    Route::get('/generated_content/suggested_topics_by_search', 'Api\GeneratedContentController@suggestedTopicsBySearch');
    Route::get('/generated_content/suggested_topics_by_history', 'Api\GeneratedContentController@suggestedTopicsByHistory');
    Route::patch('/generated_content/{id}', 'Api\GeneratedContentController@update')->middleware('throttle:20,5');
    Route::post('/generated_content/generate_curated_item_post', 'Api\GeneratedContentController@generatePostForCurationItem')->middleware('throttle:20,1');

    // misc
    Route::get('/search/{type?}', 'Json\JsonSearchController@serve');

    // notifications
    Route::get('/notifications', [\App\Http\Controllers\Api\NotificationsController::class, 'index']);
    Route::get('/notifications/unread', [\App\Http\Controllers\Api\NotificationsController::class, 'unreadIndex']);
    Route::get('/notifications/{id}', [\App\Http\Controllers\Api\NotificationsController::class, 'get']);
    Route::post('/notifications/{id}/mark_read', [\App\Http\Controllers\Api\NotificationsController::class, 'markRead']);
    Route::post('/notifications/{id}/mark_unread', [\App\Http\Controllers\Api\NotificationsController::class, 'markUnread']);
    Route::post('/notifications/mark_all_read', [\App\Http\Controllers\Api\NotificationsController::class, 'markAllRead']);

    // social inbox
    Route::group(['prefix' => 'inbox'], function(){
        Route::get('/', 'Api\InboxController@getConversations');

        Route::get('/{convoId}', 'Api\InboxController@getConversationItems');
        Route::delete('/{convoId}', 'Api\InboxController@deleteConversation');

        Route::delete('/{convoId}/items/{itemId}', 'Api\InboxController@deleteConversationItem');
        Route::get('/{convoId}/items/{itemId}/replies', 'Api\InboxController@getConversationItemReplies');
        Route::post('/{convoId}/items/{itemId}/replies', 'Api\InboxController@reply');
        Route::post('/{convoId}/items/{itemId}/reactions', 'Api\InboxController@like');


        // bulk actions for conversations
        Route::patch('update_tags', 'Api\TagController@updateConversationTags');
        Route::patch('update_status', 'Api\InboxController@updateConversationStatus');
        Route::post('assign_conversation', 'Api\InboxController@assignConversation');

        // account settings/config
        Route::get('settings/{accId}', 'Api\InboxController@getAccountInboxConfig');

        // inbox tags
        Route::get('tags', 'Api\TagController@getTags');
        Route::post('tags', 'Api\TagController@store');
        Route::delete('tags/{id}', 'Api\TagController@destroy'); 
    });

     //social contacts
     Route::get('social-contacts', 'Api\SocialContactController@getSocialContacts');
     Route::patch('social-contacts/status', 'Api\SocialContactController@updateSocialContactStatus');  // bulk action
     Route::delete('social-contacts', 'Api\SocialContactController@deleteSocialContact');

    //reports 
    Route::group(['prefix' => 'insights'], function (){

        // content
        Route::get('/posts/counts', 'Api\AnalyzeController@postCounts');
        Route::get('/posts/metrics', 'Api\AnalyzeController@postMetrics');
        Route::get('/posts/top_posts', 'Api\AnalyzeController@topPosts');
        Route::get('/posts/performance_by_hashtags', 'Api\AnalyzeController@performanceByHashtags');

        // account
        Route::get('/accounts/metrics', 'Api\AnalyzeController@accountMetrics');

        // generic
        Route::get('/stats', 'Api\AnalyzeController@stats');

        // team
        Route::get('/teams/metrics', 'Api\AnalyzeController@teamMetricsByUser');

    });

    Route::group(['prefix' => 'chat_bu'], function(){
        Route::get('/messages', 'Api\ChatBuController@messages');
        Route::post('/messages', 'Api\ChatBuController@sendMessage');
        Route::delete('/messages', 'Api\ChatBuController@deleteMessages');
    });

    Route::group(['prefix' => 'curation'], function(){
        Route::get('/topics', [\App\Http\Controllers\Api\CurationController::class, 'getTopics']);
        Route::get('/items', [\App\Http\Controllers\Api\CurationController::class, 'getItems']);
        Route::get('/items/{id}', [\App\Http\Controllers\Api\CurationController::class, 'getItem']);
        Route::get('/feeds', [\App\Http\Controllers\Api\CurationController::class, 'getFeeds']);
        Route::post('/feeds', [\App\Http\Controllers\Api\CurationController::class, 'addFeed']);
        Route::get('/feeds/{id}', [\App\Http\Controllers\Api\CurationController::class, 'getFeed']);
    });

    // used for dashboard
    Route::get('/recent_blogs', 'Api\CommonController@recentBlogs');

});
// client routes
Route::group(['prefix' => '/v1/partners', 'middleware' => 'client', ], function(){
    // Route::get('/', 'Api\PartnersController@index');
    // Route::get('/ping', 'Api\PartnersController@ping');
    Route::post('/content_by_topic', 'Api\Partners\AiContentController@generateContentByTopic')->middleware('throttle:10,2');
    Route::post('/autocomplete', 'Api\Partners\AiContentController@autocomplete')->middleware('throttle:10,2');
    Route::post('/text2img_prompt', 'Api\Partners\AiContentController@generateImagePrompt')->middleware('throttle:10,2');
});
