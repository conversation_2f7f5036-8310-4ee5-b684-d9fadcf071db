<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\Http\Controllers\Api\PostsController;
use App\PublishQueue;
use App\PublishQueueItem;
use App\Team;
use App\User;
use Carbon\Carbon;
use Mimey\MimeTypes;
use Illuminate\Http\UploadedFile;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class QueueController extends Controller
{
    public function index(){
        $queues = PublishQueue::available();
        return view('user.publish.queues.index', [
            'queues' => $queues,
            'user' => \Auth::user(),
        ]);
    }

    public function updateOrder(Request $request, $id){
        $items = $request->input('itemsByOrder', []);
        $queue = PublishQueue::available()->firstWhere('id', $id);
        foreach($items as $item){
            $itm = $queue->items()->find($item['id']);
            $itm->order = $item['order'];
            $itm->save();
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @throws ValidationException
     */
    public function addQueue(Request $request){
        $this->validate($request, [
            'name' => 'string|required|max:250|min:2',
            'team_id' => 'nullable|integer|exists:teams,id'
        ]);

        $user = user();

        /** @var User $_user */
        $_user = $user;

        $team = null;
        if($request->input('team_id')){
            // check the limit for the team admin
            /** @var Team $team */
            $team = $user->joinedTeams()->findOrFail($request->input('team_id'));

            if(!$team->hasPermission($user, 'queues.create')){
                flash('You do not have permission to create a queue for this team', 'error');
                return back();
            }

            $_user = getResourceOwner($team);
        }

        if(getUsage('queues', $team) + 1 > getPlanUsage('queues', $team)){
            // show error
            if($user->id !== $_user->id){
                flash('Please upgrade the account associated with the team for adding a queue.', 'warning');
            } else {
                flash('Please upgrade your account for adding a queue.', 'warning');
            }
            // redirect to settings
            return redirect()->route('settings', ['#billing']);
        }

        $queue = new PublishQueue([
            'name' => $request->input('name'),
            'team_id' => $request->input('team_id', null),
            'user_id' => $user->id,
        ]);

        $queue->save();

        return redirect('/app/publish/queues/' . $queue->id);

    }

    public function show($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }
        return view('user.publish.queues.queue', [
            'queue' => PublishQueue::transform($queue),
            'timezone' => timezone($queue->user),
        ]);
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Support\Collection|\Tightenco\Collect\Support\Collection
     * @throws \Illuminate\Validation\ValidationException|\Exception
     */
    public function update($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        /** @var User $user */
        $user = \Auth::user();

        $this->validate($request, [
            'name' => 'required|string|max:250|min:2',
            'active' => 'required|boolean',
            'times_to_publish' => 'nullable|integer|max:500|min:1',
            'disable_on_failure' => 'boolean',

            'started_at' => 'nullable|date|date_format:Y-m-d',
            'ended_at' => 'nullable|date|date_format:Y-m-d',

            'options' => 'required|array',

            'options.publish_to' => 'array',
            'options.publish_to.*' => 'required|integer|exists:accounts,id', // make sure it is accessible

            'options.publish_schedule' => 'array|min:1',
            'options.publish_schedule.*' => 'array',
            'options.publish_schedule.*.publish_months' => 'array',
            'options.publish_schedule.*.publish_every' => 'required|string|in:month,week',
            'options.publish_schedule.*.publish_on' => 'required|array', // should be validated later
            'options.publish_schedule.*.publish_at' => 'required|string|date_format:H:i',
        ]);

        $options = $request->input('options');

        // further validations
        foreach($options['publish_schedule'] as &$schedule){
            $every = $schedule['publish_every'];
            if($every === 'week'){
                // validate week days
                Validator::make($schedule, [
                    'publish_on.*' => 'required|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
                ])->validate();
                $schedule['publish_months'] = [];
            } else if($every === 'month'){
                Validator::make($schedule, [
                    'publish_on.*' => 'required|integer|min:1|max:31',
                    'publish_months.*' => 'required|in:January,February,March,April,May,June,July,August,September,October,November,December',
                ])->validate();
            }
        }

        if($queue->team_id > 0){
            /** @var Team $team */
            $team = Team::find($queue->team_id);

            if(!$team->hasPermission($user, 'queues.edit')){
                abort(400, 'You do no have permission to edit this queue');
            }

            /** @var Collection|Account[] $accounts */
            $accounts = $team->accounts;

        } else {
            $accounts = $user->accounts;
        }

        $accN = 0;
        foreach ($options['publish_to'] as $accId){
            if(!$accounts->firstWhere('id', $accId)){
                abort(400, 'Social media account not available with ID: ' . $accId);
            }
            ++$accN;
        }

        if($accN === 0){
            abort(400, 'No valid account to publish on');
        }

        // if we are here, all looks good
        $queue->active = (boolean) $request->input('active');

        if($queue->active && getUsage('queues', $queue) > getPlanUsage('queues', $queue)){
            // show error
            abort(400, 'Please upgrade your account for performing this action.');
        }

        $queue->name = $request->input('name');
        $queue->times_to_publish = with($request->input('times_to_publish'), function($n){
            $n = (int) $n;
            if(!$n || $n == "" || $n == 0) return null;
            return $n;
        });

        if($request->input('started_at') && $request->input('ended_at')){
            $queue->started_at = Carbon::createFromFormat('Y-m-d', $request->input('started_at'), timezone())->timezone('UTC');
            $queue->ended_at = Carbon::createFromFormat('Y-m-d', $request->input('ended_at'), timezone())->timezone('UTC');

            if($queue->ended_at < $queue->started_at){
                abort(400, 'The start date must be before the end date');
            }

            if($queue->ended_at == $queue->started_at){
                abort(400, 'The start date must be before the end date');
            }

        } else {
            $queue->started_at = null;
            $queue->ended_at = null;
        }

        $queue->save();

        $queue->setOption('publish_to', $options['publish_to']);
        $queue->setOption('publish_schedule', $options['publish_schedule']);
        $queue->setOption('disable_on_failure', $request->input('disable_on_failure', false));

        $nextPublishTime = $queue->findNextPublishTime(true);

        return collect([
            'nextItemId' => $queue->findNextQueueItemId(),
            'nextPublishTime' => $queue->active && $nextPublishTime ? (string) $nextPublishTime : null,
        ]);

    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function updateStatus($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        /** @var User $user */
        $user = \Auth::user();

        if($queue->team){
            // check the limit for the team admin
            /** @var Team $team */
            $team = $queue->team;

            if(!$team->hasPermission($user, 'queues.edit')){
                abort(400, 'You do no have permission to edit this queue');
            }
        }

        if(!$queue->active && getUsage('queues', $queue) > getPlanUsage('queues', $queue)){
            $_user = getResourceOwner($queue);
            // show error
            if($user->id !== $_user->id){
                abort(400, 'Please upgrade the account associated with the team for performing this action.');
            } else {
                abort(400, 'Please upgrade your account for performing this action.');
            }
        }

        $queue->toggleStatus();
        return response()->noContent();
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function destroy($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        $team = $queue->team;

        if($team && !$team->hasPermission(user(), 'queues.edit')){
            abort(400, 'You do no have permission to edit this queue');
        }

        $queue->delete();
        return response()->noContent();
    }

    public function getPosts($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        $paginatedData = $queue->items()->paginate(20);

        $nextPublishTime = $queue->findNextPublishTime();
        return collect([
            'items' => with($paginatedData->items(), function($itms) use($paginatedData, $queue){
                $items = [];
                foreach($itms as $index => $itm){
                    if (isset($itm->options['attachments'])) {
                        $_attachments = [];
                        foreach ((array)$itm->options['attachments'] as $_attachment) {
                            if(!$_attachment['path']) continue;
                            $_attachment['url'] = app()->environment('production') ? \Storage::temporaryUrl($_attachment['path'], now()->addDays(1)) : \Storage::url($_attachment['path']);
                            $_attachments[] = $_attachment;
                        }

                        $all_options = $itm->options;

                        //if options has thumbnail, we need to genereate the url
                        if(isset($all_options['thumbnail']) && isset($all_options['thumbnail']['key'])){
                            $all_options['thumbnail']['url'] = \Storage::cloud()->temporaryUrl($all_options['thumbnail']['key'], now()->addDays(2));
                        }

                        //when editing, post editor needs to know the options for each account
                        $options = [];
                        $accounts = $queue->accounts()->map(function($acc){ return $acc->id; })->toArray();
                        foreach($accounts as $accId){
                            $options[$accId] = isset($all_options[$accId]) ? $all_options[$accId] : $all_options;
                        }

                        $options['attachments'] = $_attachments;
                        $itm->options  = $options;
                    }

                        // calculate order based on position in collection and the current page
                    $currentOrder = ($index + 1) + (($paginatedData->currentPage() - 1) * $paginatedData->perPage());

                    $itm->order = $currentOrder;

                    $items[] = $itm;
                }
                return $items;
            }),
            'currentPage' => $paginatedData->currentPage(),
            'lastPage' => $paginatedData->lastPage(),
            'nextPage' => $paginatedData->hasMorePages() ? $paginatedData->currentPage() + 1 : null,

            'nextItemId' => $queue->findNextQueueItemId(),
            'nextPublishTime' => $queue->active && $nextPublishTime ? (string) $nextPublishTime : null,
        ]);
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response|void
     * @throws FileNotFoundException
     */
    public function addPost($id, Request $request){
        return (new \App\Http\Controllers\Api\QueueController())->addPost($id, $request);
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function bulkDelete($id, Request $request){
        $posts = $request->input('post_ids');
        $queue = PublishQueue::available()->firstWhere('id', $id);

        if(!$queue){
            abort(404);
        }

        foreach ($posts as $post){
            try {
                $item = $queue->items()->findOrFail($post); // delete by deleting model, so our listener can catch the event
                $item->delete(); // will auto delete attachments by observer

            } catch (\Exception $e) {

            }
        }
        return response()->noContent();
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function shuffleOrder($id, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        $queue->shuffle();

        return response()->noContent();
    }

    /**
     * @param $id
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function cleanupPosts($id, Request $request){
        // delete items that have times_published >= times_to_publish
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);

        if(!$queue){
            abort(404);
        }

        if($queue->times_to_publish > 0){
            $queue->items()
                ->where('times_published', '>=', $queue->times_to_publish)
                ->delete();
        }

        return response()->noContent();
    }

    /**
     * @throws ValidationException
     * @throws FileNotFoundException
     */
    public function updatePost($id, $postId, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);
        if(!$queue){
            abort(404);
        }

        /** @var PublishQueueItem $item */
        $item = $queue->items()->findOrFail($postId);
        /** @var User $user */
        $user = \Auth::user();

        /** @var Account[]|Collection $accounts */
        $accounts = $user->getAvailableAccounts()->whereIn('id', $request->input('accounts'));
        
        $all_options = $request->input('options', []);

        // validate for each account
        foreach ($accounts as $account){
            $options = isset($all_options[$account->id]) ? $all_options[$account->id] : $all_options;
            try {
                $account->publishPost([
                    'content' => $request->input('content'),
                    'attachments' => PostsController::getAttachmentsFromRequest($request), // for validation, we need attachments as uploadedfile[] objects for validation
                    'options' => $options,
                ], now(), true);
            } catch (\Exception $e) {
                if($e instanceof ValidationException){
                    throw $e;
                }
                abort(400, $e->getMessage());
            }
        }

        $content = $request->input('content');
        $item->content = $content;

        $allCurrentAttachments = [];
        if($request->has('existing_attachments')) {
            $attachmentPathsBeforeRequest = collect($item->getOption('attachments'))->map(function ($f) {
                return $f['path'];
            })->toArray();

            $attachments = PostsController::getAttachmentsFromRequest($request, $attachmentPathsBeforeRequest);

            // now add existing attachments that are missing from $attachments (because those are not needed to be uploaded file objects)
            $existingCurrentAttachments = collect($request->input('existing_attachments', []))->map(function ($json) {
                if(is_string($json)){
                    $json = json_decode($json, true);
                }
                return $json;
            })->filter(function ($att) use ($attachmentPathsBeforeRequest) {
                return isset($att['path']) && in_array($att['path'], $attachmentPathsBeforeRequest);
            })->toArray();

            $allCurrentAttachments = $attachments;
            foreach ($existingCurrentAttachments as $index => $existingCurrentAttachment) {
                // add existing attachment preserving the order
                $allCurrentAttachments[$index] = $existingCurrentAttachment;
            }
        }

        $mimes = new MimeTypes;

        $attachmentsData = collect($allCurrentAttachments)->map(function ($file) use (&$mimes) {
            if ($file instanceof UploadedFile) {
                $path = \Storage::putFile('attachments', $file);
                $attMime = $file->getMimeType();
                $attType =  strtolower($mimes->getExtension($attMime));
                $att = [
                    'path' => $path,
                    'type' => $attType,
                    'name' => $file->getClientOriginalName(),
                    'mimeType' => $attMime
                ];
            } else {
                $att = $file; // existing file; already an array
            }
            return $att;
        })->toArray();

        $all_options['attachments'] = $attachmentsData;

        $item->options = $all_options;

        $item->update();
        return response()->noContent();
    }

    /**
     * @throws \Exception
     */
    public function deletePost($id, $postId, Request $request){
        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available()->firstWhere('id', $id);

        if(!$queue){
            abort(404);
        }

        if($queue->team){
            if(!$queue->team->hasPermission(user(), 'queues.add_post')){
                abort(400, 'You do not have permission to add or remove content for this queue');
            }
        }

        $item = $queue->items()->findOrFail($postId);

        $item->delete(); // will auto delete attachments by observer

        return response()->noContent();
    }
}
