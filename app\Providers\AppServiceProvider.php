<?php

namespace App\Providers;

use App\Account;
use App\Automation;
use App\Feed;
use App\InboxConversation;
use App\InboxConversationItem;
use App\Observers\AccountObserver;
use App\Observers\AutomationObserver;
use App\Observers\FeedObserver;
use App\Observers\InboxConversationItemObserver;
use App\Observers\InboxConversationObserver;
use App\Observers\PostObserver;
use App\Observers\PublishQueueItemObserver;
use App\Observers\PublishQueueObserver;
use App\Observers\TeamObserver;
use App\Observers\UserObserver;
use App\Post;
use App\PublishQueue;
use App\PublishQueueItem;
use App\Socialite\LinkedInProvider;
use App\Socialite\LinkMngrProvider;
use App\Socialite\MastodonProvider;
use App\Socialite\RedditProvider;
use App\Socialite\ThreadsProvider;
use App\Socialite\TikTokProvider;
use App\Socialite\PinterestProvider;
use App\Socialite\InstagramProvider;
use App\Socialite\XProvider;
use App\Team;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use App\User;
use Facebook;
use Laravel\Cashier\Cashier;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function boot()
    {
        if(app()->environment() === 'production') {
            // dont show deprecation notices
            error_reporting(E_ALL & ~E_USER_DEPRECATED);
        }

        // user observer
        User::observe(UserObserver::class);

        // account observer
        Account::observe(AccountObserver::class);

        // post observer
        Post::observe(PostObserver::class);

        // team observer
        Team::observe(TeamObserver::class);

        // feed observer
        Feed::observe(FeedObserver::class);

        // automation observer
        Automation::observe(AutomationObserver::class);

        // queues observer
        PublishQueue::observe(PublishQueueObserver::class);
        PublishQueueItem::observe(PublishQueueItemObserver::class);

        // inbox observers
        InboxConversation::observe(InboxConversationObserver::class);
        InboxConversationItem::observe(InboxConversationItemObserver::class);

        // facebook (not used anymore)
        $this->app->bind('Facebook', function () {
            return new Facebook\Facebook([
                'app_id' => config('services.facebook.client_id'),
                'app_secret' => config('services.facebook.client_secret'),
                'default_graph_version' => config('services.facebook.default_graph_version'),
            ]);
        });

        Schema::defaultStringLength(191);


        // user onboarding steps
        \Onboard::addStep('Verify your email')
            ->link('/app/settings')
            ->cta('Verify Email')
            ->completeIf(function (User $user) {
                return $user->verified;
            });

        \Onboard::addStep('Choose your timezone')
            ->link('/app/settings')
            ->cta('Set Timezone')
            ->completeIf(function (User $user) {
                return $user->getOption('timezone') !== null;
            });

        \Onboard::addStep('Add your phone number')
            ->link('/app/settings')
            ->cta('Add phone')
            ->completeIf(function (User $user) {
                if($user->getOption('onboarding.hide')){
                    return true;
                }
                return $user->getPhone();
            });

        \Onboard::addStep('Add your social media accounts')
            ->link('/app/accounts')
            ->cta('Add Account')
            ->completeIf(function (User $user) {
                $count = $user->accountsFromTeams(true)->count() + Account::ofUser($user->id)->count();
                return $count > 0;
            });

        \Onboard::addStep('Start your free trial')
            ->link('/app/settings#billing')
            ->cta('Choose A Plan')
            ->completeIf(function (User $user) {
                if($user->getPlan(true) !== 'free'){
                    // if on a plan, that means no need to ask them to start trial
                    return true;
                }
                if($user->accountsFromTeams(true)->count() > 0){
                    // is part of a team, so no need
                    return true;
                }
                return $user->hasPaymentMethod();
            });

        // set up socialite for linkmngr
        $this->bootLinkMngrSocialite();

        // set up socialite for mastodon
        $this->bootMastodonSocialite();

        // set up socialite for tiktok
        $this->bootTikTokSocialite();

        // set up socialite for pinterest
        $this->bootPinterestSocialite();
        // setup socialite for linkedin (we use our extended provider)
        $this->bootLinkedInSocialite();
        // setup socialite for threads
        $this->bootThreadsSocialite();
        
        // setup socialite for instagram
        $this->bootInstagramSocialite();

        // setup socialite for twitter
        $this->bootTwitterSocialite();

        // setup socialite for reddit
        $this->bootRedditSocialite();
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment() !== 'production') {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
        }

        // disable cashier built-in migration (we use our own)
        Cashier::ignoreMigrations();

        // keep past due subscriptions active so the user can still access the subscription features until it is cancelled
        Cashier::keepPastDueSubscriptionsActive();
    }

    /**
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    private function bootLinkMngrSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'linkmngr',
            function ($app) use ($socialite) {
                $config = $app['config']['services.linkmngr'];
                return $socialite->buildProvider(LinkMngrProvider::class, $config);
            }
        );
    }

    private function bootMastodonSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'mastodon',
            function ($app) use ($socialite) {
                $config = $app['config']['services.mastodon'];
                return $socialite->buildProvider(MastodonProvider::class, $config);
            }
        );
    }

    private function bootTikTokSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'tiktok',
            function ($app) use ($socialite) {
                $config = $app['config']['services.tiktok'];
                return $socialite->buildProvider(TikTokProvider::class, $config);
            }
        );
    }
    private function bootPinterestSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'pinterest',
            function ($app) use ($socialite) {
                $config = $app['config']['services.pinterest'];
                return $socialite->buildProvider(PinterestProvider::class, $config);
            }
        );
    }
    private function bootInstagramSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'instagram',
            function ($app) use ($socialite) {
                $config = $app['config']['services.instagram'];
                return $socialite->buildProvider(InstagramProvider::class, $config);
            }
        );
    }
    private function bootLinkedInSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'linkedin',
            function ($app) use ($socialite) {
                $config = $app['config']['services.linkedin'];
                return $socialite->buildProvider(LinkedInProvider::class, $config);
            }
        );
    }

    private function bootThreadsSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'threads',
            function ($app) use ($socialite) {
                $config = $app['config']['services.threads'];
                return $socialite->buildProvider(ThreadsProvider::class, $config);
            }
        );
    }

    private function bootTwitterSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'twitter',
            function ($app) use ($socialite) {
                $config = $app['config']['services.twitter'];
                return $socialite->buildProvider(XProvider::class, $config);
            }
        );
    }

    private function bootRedditSocialite()
    {
        $socialite = $this->app->make('Laravel\Socialite\Contracts\Factory');
        $socialite->extend(
            'reddit',
            function ($app) use ($socialite) {
                $config = $app['config']['services.reddit'];
                return $socialite->buildProvider(RedditProvider::class, $config);
            }
        );
    }

}
