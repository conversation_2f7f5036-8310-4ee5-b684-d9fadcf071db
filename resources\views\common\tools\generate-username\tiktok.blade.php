@extends('layout.default')

@php
    $title = 'Free TikTok Username Generator';
    $description = 'Use our Free TikTok Username Generator to easily create unique TikTok handles. Stand out on TikTok with a personalized username.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/generate-username/tiktok';

    $form = new \App\Generate\DynamicForm\Form('username_generator');
    $fields = $form->getFields();

    $fields = array_filter($fields, function ($field) {
        return $field['id'] !== 'network';
    });
@endphp

@php($title = 'Free TikTok Username Generator')
@php($description = 'Use our Free TikTok Username Generator to easily create unique TikTok handles. Stand out on TikTok with a personalized username.')
@php($image = 'https://socialbu.com/images/site/robot_working_on_paper.png')
@php($url = 'https://socialbu.com/tools/generate-username/tiktok')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />

@endpush
@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Usernames',
                    type: 'username_generator',
                    splitDataBy:',',
                    inputData:{
                        network: 'tiktok'
                    },
                });
            });
        });
    </script>
@endpush
@section('content')
    <main class="main-content">
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12 text-center mb-5">
                        <h2 class="display-4 mb-3 pb-0">
                            Free TikTok Username Ideas Generator
                        </h2>
                        <p class="mb-6">
                            Unique TikTok Username Ideas in Seconds
                        </p>
                    </div>
                    <div class="col-md-8 mx-auto w-100 mh-350">
                        <div id="generate-tool-output">
                            <div class="text-center">
                                <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>    
            <div class="container-fluid d-md-block d-none p-0 pt-7">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username.webp" alt="Generate username">
            </div>
            <div class="container-fluid d-md-none d-block p-0 pt-6">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username-mobile.webp" alt="Generate username">
            </div>
        </section>

        <section class="section rounded-xl p-xxl-5 px-20">
            <div class="container d-flex flex-column align-items-center text-center bg-primary text-white rounded-xl rounded-4 p-6 p-md-5 p-lg-8">
                <h2 class="display-2 fw-bold mb-6">Got Your perfect TikTok <br> username, why stop there?</h2>
                <p class="lead-2 mb-6">Run your whole Social Media with Automations.</p>
                <a href="#" class="lead-2 btn bg-white btn-light text-primary d-flex align-items-center gap-2 px-4 py-2 ">
                    Link your accounts →
                </a>
            </div>
        </section>
            
        <section class="section" id="more_features" style="display:none;">
            <div class="container">
                <div class="row align-items-stretch">
                    <div class="col-12">
                        <h4 class="d-md-block d-none display-3 mb-2">What’s the Best TikTok Username Generator?</h4>
                        <h4 class="d-md-none display-2 mb-4 pb-1">What’s the Best <br> TikTok Username <br> Generator?</h4>
                        <p class="d-md-block d-none pt-1 mb-6">
                        SocialBu's TikTok username generator creates fresh, memorable TikTok username ideas fast for influencers, gamers, and businesses—whether you’re looking for a TikTok username for girls, a stylish TikTok username, or something unique.
                    </p>
                    <p class="d-md-none pt-1 mb-5">
                        SocialBu's TikTok username generator creates fresh, memorable TikTok username ideas fast for influencers, gamers, and businesses—whether you’re looking for a TikTok username for girls, a stylish TikTok username, or something unique.
                    </p>
                    </div>
                    <div class="col-md-6 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3 px-20">
                        <div class="card bg-primary-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pt-5 pb-6 px-5">
                                <h6 class="d-md-block d-none display-5 mb-3">Enter Keywords</h6>
                                <h6 class="d-md-none display-5 mb-5">Enter Keywords</h6>
                                <p class="mb-0 d-md-block d-none">Add words, describe your vibe—like “dance” or “fashion” for TikTok username ideas with your name.</p>
                                <p class="d-md-none pt-1 mb-0">
                                    Add words, describe your vibe—like “dance” or “fashion” for TikTok username ideas with your name.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-12 mb-md-0 mb-4">
                        <div class="card bg-success-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pt-5 pb-6 px-5">
                                <h6 class="d-md-block d-none display-5 mb-3">Choose Style</h6>
                                <h6 class="d-md-none display-5 mb-5">Choose Style</h6>
                                <p class="mb-0">Select tone, category easily—think TikTok username stylish or casual.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-danger-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pt-5 pb-6 px-5">
                                <h6 class="d-md-block d-none display-5 mb-3">Customize Options</h6>
                                <h2 class="d-md-none display-5 mb-5">Customize Options</h2>
                                <p class="mb-0">Set length, characters quickly for a TikTok username change.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-warning-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pt-5 pb-6 px-5">
                                <h6 class="d-md-block d-none display-5 mb-3">Pick Favorite</h6>
                                <h2 class="d-md-none display-5 mb-5">Pick Favorite</h2>
                                <p class="mb-0">Choose and use your TikTok username.</p>
                            </div>
                        </div>
                    </div> 
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100 d-flex flex-column">
                            <div class="pt-md-6 pb-md-6 pt-5 pb-6 px-5 d-flex flex-column align-items-center justify-content-between">
                                <h6 class="display-5">Why don't you try for yourself?</h6>
                                <button type="submit" class="btn btn-primary mt-4">Generate Username</button>
                            </div>
                        </div>
                    </div>                                     
                </div>
            </div>
        </section>

       <section class="section">
            <div class="container px-20">
                <div class="row align-items-stretch">
                    <div class="col-12 px-20">
                        <h4 class="display-4 mb-2">
                            Create Your Perfect TikTok Username in Seconds
                        </h4>
                        <p class="pt-1 mb-6">
                            With short videos and trends, you can express yourself, join the fun, and connect with a global audience no matter where you are. It’s simple, fast, and fun! Here’s what you’ll do:
                        </p>
                    </div>
                    <div class="col-md-4 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">1. Drop your Keywords</h6>
                                <p class="mb-5">Think about what makes you you. Love dancing? Into fashion? Starting a trend? Add a few words separated by commas like "dance, trendy, vibe" to get a unique TikTok username or TikTok username ideas for girls.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">2. Add a Short Description</h6>
                                <p class="mb-0">Tell us what you’re aiming for. Want a TikTok username for your personal account, a throwaway account, or something bold for your TikTok presence?</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">3. Pick a Tone & Category</h6>
                                <p class="mb-0">Choose a vibe—formal, casual, or fun—and a category like fashion, dance, or personal for stylish TikTok username ideas.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">4. Customize Your Username</h6>
                                <p class="mb-0">Decide how long you want it (8 characters, 10, or more), and whether you want numbers or special characters to make your TikTok username for girls pop.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">5. Hit Generate & Repeat</h6>
                                <p class="mb-0">In seconds, you’ll get 12 unique TikTok username ideas—including TikTok username ideas with your name or without your name—you can tweak, copy, and use on TikTok. Didn’t like options? Generate again with our TikTok username generator.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-5">
                        <div class="card bg-light rounded-xl h-100 d-flex flex-column">
                            <div class="pt-md-6 pb-md-6 pt-5 pb-6 px-5 d-flex flex-column align-items-center justify-content-between">
                                <h6 class="display-5">Ready to give it a shot?</h6>
                                <a href="#top" class="btn btn-primary mt-6">Generated Usernames</a>
                            </div>
                        </div>
                    </div>                                           
                </div>
            </div>
        </section>

        @include('common.internal.cta-image')
        
        <section class="section">
            <div class="container">
                

                <div class="row gap-y justify-content-center">
                    <div class="col-md-9">
                        <header class="section-header mb-md-7 mb-6 pb-md-3 pb-2 text-start">
                            
                        </header>
                        <h6 class="d-md-block d-none display-4 mb-3 mx-3">FAQs</h6>
                        <h2 class="d-md-none display-6 mb-4">FAQs</h2>
                        <div class="accordion accordion-arrow-right border border-light rounded-2xl px-5 py-2" id="frequent-questions">
                            @foreach([
                            ['id' => '1', 'question' => 'Is the TikTok username generator free to use?', 'answer' => 'Yes, our Free TikTok Username Generator is completely free. Generate unlimited unique TikTok username ideas instantly, helping you find the perfect TikTok username without any cost.'],
                            ['id' => '2', 'question' => 'What’s next after finding a username?', 'answer' => 'Enjoy your new TikTok username on TikTok. You might want to explore SocialBu to manage and grow your social media presence automatically. Learn more.'],
                            ['id' => '3', 'question' => 'Can I create a username for my small business account?', 'answer' => 'Absolutely. Use the "Business" category and formal tone to create professional TikTok username ideas that align with your brand identity.'],
                            ['id' => '4', 'question' => 'What to do if my username is already taken?', 'answer' => 'Our TikTok username generator creates fresh, unique TikTok username ideas. If needed, you can tweak keywords or customize them with numbers or special characters for a TikTok username change.'],
                            ['id' => '5', 'question' => 'How to make a username with my name?', 'answer' => 'Simply enter your name as a keyword, choose your tone and category, and our TikTok username generator will create TikTok username ideas with your name, ensuring a personal touch for rare results.'],
                            ['id' => '6', 'question' => 'Will the generator suggest famous TikTok username ideas for my brand?', 'answer' => 'Yes, if you select the business category and formal tone, you’ll get standout TikTok username ideas that elevate your brand’s presence with professional, memorable TikTok usernames.'],
                            ['id' => '7', 'question' => 'Are the usernames generated truly unique TikTok username ideas?', 'answer' => 'Our stylish TikTok username generator uses advanced algorithms to create unique TikTok username ideas for girls and others, but we recommend checking TikTok’s availability to ensure your chosen TikTok username isn’t taken.'],
                            ['id' => '8', 'question' => 'Can I customize aesthetic TikTok username ideas with special characters?', 'answer' => 'Yes, enable the special characters option, set your preferred length, and generate stylish TikTok username ideas that stand out with a creative flair.'],
                            ['id' => '9', 'question' => 'Can I get username ideas for dancing or startup?', 'answer' => 'Yes, you can create TikTok username ideas for girls or businesses that match your interests or style by picking the "Dance," "Business," or "Personal" categories and fun/casual tones with our TikTok username generator.'],
                            ] as $index => $faq)
                            <div class="card shadow-none mb-2">
                                <div class="card-header px-0 pt-5 mb-1 {{ !$loop->first && !$loop->last ? 'border-bottom pb-5' : 'pb-2' }}" data-toggle="collapse" href="#collapse-{{ $faq['id'] }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    <div class="card-title d-flex align-items-center justify-content-between">
                                        <h6 class="mb-0 display-6 font-weight-700">{{ $faq['question'] }}</h6>
                                        <i class="ph {{ $index === 0 ? 'ph-caret-up' : 'ph-caret-down' }} ph-md"></i>
                                    </div>
                                </div>
                                <div id="collapse-{{ $faq['id'] }}" class="collapse {{ $index === 0 ? 'show' : '' }}" data-parent="#frequent-questions">
                                    <div class="card-body pb-3 px-0 pt-0 {{  !$loop->last ? 'border-bottom' : '' }}">
                                        <p class="mb-3">{!! $faq['answer'] !!}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

@endsection
