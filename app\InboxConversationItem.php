<?php

namespace App;

use App\Respond\InboxManager;
use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\InboxConversationItem
 *
 * @property int $id
 * @property string $external_id
 * @property array|null $data
 * @property array|null $attachments
 * @property string $type
 * @property int $inbox_conversation_id
 * @property int|null $parent_id
 * @property int|null $social_contact_id
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|InboxConversationItem[] $children
 * @property-read int|null $children_count
 * @property-read \App\InboxConversation $conversation
 * @property-read InboxConversationItem|null $parent
 * @property-read InboxConversationItem|null $root
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereAttachments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereInboxConversationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereSocialContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string|null $status
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereStatus($value)
 * @property-read \App\SocialContact|null $contact
 * @property string|null $fetched_at
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversationItem whereFetchedAt($value)
 */
class InboxConversationItem extends Model
{
    use HasOptions;

    protected $fillable = [
        'inbox_conversation_id', 'external_id', 'data', 'type', 'parent_id', 'social_contact_id', 'status',
    ];

    protected $casts = [
        'data' => 'array',
        'options' => 'array',
        'attachments'=> 'array'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public static function transform(self $item){
        return collect([
            'id' => $item->id,
            'data' => $item->data,
            'attachments' => $item->getAttachments(),
            'type' => $item->type,
            'parent_id' => $item->parent_id,
            'status' => $item->status, // will be used for sending status; like "sent" or "failed" maybe or "pending"
            'timestamp' => $item->created_at->getTimestamp(),
            'external_id' => $item->external_id,
        ]);
    }

    public static function findItem(string $type, string $externalId)
    {
        return self::where('type', $type)
            ->where('external_id', $externalId)
            ->first();
    }

    /**
     * @param string $objectType
     * @param array{external_id: string, conversation_external_id: string, data: array, conversation_type?: string, parent_id?: string, timestamp: int, conversation?: InboxConversation, account: Account} $data
     * @param array{external_id: string, name: string, type: string, profile_pic?: string, options?: object} $sender
     * @return InboxConversationItem
     * @throws \Exception
     */
    public static function createItem(string $objectType, array $data, array $sender): InboxConversationItem
    {
        try {
            \Validator::make($data, [
                'external_id' => 'required|string', // external id of the object - we will use this to find the object in our db if needed

                'conversation_external_id' => 'required', // external conversation/thread id

                'data' => 'required|array', // the data of the change - should have 'text' key for message/comment etc
                'data.text' => 'required|string',


                'parent_id' => 'nullable|string', // the id of the parent item if any
                'timestamp' => 'required|integer', // the timestamp of the item

                'conversation_status' => 'nullable|string', // status of the convo (open, closed, pending, etc.)

                'attachments' => 'array', // if any attachments (array) - should be UploadedFile objects
                'attachments.*' => 'file',
            ])->validate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw new \Exception('Invalid data: ' . array_first($e->errors()));
        }

        $account = $data['account'] ?? null;
        if(!$account){
            throw new \Exception('Account not provided');
        }

        // validate the sender data
        SocialContact::validateContact($sender);

        // if the sender is blocked, we don't create the item
        if(SocialContact::shouldIgnore($sender['type'], $sender['external_id'], $account->id)){
            throw new \Exception('Sender is blocked');
        }

        // get the conversation
        $conversation = $data['conversation'] ?? InboxConversation::findOrCreateConversation(array_filter([
            'external_id' => $data['conversation_external_id'],
            'type' => $data['conversation_type'] ?? $data['type'],
            'status' => $data['conversation_status'] ?? null,
            'options' => $data['conversation_options'] ?? null,
            'data' => $data['conversation_data'] ?? null,
            'timestamp' => $data['conversation_timestamp'] ?? null,
        ]), $sender, $account);

        // create the conversation item if not already found
        $item = InboxConversationItem::findItem($objectType, $data['external_id']);

        if(!$item){
            /* @var InboxConversationItem $item */
            $item = $conversation->items()->make([
                'external_id' => $data['external_id'],
                'data' => $data['data'],
                'type' => $objectType,
                'social_contact_id' => $sender['external_id'] !== $account->account_id ? SocialContact::findOrCreateContact($account, $sender)->id : null,
                'parent_id' => $data['parent_id'] ?? null,
            ]);

            $item->created_at = Carbon::createFromTimestamp($data['timestamp']);
            $item->save();

            $conversation->updateLastItem($sender, $item);

            // update the conversation last message
            // only update if the timestamp is greater than the current last message timestamp
            $lastTimestamp = $conversation->data['last_item']['timestamp'] ?? 0;

            if($data['timestamp'] > $lastTimestamp){

                if($conversation->status === 'muted'){
                    // do not change status
                    $data['conversation_status'] = 'muted';
                }

                $newStatus = $data['conversation_status'] ?? 'open';

                if ($conversation->status !== $newStatus) { // update the status if needed
                    $conversation->status = $newStatus;
                    $conversation->save();
                }
            }
        }

        return $item;
    }

    /**
     * Get attachments
     */
    public function getAttachments(){
        return transform_attachments((array) $this->attachments);
    }

    /**
     * Get the feed associated with this object.
     */
    public function conversation()
    {
        return $this->belongsTo(InboxConversation::class, 'inbox_conversation_id');
    }

    public function contact(){
        return $this->belongsTo(SocialContact::class, 'social_contact_id');
    }

    /**
     * Get the  parent feed post.
     */
    public function parent()
    {
        return $this->belongsTo(self::class);
    }

    public function children(){
        return $this->hasMany(self::class, 'parent_id'); //->with('children')
    }

    public function getAccount(){
        return $this->conversation->account;
    }

    /**
     * @throws \Exception
     */
    public function getApiObject(): Respond\BaseObject
    {
        $mngr = new InboxManager($this->getAccount());
        return $mngr->getApiObject($this);
    }

    private function getOptions(){
        return $this->options;
    }

    private function setOptions($options){
        $this->options = $options;
        $this->save();
        return $this;
    }
}
