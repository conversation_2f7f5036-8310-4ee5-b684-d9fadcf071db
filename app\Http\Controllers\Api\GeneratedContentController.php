<?php

namespace App\Http\Controllers\Api;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Account;
use App\CurationItem;
use App\GeneratedContent;
use App\Helpers\ContentGenerator;
use App\Post;
use App\Team;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Cache\RateLimiter;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class GeneratedContentController extends Controller
{
    private function query(){
        $teamIds = user()->joinedTeams->filter(function(/** @var $team Team */ $team){
            return $team->hasPermission(user(), 'generated_content.view');
        })->map(function (/** @var $team Team */ $team) {
            return $team->id;
        })->toArray();

        return GeneratedContent::query()
            ->where(function(/** @var $query Builder */ $query) use($teamIds){
                // only show for teams where user has permission or where user generated the content
                return $query
                    ->whereIn('team_id', $teamIds)
                    ->orWhere(function (/** @var $query Builder */ $query) {
                        $query->where('team_id', null)->where('user_id', user()->id);
                    });
            });
    }

    public function index(Request $request)
    {
        $teamIds = user()->joinedTeams->filter(function(/** @var $team Team */ $team){
            return $team->hasPermission(user(), 'generated_content.view');
        })->map(function (/** @var $team Team */ $team) {
            return $team->id;
        })->toArray();
        $paginated = $this->query()
            ->when($request->input('type'), function(/** @var $query Builder */ $query, $type){
                // filter by type if needed
                return $query->where('type', $type);
            })
            ->orderBy('created_at', 'desc') // so recently created content is first
            ->paginate();

        return collect([
            'items' => collect($paginated->items())->map(function(/** @var $generatedContent GeneratedContent */ $generatedContent){
                return $generatedContent::transform($generatedContent);
            }),
            'currentPage' => $paginated->currentPage(),
            'lastPage' => $paginated->lastPage(),
            'nextPage' => $paginated->hasMorePages() ? $paginated->currentPage() + 1 : null,
            'total' => $paginated->total(),
        ]);
    }

    /**
     * @throws \Exception
     */
    public function create(Request $request){
        return $this->generatePost($request);
    }

    /**
     * @throws \Exception
     */
    public function createFromPost(Request $request){
        // only need post id for this endpoint
        $this->validate($request, [
            'post_id' => 'required|exists:posts,id',
        ]);

        /** @var Post $post */
        $post = PostsController::query()->findOrFail($request->input('post_id'));

        if($post->type !== 'text'){
            throw new \Exception('Only text posts can be used to generate content');
        }

        $contentType = 'generic';
        if(Str::contains($post->account->type, 'linkedin')){
            $contentType = 'linkedin_post';
        } else if(Str::contains($post->account->type, 'twitter')){
            $contentType = 'tweet';
        }

        $team = $post->getTeam(user());

        // find topic
        $topics = $post->getOption('related_topics');
        if($topics === null){
            try {
                $ret = ContentGenerator::getInstance()->generateRelatedTopics($post->content, 'gpt-3');

                $topics = collect($ret['topics'])->sortByDesc(function($topic){
                    return Str::length($topic);
                })->filter()->toArray();

                if(empty($topics)){
                    // no topic found? strange, try our backup method
                    $newTopic = (ContentGenerator::getInstance()->generateTopic($post->content))['topic'];
                    if($newTopic){
                        $topics[] = $newTopic;
                    }
                }

            } catch(\Exception $e){
                report($e);
                throw new \Exception('Error generating topics from post');
            }

            if(!empty($topics)){
                // we have topics here, so save it in post for future re-use
                $post->setOption('related_topics', $topics);
            }
        }

        if(empty($topics)){
            throw new \Exception('No suitable topics found for post');
        }

        // here we have everything required for generating the post
        $request->merge([
            'type' => $contentType,
            'topic' => Arr::random($topics),
            'team_id' => $team ? $team->id : null,
            'account_id' => $post->account->id,
        ]);

        return $this->generatePost($request);
    }

    public function update(Request $request, $id){
        $this->validate($request, [
            'is_bad' => 'bool',
        ]);

        $generatedContent = $this->query()->findOrFail($id);

        if( now()->diffInDays($generatedContent->created_at) > 2 ){
            abort(400, 'You can only flag content that was created within the past 2 days');
        }

        if($request->has('is_bad')){
            $generatedContent->is_bad = (boolean) $request->input('is_bad');
        }

        $generatedContent->save();
    }

    /**
     * Generate post content
     * @throws \Exception
     */
    private function generatePost(Request $request){
        $this->validate($request, [
            'type' => 'required|in:tweet,linkedin_post,instagram_caption,facebook_post,tiktok_caption,youtube_video_description,reddit_post,pinterest_pin,google_business_profile_post,mastodon_post,generic',
            'topic' => 'required|string|max:280', // the keyword to generate content for
            'team_id' => 'nullable|integer|exists:teams,id', // team to assign to this content
            'account_id' => 'required|integer|exists:accounts,id', // account to assign to this content
        ]);
        $team = $request->input('team_id') ? Team::find($request->input('team_id')) : null;
        if($team && !$team->hasPermission(user(), 'generated_content.generate')){
            // if creating for a team that we don't have permission to generate content for
            abort(403, 'You do not have permission to generate content for this team');
        }

        if(!$team){
            // not creating for team, so check if user owns account
            if(!user()->accounts()->find($request->input('account_id'))){
                // user does not own account, so abort
                abort(403, 'You do not have permission to generate content for this account');
            }
        }

        $account = user()->getAvailableAccounts(['*'], true)->firstWhere('id', $request->input('account_id'));
        if(!$account){
            throw new \Exception( 'Invalid account selected');
        }

        // check limit
        if( !planHasFeature('content_generation', $team ?: $account) ){
            abort(403, 'Content generation feature is not available for ' . ($team ? 'this team' : 'this account'));
        }
        if(getUsage('generated_content', $team ?: $account) + 1 > getPlanUsage('generated_content', $team ?: $account)){
            abort(403, ($team ? 'Content generation limit is reached for this account/team. ' : 'You have reached your content generation limit. ') . ('Maximum content allowed: ' . getPlanUsage('generated_content', $team ?: $account)));
        }

        if($request->input('type') === 'tweet' && $account->type !== 'twitter.profile'){
            throw new \Exception( 'The account_id should be of a Twitter account');
        } else if($request->input('type') === 'linkedin_post' && !Str::contains($account->type , 'linkedin')){
            throw new \Exception( 'The account_id should be of a LinkedIn account');
        } else if($request->input('type') === 'instagram_caption' && !Str::contains($account->type , 'instagram')){
            throw new \Exception( 'The account_id should be of a Instagram account');
        }

        $userContent = '';
        $arrContent = collect( $this->getRecentPostsWithTopics($account) )->map(function($d) {
            return [
                'text' => $d['post'],
                'topic' => $d['topic'],
            ];
        })->toArray();

        if(count($arrContent) > 0){
            $userContent = '### Start: Samples from recent posts of the account ###'. "\n";
            foreach ($arrContent as $content) {
                $userContent .= 'Topic: ' . $content['topic'] . "\n" . 'Post: ' . $content['text'] . "\n###\n";
            }
            $userContent .= '### End: Samples from recent posts of the account ###'. "\n";
        }


        $model = 'gpt-3';

        $input = '';
        $content = '';

        // generate content
        $tries = 1;
        while( strlen(trim($content)) < 3 && $tries <= 10){

            if($tries > 1){
                // something happened, so pick another model
                if($model === 'gpt-neo'){
                    $model = 'gpt-j';
                } else if($model === 'gpt-j'){
                    $model = 'gpt-3';
                } else if($model === 'gpt-3'){
                    $model = 'gpt-neo';
                }
            }

            try {
                $generated = ContentGenerator::getInstance()->generatePost($request->input('topic'), $userContent, $model, $request->input('type'));

                $content = trim($generated['post']);
                // trim special non-break-space
                $content = trim($content, ' ');

                // store input used to generate content
                $input = $generated['input'];
            } catch (\Exception $exception){

                $content = '';

                // report
                report($exception);
            }


            $uniqueChars = count( array_unique( str_split( $content)));
            if($uniqueChars < 3){
                // probably not correct, so try again
                $content = '';
            } else if(strlen(trim($content)) < 3){
                // we failed to generate content, so try again
                $content = '';
            }

            if(Str::contains($content, 'post:')){
                $content = '';
            }

            if(Str::contains($content, $request->input('type') . ':')){
                $content = '';
            }

            if(Str::contains($content, 'topic:')){
                $content = '';
            }

            ++$tries;

        }
        if(strlen(trim($content)) < 3){
            // we failed to generate content, so abort
            abort(500, 'Failed to generate content: '.  $content . $tries);
        }

        // now save content
        /** @var GeneratedContent $generatedContent */
        $generatedContent = GeneratedContent::create([
            'type' => $request->input('type'),
            'content' => $content,
            'keyword' => $request->input('topic'),
            'account_id' => $account->id,
            'team_id' => $team ? $team->id : null,
            'user_id' => user()->id,
        ]);
        $generatedContent->setOption('input', $input);
        $generatedContent->setOption('model', $model);


        return response()->json($generatedContent::transform($generatedContent), 201);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function autocompletePost(Request $request){

        if($request->input('content') === null){
            $request->merge([
                'content' => '',
            ]);
        }

        $this->validate($request, [
            'account_id' => 'required|exists:accounts,id',
            'content' => 'nullable|string|max:1000',
            'team_id' => 'nullable|integer|exists:teams,id',
        ]);

        $team = $request->input('team_id') ? Team::find($request->input('team_id')) : null;
        if($team && !$team->hasPermission(user(), 'generated_content.generate')){
            // if creating for a team that we don't have permission to generate content for
            abort(403, 'You do not have permission to generate content for this team');
        }

        if(!$team){
            // not creating for team, so check if user owns account
            if(!user()->accounts()->find($request->input('account_id'))){
                // user does not own account, so abort
                abort(403, 'You do not have permission to generate content for this account');
            }
        }

        /** @var Account $account */
        $account = user()->getAvailableAccounts(['*'], true)
            ->where('id', $request->input('account_id'))
            ->first();

        if (!$account) {
            abort(404, "Invalid request.");
        }

        // check limit
        if( !planHasFeature('content_autocomplete', $team ?: $account) ){
            abort(403, 'Content autocompletion feature is not available for ' . ($team ? 'this team' : 'this account') . '. Please upgrade your plan.');
        }

        $userContent = collect( $this->getRecentPostsWithTopics($account) )->map(function($d) {
            return $d['post'];
        })->toArray();

        $modelToUse = 'gpt-3';

        try {
            $generated = ContentGenerator::getInstance()->autocomplete($request->input('content'), $userContent, $modelToUse);

            $content = trim($generated['content']);
            // trim special non-break-space
            $content = trim($content, ' ');

            // store input used to generate content
            return response()->json([
                'content' => $content
            ]);
        } catch (\Exception $e) {
            abort(500, $e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function generatePostForCurationItem(Request $request){
        $this->validate($request, [
            'id' => 'required|integer|exists:curation_items,id',
        ]);

        $hasFeature = planHasFeature('curation_generate');
        if(!$hasFeature){
            $teams = user()->joinedTeams;
            foreach($teams as $team){
                if(planHasFeature('curation_generate', $team)){
                    $hasFeature = true;
                    break;
                }
            }
        }

        // check limit
        if( !$hasFeature ){
            abort(403, 'This feature is only available for the plans: Super, and above. Please upgrade your (or your team\'s plan).');
        }

        $curationItem = CurationItem::find($request->input('id'));

        try {

            $description = $curationItem->description;

            // trim all html
            $description = strip_tags($description);

            $content = ContentGenerator::getInstance()->generateBlogPostCommentary($curationItem->title, $description);

            return response()->json([
                'content' => $content
            ]);
        } catch (\Exception $e) {
            abort(500, $e->getMessage());
        }
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function suggestedTopicsByAccount(Request $request){
        $this->validate($request, [
            'account_id' => 'required|integer|exists:accounts,id',
        ]);

        $account = user()->getAvailableAccounts(['*'], true)->firstWhere('id', $request->input('account_id'));
        if(!$account){
            return [];
        }

        if(!\Cache::has('last_10_posts_topics::' . $account->id)){
            return [];
        }

        $userPosts = $this->getRecentPostsWithTopics($account);

        return collect($userPosts)->unique('topic')->unique('post')->map(function($t){
            return $t['topic'];
        })->filter(function($t){
            $uniqueChars = count( array_unique( str_split( $t)));
            if($uniqueChars < 3){
                return false;
            } else if(Str::contains($t, 'http')){
                // link? bogus
                return false;
            }
            return true;
        })->unique()->take(10)->values()->toArray();
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function suggestedTopicsByHistory(Request $request){
        $this->validate($request, [
            'account_id' => 'required|integer|exists:accounts,id',
        ]);
        $account = user()->getAvailableAccounts(['*'], true)->firstWhere('id', $request->input('account_id'));
        if(!$account){
            throw new \Exception( 'Invalid account selected');
        }

        $keywordsToQuery = $this->query()
            ->where('is_bad', false)
            ->where('account_id', $request->input('account_id'))
            ->groupBy(['keyword', 'created_at'])
            ->select('keyword')
            ->orderBy('created_at', 'desc')
            ->limit(5)->get()->map(function($t){
                return $t->keyword;
            })->unique()->values()->shuffle()->toArray();

        return array_merge( collect($this->getRelatedTopics($keywordsToQuery))->shuffle()->take(5)->toArray() , $keywordsToQuery); // max 15
    }

    /**
     * @throws \Exception
     */
    public function suggestedTopicsBySearch(Request $request){
        $this->validate($request, [
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|min:3',
            'account_id' => 'nullable|integer|exists:accounts,id',
        ]);

        $account = null;
        if($request->input('account_id') > 0){
            $account = user()->getAvailableAccounts(['*'], true)->firstWhere('id', $request->input('account_id'));
        }

        return array_slice($this->getRelatedTopics($request->input('keywords'), $account), 0, 10); // max 10
    }

    /**
     * @param array $keywords
     * @param Account|null $account
     * @return array
     */
    private function getRelatedTopics(array $keywords, Account $account = null){

        $keywords = array_unique($keywords);

        $guzzle = ContentGenerator::getInstance()->getGuzzle('openai');

        $all = collect();

        $getAccountPrompt = function(Account $account){

            $accountTopics = collect($this->getRecentPostsWithTopics($account))->map(function($t){
                return $t['topic'];
            })->toArray();

            return implode('\n', [
                'Social account: ' . $account->name,
                !empty($accountTopics) ? 'Recent topics of content:' : '',
                '',
                !empty($accountTopics)  ? '- ' . implode("\n- ", $accountTopics) : '',
                '',
            ]);
        };

        $jsonKeywords = json_encode($keywords);

        \Cache::forget('suggested_keywords::' . $jsonKeywords);
        $data = \Cache::remember('suggested_keywords::' . $jsonKeywords, now()->addDays(7), function() use($guzzle, $getAccountPrompt, $account, $keywords){
            try {
                $res = $guzzle->post('https://api.openai.com/v1/chat/completions', [
                    'json' => [
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode('\n', [
                                    $account ? $getAccountPrompt($account) : '',
                                    'Generate 10 relevant and very specific topics that the user could talk about or publish content about on their social account',
                                    '- Give topics as a list with each line starting with -.',
                                    '- Don\'t include double quotes if not needed.',
                                    '- Make topics straight to the point and very specific (no generic topics).',
                                    '- Each topic should not be too long (ideally within 10 words)',
                                    '',
                                    'Focus on:',
                                    '- ' . implode("\n- ", $keywords),
                                    '',
                                    'Topics:'
                                ]))
                            ],
                        ],
                        'temperature' => 1.0,
                        'max_tokens' => 200,
                        'stop' => ['Topics:'],
                        'top_p' => 1,
                    ],
                ]);

                $data = @json_decode($res->getBody());
            } catch (ClientException $e){
                report($e);
                return null;
            }

            if(!$data){
                return null;
            }

            return $data;
        });

        // add related topics to the list
        if($data){
            // parse
            $content = trim(collect($data->choices)->map(function($choice){
                return $choice->message->content;
            })->unique()->values()->first());

            if($content){
                $all = $all->merge(explode("\n", $content));
                $all = $all->map(function($s){
                    // remove - at start and trim
                    return trim(Str::replaceFirst('-', '', $s));
                });
            }
        }

        return $all->filter(function ($s){
            if(Str::contains($s, [
                'free',
                'new',
                'latest',
                'best',
                'this',
                'top',
                'quote',
            ])){
                return false;
            }
            return true;
        })->unique()->values()->toArray();

    }

    private function getRecentPostsWithTopics($account){
        // use cache if available
        return \Cache::remember('last_10_posts_topics::' . $account->id, now()->addDays(2), function() use($account){

            // make the tweets into the required format
            return Post::where('account_id', $account->id)->where('published_at', '<>', null)->where('content', '<>', null)->orderByDesc('id')->limit(20)->get()->filter(function(/** @var $post Post */ $post){
                // the tweet should have enough characters to be a valid tweet
                $len = strlen(trim($post->content));
                if($len < 10 || $len > 20){
                    return false;
                }

                $uniqueChars = count( array_unique( str_split( $post->content )));
                if($uniqueChars < 3){
                    return false;
                }

                // check if string is valid url
                if (filter_var($post->content, FILTER_VALIDATE_URL) === TRUE) {
                    return false;
                }

                if(Str::contains($post->content, 'http://')){
                    // link? bogus
                    return false;
                }

                if(Str::contains($post->content, 'https://')){
                    // link? bogus
                    return false;
                }

                return true;
            })->take(10)->map(function (/** @var $post Post */ $post){

                $topic = '';
                if($post->getOption('suggested_topic')){
                    return [
                        'post' => $post->content,
                        'topic' => $post->getOption('suggested_topic'),
                    ];
                }

                $modelsToUse = [ // models to use for topic extraction, ordered by priority, at-least one of each should return the topic
                    'gpt-3',
                ];
                while(!empty($modelsToUse) && (strlen($topic) < 3 || count(explode(' ', $topic)) > 10)){ // either the topic is not set or the topic is too long (probably wrong), we re-generate
                    $res = ContentGenerator::getInstance()->generateTopic($post->content, array_shift($modelsToUse)); // this will take time
                    $topic = $res['topic'];

                    $uniqueChars = count( array_unique( str_split( $topic)));
                    if($uniqueChars < 3){
                        // probably not correct, so try again
                        $topic = '';
                    }

                    if(Str::contains($topic, ['tweet:', 'topic:', '__'])){
                        $topic = '';
                    }

                }

                if(strlen(trim($topic)) === 0){
                    // still topic is not generated, so we skip the tweet
                    return null;
                } else if (trim($topic) === trim($post->content)){
                    return null;
                } else if(count(explode("\n", $topic)) > 1){
                    // topic is too long, so we skip the tweet
                    return null;
                }

                // save the topic, so we don't have to do this again
                $post->setOption('suggested_topic', $topic);

                return [
                    'post' => $post->content,
                    'topic' => $topic,
                ];

            })->filter()->unique('post')->unique('topic')->take(10)->values()->toArray();

        });
    }

    public function submitGenerateForm(Request $request, $form)
    {
        try {
            $user = $request->user('api');
            $rateLimiter = app(RateLimiter::class);
            // Check if form contains image field
            $hasImage = str_contains($form, 'image_generator');
            //limits
            $guestLimit = $hasImage ? 2 : 5;
            $userLimit = $hasImage ? 5 : 10;

            [$limit, $key, $message] = $user 
                ? [$userLimit, 'user_generated_' . $user->id, 'Please upgrade your plan for generating more outputs.']
                : [$guestLimit, 'guest_generated_' . $request->ip(), 'Please sign up for more generations.'];
            if ($rateLimiter->tooManyAttempts($key, $limit)) {
                return response()->json(['message' => $message], 429);
            }

            $rateLimiter->hit($key, 60);
            
            $form = new \App\Generate\DynamicForm\Form($form);
            $response = $form->process($request->all());
            return $response;
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            return response()->json([
                'message' => $validationException->getMessage(),
                'errors' => $validationException->validator->errors()->toArray(),
            ], 422);
        } catch (\InvalidArgumentException $invalidArgumentException) {
            return response()->json([
                'message' => $invalidArgumentException->getMessage(),
            ], 400);
        } catch (\Exception $exception) {
            //send generic exception with status code 500
            \Log::info($exception->getMessage());
            return response()->json([
                'message' => "Something went wrong while processing your request. Please try again later.",
            ], 500);
        }

    }
}
