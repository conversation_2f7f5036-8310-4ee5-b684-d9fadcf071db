<template>
    <div class="card border zoom-effect1 post-container rounded-xl mb-2" :class="{ 'border-danger': post.error || !account.active }" v-if="post">
        <div class="card-body post-body p-20">
            <div class="post-accounts d-flex justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="mr-3 position-relative" v-for="(account,i) in post.accounts" :key="i">
                        <img class="profile-pic1 border border-secondary"
                            :src="account.image"
                            :title="account.name" v-tooltip>
                        <img class="border rounded-circle border-white network-icon"
                             :src="getIconForAccount(account.type, 'circular')"
                             :title="account._type" v-tooltip>
                    </div>
                    <h6 class="d-md-block d-none m-0 font-weight-500 text-break" :title="account.name">{{ account.name.length > 60 ? account.name.slice(0, 60) + '...' : account.name }}</h6>
                    <h6 class="d-md-none m-0 font-weight-500 text-break" :title="account.name">{{ account.name.length > 14 ? account.name.slice(0, 14) + '...' : account.name }}</h6>
                </div>
                <div>
                    <div class="post-buttons">
                        <template>
                            <span class="d-md-inline-block d-none mx-2">
                                <span class="badge badge-primary small-2" v-if="post.post_options.post_as_story">
                                    Story
                                </span>
                                <span class="badge badge-warning small-2" v-if="!post.approved">
                                    Awaiting Approval
                                </span>
                                <span class="badge badge-secondary small-2" v-else-if="post.draft">
                                    Draft
                                </span>
                            </span>
                        </template>
                        <span class="d-md-inline-block d-none badge badge-secondary" v-if="post.post_options.badge" v-text="post.post_options.badge"></span>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-sm btn-outline-light border-none p-2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="ph ph-dots-three ph-bold ph-lg"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <div class="dropdown-item cursor-pointer  d-flex align-items-center"
                                    @click="previewPost" v-if="!post.published">
                                    <i class="d-md-block d-none ph ph-eye ph-lg mr-3"></i>
                                    <i class="d-md-none ph ph-eye ph-md mr-2"></i>
                                    Preview
                                </div>
                                <div v-if="!post.published && canEdit" class="d-md-none dropdown-item cursor-pointer  d-flex align-items-center" data-dismiss="modal" @click="onEditPost">
                                    <i class="ph ph-pencil-simple-line ph-md mr-2"></i>
                                    Edit Post
                                </div>
                                <div class="d-md-none dropdown-item cursor-pointer  d-flex align-items-center" @click.prevent="postNow" v-if="!canApprove && canEdit">
                                    <i class="ph ph-paper-plane-tilt ph-md mr-2" aria-hidden="true"></i>
                                    Post Now
                                </div>
                                <div class="dropdown-item cursor-pointer  d-flex align-items-center"
                                    @click="showShortenedLinks" v-if="post.shortened_links.length">
                                    <i class="d-md-block d-none ph ph-link ph-lg mr-3"></i> 
                                    <i class="d-md-none ph ph-link ph-md mr-2"></i> 
                                    Shortened links
                                </div>
                                <div class="dropdown-item cursor-pointer d-flex align-items-center"
                                        v-if="!attachmentsDeleted || !post.published"
                                        @click="onDuplicatePost">
                                    <template v-if="!post.published">
                                        <i class="d-md-block d-none ph ph-copy ph-lg mr-3"></i>
                                        <i class="d-md-none ph ph-copy ph-md mr-2"></i>
                                        Duplicate
                                    </template>
                                    <template v-else>
                                        <i class="d-md-block d-none ph ph-arrow-bend-up-right ph-lg mr-3"></i> 
                                        <i class="d-md-none ph ph-arrow-bend-up-right ph-md mr-2"></i>
                                        <span>Republish</span>
                                    </template>
                                </div>
                                <div class="dropdown-item cursor-pointer d-flex align-items-center"
                                    title="Let AI write a post similar to this one"
                                    v-if="post.type === 'text'"
                                    @click="generateSimilarPosts">
                                    <i class="d-md-block d-none ph ph-sparkle ph-lg mr-3"></i> 
                                    <i class="d-md-none ph ph-sparkle ph-md mr-2"></i>
                                    Generate Similar
                                </div>
                                <div class="dropdown-item cursor-pointer  d-flex align-items-center "
                                    @click.prevent="moveToDrafts" v-if="!post.draft && canEdit">
                                    <i class="d-md-block d-none ph ph-floppy-disk ph-lg mr-3" aria-hidden="true"></i>
                                    <i class="d-md-none ph ph-floppy-disk ph-md mr-2" aria-hidden="true"></i>
                                    Save as Draft
                                </div>
                                <div class="dropdown-item cursor-pointer  d-flex align-items-center"
                                        @click.prevent="deletePost" v-if="canEdit">
                                    <i class="d-md-block d-none ph ph-trash ph-lg mr-3" aria-hidden="true"></i>
                                    <i class="d-md-none ph ph-trash ph-md mr-2" aria-hidden="true"></i>
                                    {{$lang.get('generic.delete')}}
                                </div>
                            </div>

                            <portal target-el="#shortenedLinks" v-if="renderShortenedLinks">
                                <div class="list-group">
                                    <div class="list-group-item" v-for="link in post.shortened_links">
                                        <h6>
                                            {{ link.link }}
                                        </h6>
                                        <p class="small">
                                            {{ link.target }}
                                        </p>
                                    </div>
                                </div>
                            </portal>

                            <portal target-el="#previewPost" v-if="renderPreview">
                                <Preview
                                    :account="post.accounts[0]"
                                    :text="post.content"
                                    :attachments="post.attachments"
                                    :options="post.post_options || {}"
                                    :link="link"
                                />
                            </portal>

                            <portal target-el="#generatedPosts" v-if="renderGeneratedPosts">
                                <div class="card border border-primary">
                                    <div class="card-body d-flex align-items-center">
                                        <div>
                                            <i class="ph ph-magic-wand ph-3xl socialbu-color" title="This AI Writer is your friend"></i>
                                        </div>
                                        <div class="border-left border-primary lead text-dark pl-4 ml-4 pr-6">
                                            <div v-html="spinnerHtml" v-if="ajaxPending"></div>
                                            <div v-else>
                                                <div v-if="!generatedPost">
                                                    The content written by AI will show here...
                                                </div>
                                                <div v-else>
                                                    {{ generatedPost }}
                                                </div>
                                            </div>
                                            <div class="border-top text-right pt-2 mt-2"
                                                v-if="!ajaxPending">
                                                <button class="btn btn-sm btn-link text-muted"
                                                        @click="generatePost"
                                                        v-if="!ajaxPending">
                                                    Re-generate
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary"
                                                    @click="createPost(generatedPost)"
                                                    v-if="generatedPost"
                                                >
                                                    Create post
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-muted small mt-4">
                                    The generated content is saved in your account and you can access it <a target="_blank" href="/app/generate">here</a>.
                                </div>
                            </portal>

                        </div>
                    </div>
                </div>
            </div>
            <div class=" d-flex flex-md-row flex-column justify-content-between align-items-start rounded">
                <div v-if="post.attachments.length" class="text-center"
                     :class="{'col-12__': (post.attachments.length) && !post.content, 'col-2__ d-flex align-items-center': (post.attachments.length) && post.content}">
                    <template v-if="post.attachments[0].mime.includes('image')">
                        <div :class="{'mb-2' : post.content}"
                             v-if="post.content" style="position: relative">
                            <img class="cursor-pointer post-image border rounded-lg"
                                 :src="post.attachments[0].url"
                                 :alt="post.attachments[0].name"
                                 @click="openAttachments(post.attachments)"
                                 v-tooltip="$lang.get('generic.view')"
                                 v-lazy
                                 v-if="!attachmentsDeleted" />
                            <template v-else>
                                <div
                                     style="position: absolute;width: 100%;text-align: center;opacity: 0.8;height: 100%;"
                                     class="d-flex align-items-center justify-content-center">
                                    <i class="ph ph-image"></i>
                                </div>
                                <img class="post-image border rounded-lg" src="/images/1x1.png" style="width: 100px;height: 100px;opacity: 0.2;filter: blur(2px);" />
                            </template>
                            <small class="d-md-block d-none badge badge-light bg-white small-2 font-weight-500" style="position: absolute; right: 4px; top: 4px"
                                v-if="post.attachments.length > 1">+{{ post.attachments.length - 1 }} more</small>
                            <small class="d-md-none badge badge-light text-white small-2 font-weight-500" style="position: absolute; right: 23px; bottom: 8px"
                                v-if="post.attachments.length > 1">+{{ post.attachments.length - 1 }} more</small>
                        </div>
                        <template v-else>
                            <div v-tooltip="$lang.get('generic.view')" v-if="!attachmentsDeleted">
                                <img v-for="(attachment,i) in post.attachments" :key="i"
                                     :src="attachment.url"
                                     :alt="attachment.name"
                                     @click="openAttachments(post.attachments)"
                                     v-lazy
                                     class="cursor-pointer post-image border rounded-md" />
                            </div>
                            <div class="d-flex" v-else>
                                <div v-for="(attachment,i) in post.attachments" :key="i">
                                    <div style="position: absolute;width: 100%;text-align: center;opacity: 0.8;height: 100%;" class="d-flex align-items-center justify-content-center">
                                        <i class="ph ph-image"></i>
                                    </div>
                                    <img src="/images/1x1.png" style="width: 100px;height: 100px;opacity: 0.2;filter: blur(2px);"
                                         :alt="attachment.name"
                                         class="post-image border rounded-lg" />
                                </div>
                            </div>
                        </template>
                    </template>
                    <template v-else-if="post.attachments[0].mime.includes('video')">
                        <video class="cursor-pointer post-image border rounded-md" preload="metadata"
                               v-tooltip="'Watch'" v-if="!attachmentsDeleted"
                               v-lazy
                               @click="openAttachments(post.attachments)">
                            <source :src="post.attachments[0].url"/>
                        </video>
                        <div
                             v-else>
                            <div style="position: absolute;width: 100%;text-align: center;opacity: 0.8;height: 100%;" class="d-flex align-items-center justify-content-center">
                                <i class="ph ph-video-camera"></i>
                            </div>
                            <img src="/images/1x1.png" style="width: 100px;height: 100px;opacity: 0.2;filter: blur(2px);"
                                 class="post-image border rounded" />
                        </div>
                    </template>
                    <template v-else>
                        <template v-for="(attachment,i) in post.attachments">
                            <template>
                                <div :key="i">
                                    <a class="document-link d-flex align-items-center justify-content-center flex-column border rounded-lg p-2" :href="attachment.url" target="_blank">  <i class="ph ph-lg ph-download-simple"></i>{{ truncate(attachment.name.split('.').slice(0, -1).join('.'), { length: 8, omission: "..." }) + '.' + attachment.name.split('.').pop() }}
                                    </a>
                                </div>
                            </template>
                        </template>
                    </template>
                </div>
                <div class="flex-fill"
                     :class="{'col-12__': !post.attachments.length, 'col-10__ ml-md-4': post.attachments.length}">
                    <div class="post-text" :class="{'cursor-default': post.published }">
                        <RichText
                            :value="post.content"
                            :highlight="highlightConfig"
                            :readonly="true" />
                    </div>
                </div>
            </div>
            <div class="mt-2" v-if="!post.published">
                <div class="d-flex justify-content-between flex-md-row flex-column">
                    <div class="d-flex align-items-center mb-md-0 mb-2">
                        <div v-if="!post.draft" class="mr-md-2 mr-3">
                            <div>
                                <button class="btn btn-xs d-flex align-items-center border-none p-0" @click.prevent="editTime()"
                                    v-tooltip="$lang.get('generic.change')" v-if="canEdit">
                                    <i class="edit-time-icon ph ph-clock ph-md" aria-hidden="true"></i>
                                    {{ $momentUTC(post.publish_at).format('h:mm a') }}
                                </button>
                                <button class="btn btn-sm d-flex align-items-center border-none p-0" v-else>
                                    <i class="edit-time-icon ph ph-clock ph-md" aria-hidden="true"></i>
                                    {{ $momentUTC(post.publish_at).format('h:mm a') }}
                                </button>
                            </div>
                        </div>
                        <div v-else>
                            <button class="btn btn-outline-light btn-sm d-flex align-items-center mr-2" @click.prevent="editTime(true)">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-clock ph-md mr-2" aria-hidden="true"></i>
                                    Schedule
                                </div>
                            </button>
                        </div>
                        <div class="added-by">
                            <span class="d-md-inline-block d-none small-2 mr-2" v-if="post.user_id !== myUserId">
                                Added by <span class="small-1"> {{ post.user_name }} </span>
                                <span
                                    v-tooltip
                                    :title="
                                        $momentUTC(post.created_at, 'YYYY-MM-DD HH:mm:00').format(
                                            'MMM DD, YYYY - h:mm a'
                                        )
                                    "
                                    >{{ $momentUTC(post.created_at).fromNow() }}
                                </span>
                            </span>
                            <span class="d-md-inline-block d-none small-2 mr-2" v-else>
                                Added
                                <span
                                    v-tooltip
                                    :title="
                                        $momentUTC(post.created_at, 'YYYY-MM-DD HH:mm:00').format(
                                            'MMM DD, YYYY - h:mm a'
                                        )
                                    "
                                    >{{ $momentUTC(post.created_at).fromNow() }}</span
                                >
                            </span>
                        </div>
                        <template>
                            <span class="d-md-none d-inline-block mr-3">
                                <span class="badge badge-primary small-2" v-if="post.post_options.post_as_story">
                                    Story
                                </span>
                                <span class="badge badge-light small-2" v-else-if="post.draft">
                                    Draft
                                </span>
                            </span>
                        </template>
                    </div>
                    <div class="d-flex flex-wrap mb-md-0 mb-2">
                        <div class="mr-2" v-if="Object.keys(post.post_options).length && !showOptions">
                            <label class="cursor-pointer btn btn-sm btn-outline-light d-flex align-items-center mb-0"
                                v-tour:postvue_showmore="'Advanced options are usually hidden by default. You can click this button to reveal those options.'"
                                @click="showOptions = true">
                                <i class="ph ph-caret-down mr-2"></i> Post Options
                            </label>
                        </div>
                        <div class="mr-2" v-else-if="Object.keys(post.post_options).length && showOptions">
                            <label class="cursor-pointer btn btn-sm btn-outline-light d-flex align-items-center mb-0"
                                @click="showOptions = false">
                                <i class="ph ph-caret-up mr-2"></i> Post Options
                            </label>
                        </div>
                        <div v-if="!post.published && canEdit">
                            <!-- data-dismiss is needed on the edit button, because edit opens modal, in case this component is in a modal already, we close that modal -->
                            <button class="btn btn-sm btn-outline-light d-md-flex d-none mr-2" data-dismiss="modal" @click="onEditPost">
                                <i class="ph ph-pencil-simple-line ph-md mr-2"></i>
                                    Edit
                            </button>
                        </div>
                        <div v-if="!post.error">
                            <div class="d-flex mt-md-0 mt-2" v-if="canApprove">
                                <button class="btn btn-outline-light btn-sm mr-2" @click.prevent="approve(false)">
                                    <div class="d-flex align-items-center">
                                        <i class="ph ph-x ph-md text-danger mr-2" aria-hidden="true"></i>
                                        Reject
                                    </div>
                                </button>
                                <button class="btn btn-outline-light btn-sm" @click.prevent="approve(true)">
                                    <div class="d-flex align-items-center">
                                        <i class="ph ph-check ph-md text-success mr-2" aria-hidden="true"></i>
                                        Approve
                                    </div>
                                </button>
                            </div>
                            <div class="d-md-flex d-none" v-if="canEdit">
                                <button class="btn btn-outline-light btn-sm d-flex align-items-center mr-2" @click.prevent="postNow" v-if="!canApprove">
                                    <div class="d-flex align-items-center">
                                        <i class="ph ph-arrow-up-right ph-md mr-2" aria-hidden="true"></i>
                                        Publish Now
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="d-md-block d-none" v-if="post.error">
                            <button class="btn btn-outline-light btn-sm cursor-pointer" @click="editTime(true)">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-arrow-up-right ph-md mr-2" aria-hidden="true"></i>
                                    {{$lang.get('publish.reschedule')}}
                                </div>
                            </button>
                            <button class="btn btn-outline-light btn-sm cursor-pointer" @click="postNow()">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-clock-clockwise ph-md mr-2" aria-hidden="true"></i>
                                    {{$lang.get('generic.retry')}}
                                </div>
                            </button>
                            <button class="btn btn-outline-light btn-sm cursor-pointer" @click="deletePost">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-trash ph-md mr-2"></i>
                                    {{$lang.get('generic.delete')}}
                                </div>
                            </button>
                            
                        </div>
                    </div>
                </div>
                <div>

                    <div class="alert alert-danger mt-3" v-if="!account.active">
                        <span>Social account is disconnected, please <a href="/app/accounts?show=inactive">reconnect the account</a> to ensure this post gets published.</span>
                    </div>
                    <template v-if="post.draft && post.reject_reason">
                        <div class="d-md-block d-none alert alert-warning mt-3">
                            &nbsp; Post Rejected: {{ post.reject_reason }}
                        </div>
                        <div class="d-md-none text-warning mt-3">
                            Post Rejected: {{ post.reject_reason }}
                        </div>
                    </template>
                    <div class="d-flex justify-content-between mt-3" v-else-if="post.error">
                        <div class="d-md-block d-none alert alert-danger col-12 text-break">
                            <span>Error: {{ post.error.message || "We are unable to publish this post." }}</span> 
                        </div>
                        <p class="d-md-none text-danger mb-0">Error: {{ post.error.message || "We are unable to publish this post." }}</p>
                    </div>

                </div>
            </div>
            <div v-else-if="post.published">
                <div class="d-flex align-items-center justify-content-between flex-wrap mt-4">
                    <div class="d-flex align-items-center" :class="post.source === 'queue' ? 'mb-md-0 mb-2' : ''">
                        <span class="small-2 mr-2" v-tooltip :title="$momentUTC(post.published_at).format('D MMM, Y h:mm a')">
                            Published {{ $momentUTC(post.published_at).fromNow() }}
                        </span>
                        <span
                            class=" badge badge-light border rounded"
                            v-tooltip="
                                post.post_options && post.post_options.post_source_name
                                    ? 'Published from ' + post.post_options.post_source_name + ' (queue)'
                                    : 'Published from a queue'
                            "
                            v-if="post.source === 'queue'"
                        >
                            From Queue
                        </span>
                        <span
                            class=" badge badge-light border rounded"
                            v-tooltip="
                                post.post_options && post.post_options.post_source_name
                                    ? 'Added by ' + post.post_options.post_source_name + ' (automation)'
                                    : 'Added by an automation'
                            "
                            v-if="post.source === 'automation'"
                        >
                            Added by Automation
                        </span>
                    </div>
                    <div class="d-flex align-items-center mt-md-0 mt-2">
                        <div v-if="Object.keys(post.post_options).length && !showOptions" class="mr-2">
                            <label class="cursor-pointer btn btn-sm btn-outline-light mb-0"
                                   v-tour:postvue_showmore="'Advanced options are usually hidden by default. You can click this button to reveal those options.'"
                                   @click="showOptions = true">
                                <i class="ph ph-caret-down"></i> Post Options
                            </label>
                        </div>
                        <div v-else-if="Object.keys(post.post_options).length && showOptions" class="mr-2">
                            <label class="cursor-pointer btn btn-sm btn-outline-light d-flex align-items-center mb-0"
                                @click="showOptions = false">
                                <i class="ph ph-caret-up mr-2"></i> Post Options
                            </label>
                        </div>
                        <div class="ml-2" v-if="permalink">
                            <a  target="_blank"
                                :href="permalink"
                                class="text-capitalize btn btn-outline-light btn-sm mr-1">
                                <div class="d-flex align-items-center">
    
                                    <img class="icon-size mr-2" :src="getIconForAccount(account.type)" :alt="account._type">
                                    <span>See Post</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-2 p-4 mx-0 bg-light rounded-md" v-if="showOptions">
                <!-- render post_options -->
                <div class="col-12 pl-0"
                     :class="{
                            'col-md-6': ['threaded_replies', 'title', 'video_title'].includes(option),
                            'col-md-6': !['threaded_replies', 'title', 'video_title'].includes(option),
                         }"
                     v-for="(val, option, i) in post.post_options"
                     :key="i + 'postopts'">
                    <template v-if="option === 'trim_link_from_content'">
                        <div>
                            <h6 class="font-weight-500 mb-1">
                                Trim link from content
                            </h6>
                            <p class="pl-2 border-left">{{ val ? "Yes":"No" }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'share_as_story'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Share as Story </h6>
                            <p class="pl-2 border-left">{{ val ? "Yes":"No" }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'location'">
                        <template v-if="val">
                            <div>
                                <h6 class="font-weight-500 mb-1">Location </h6>
                                <p class="pl-2 border-left">{{ val.title || val.lat + ', ' + val.lng }}</p>
                            </div>
                        </template>
                    </template>
                    <template v-else-if="option === 'thumbnail'">
                        <template v-if="val">
                            <div class="d-flex flex-column">
                                <label class="font-weight-500 text-dark">Thumbnail</label>
                                <img class="cursor-pointer post-image border-left pl-2"
                                    :src="val.url"
                                    :alt="val.name"
                                    @click="openAttachments([val])"
                                    v-tooltip="$lang.get('generic.view')"
                                    v-lazy
                                />
                            </div>
                        </template>
                    </template>
                    <template v-else-if="option === 'threaded_replies'">
                        <label class="font-weight-500 text-dark">Threaded Replies</label>
                        <div class="pl-2 mb-4 border-left"
                             v-for="(opt, i) in val" :key="i + post.id + 'thread reply'">
                            <div class="d-flex justify-content-between"
                                v-if="typeof opt === 'object'">
                                <div>
                                    {{opt['text'] }}
                                </div>
                                <div
                                    v-if="opt['media'] && opt['media'].length">
                                    <template v-if="opt['media'][0].mimeType && opt['media'][0].mimeType.includes('video')">
                                        <video class="cursor-pointer post-image border rounded" preload="metadata"
                                            v-tooltip="'Watch'"
                                            v-lazy
                                            @click="openAttachments(opt['media'])">
                                            <source :src="opt['media'][0].url"/>
                                        </video>
                                    </template>
                                    <div class="position-relative ml-2"
                                        v-else>
                                        <img class="cursor-pointer post-image border rounded"
                                            :src="opt['media'][0].url"
                                            :alt="opt['media'][0].name"
                                            @click="openAttachments(opt['media'])"
                                            v-tooltip="$lang.get('generic.view')"
                                            v-lazy
                                        />
                                        <small class="badge badge-light" style="position: absolute; right: 2px; top: 2px"
                                            v-if="opt['media'].length > 1">+{{ opt['media'].length - 1 }} more</small>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                {{opt}}
                            </div>
                        </div>
                    </template>
                    <template v-else-if="option === 'topic_type'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Post Type </h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'event_title'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Title </h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'event_start'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Start At </h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'event_end'">
                        <div>
                            <h6 class="font-weight-500 mb-1">End At </h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'image_alt_text' || option === 'media_alt_text'">
                        <div v-for="(opt, index) in val" :key="index + 'altText'">
                            <h6 class="font-weight-500 mb-1">Alt text for media {{ index + 1 }} </h6>  
                            <p class="pl-2 border-left">{{opt }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'privacy_status'">
                        <div>
                            <h6 class="font-weight-500 mb-1">{{ account.type == 'google.youtube' ? 'Visibility' : 'Privacy' }}</h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'video_title'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Title</h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else-if="option === 'video_tags'">
                        <div>
                            <h6 class="font-weight-500 mb-1">Tags</h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                    <template v-else>
                        <div>
                            <h6 class="font-weight-500 mb-1 text-capitalize">{{ option.replace(/_/g, ' ') }}</h6>
                            <p class="pl-2 border-left">{{ val }}</p>
                        </div>
                    </template>
                </div>
            </div>
            <div class="post-insights d-flex align-items-center flex-wrap px-20 py-4 bg-light rounded-md mt-2" v-if="post.insights && post.insights.length">
                <template v-for="(metric, i) in post.insights">
                    <div :key="i">
                        <div class="d-md-block d-none mr-6" >
                            <span class="text-capitalize font-weight-500">{{ metric.type.replace(/_/g, ' ') }}:</span>
                            <span v-if="metric.type === 'engagement'">
                                {{ (metric.value * 100).toFixed(1) }}%
                            </span>
                            <span v-else>
                                {{ metric.value }}
                            </span>
                        </div>
                        <div class="d-md-none px-2 mb-2">
                            <span class="text-capitalize font-weight-500">{{ metric.type.replace(/_/g, ' ') }}:</span>
                            <span v-if="metric.type === 'engagement'">
                                {{ (metric.value * 100).toFixed(1) }}%
                            </span>
                            <span v-else>
                                {{ metric.value }}
                            </span>
                        </div>
                    </div>
                </template>
            </div>
        </div>

    </div>
</template>

<script>
import _ from "lodash";
import $ from "jquery";
import { alertify, axios, appConfig, getIconForAccount, getColorClassForAccount, axiosErrorHandler, spinnerHtml } from "../../components";
import createContent from "../../scripts/create_content";
import SlideoutModal from "../../slideout_modal";
import Preview from "./posteditor/Preview.vue";
import create_content from "../../scripts/create_content";
import RichText from "./common/RichText";

let modalPreview, modalShortenedLinks, modalSimilarPosts;

export default {
    name: "Post",
    components: {RichText, Preview},
    props: ["post"],
    data() {
        return {
            ajaxPending: false,
            showOptions: false,
            renderPreview: false,
            renderShortenedLinks: false,
            renderGeneratedPosts: false,

            // for ai generated posts
            generatedPost: null,
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        myUserId() {
            return appConfig.userId;
        },
        account() {
            return this.post && this.post.accounts && this.post.accounts[0];
        },
        canApprove() {
            return this.post.can_approve;
        },
        canEdit() {
            return this.post.can_edit;
        },
        attachmentsDeleted(){
            return this.post.attachments.some(a => a.deleted);
        },
        link(){
            const regex = /\b((?:https?:\/\/|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'\".,<>?«»“”‘’]))/gi;
            const matches = (this.post.content || "").match(regex) || [];
            if (matches.length) {
                if (!matches[0].startsWith("http")) return "http://" + matches[0];
                else return matches[0];
            }
            return null;
        },
        permalink() {
            if (!this.post.published) return null;
            return this.post.permalink;
        },
        highlightConfig(){
            const account = this.account;
            if(account.type === "twitter.profile"){
                return [
                    "hashtag",
                    "mention",
                    "link",
                ];
            } else if(account.type.includes("linkedin")){
                return [
                    "hashtag",
                    "link",
                ];
            } else if(account.type.includes("facebook")){
                return [
                    "hashtag",
                    "link",
                ];
            } else if(account.type === "google.location"){
                return []; // nothing to highlight
            } else if(account.type.includes("instagram")){
                return [
                    "hashtag",
                    "mention",
                ];
            }
            return [
                "hashtag",
                "mention",
                "link",
            ];
        }
    },
    methods: {

        getIconForAccount,
        getColorClassForAccount,
        truncate: _.truncate,
        getTitleFromStr(str) {
            return _.startCase(_.camelCase(str));
        },
        openAttachments(attachments) {
            if (this.post.published && this.attachmentsDeleted) return;
            this.$emit("set-attachments", attachments);
            let _this = this;
            $("#attachmentModal")
                .modal("show")
                .on("hidden.bs.modal", function() {
                    _this.$emit("set-attachments", []);
                    $(this).off("hidden.bs.modal");
                });
        },
        async previewPost(){
            if(!modalPreview){
                modalPreview = await SlideoutModal.create("Preview", {
                    class: "border-left shadow",
                    onShow: el => {
                        $(el).find(".modal-body").empty().append("<div id='previewPost' />");
                        this.renderPreview = true;
                    },
                    onHidden: () => {
                        this.renderPreview = false;
                        modalPreview.destroy();
                        modalPreview = null;
                    }
                });
            }
            await modalPreview.open();
        },
        async showShortenedLinks(){
            if(!modalShortenedLinks){
                modalShortenedLinks = await SlideoutModal.create("Shortened Links", {
                    class: "border-left shadow",
                    onShow: el => {
                        $(el).find(".modal-body").empty().append("<div id='shortenedLinks' />");
                        this.renderShortenedLinks = true;
                    },
                    onHidden: () => {
                        this.renderShortenedLinks = false;
                        modalShortenedLinks.destroy();
                        modalShortenedLinks = null;
                    }
                });
            }
            await modalShortenedLinks.open();
        },
        async generateSimilarPosts(){
            if(!modalSimilarPosts){
                modalSimilarPosts = await SlideoutModal.create(`<i class="ph ph-magic-wand"></i> Generate Post using AI`, {
                    class: "border-left shadow",
                    onShow: el => {
                        $(el).find(".modal-body").empty().append("<div id='generatedPosts' />");
                        this.renderGeneratedPosts = true;
                        this.$nextTick(()=>{
                            this.generatePost();
                        });
                    },
                    onHidden: () => {
                        this.renderGeneratedPosts = false;
                        modalSimilarPosts.destroy();
                        modalSimilarPosts = null;
                    }
                });
            }
            await modalSimilarPosts.open();
        },
        async generatePost(){
            if(this.ajaxPending){
                return;
            }
            this.ajaxPending = true;

            try {
                const { data } = await axios.post("/api/v1/generated_content/generate_by_post", {
                    post_id: this.post.id,
                });

                this.generatedPost = data.content;

                // all done
                this.ajaxPending = false;

            } catch (e) {
                axiosErrorHandler(e);
                this.ajaxPending = false;
            }
        },
        async createPost(text){
            modalSimilarPosts.close();
            (await create_content.getPostComposer()).content = text;
            await create_content.newPost();
        },
        async moveToDrafts() {
            if (!this.canEdit || this.post.draft) return;
            if (this.ajaxPending) return;
            const $post = $(this.$el);
            $post.spin();
            this.ajaxPending = true;
            try {
                const res = await axios.patch("/app/publish/posts/" + this.post.id, {
                    draft: true
                });
                if (res.data.success) {
                    this.$emit("updated", _.extend({}, this.post, res.data.post, { _draft: true }));
                }
            } catch (e) {
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
            }
            $post.spin(false);
            this.ajaxPending = false;
        },
        async postNow() {
            if (!this.canEdit) return;
            if (this.ajaxPending) return;
            const $post = $(this.$el);
            $post.spin();
            this.ajaxPending = true;
            try {
                const res = await axios.patch("/app/publish/posts/" + this.post.id, {
                    publish_now: true
                });
                if (res.data.success) {
                    this.$emit("updated", _.extend({}, this.post, res.data.post));
                }
            } catch (e) {
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
            }
            $post.spin(false);
            this.ajaxPending = false;
        },
        deletePost(e) {
            if (this.ajaxPending) return;

            alertify.delete("Are you sure you want to delete this post?", async () => {
                const $post = $(this.$el);
                this.ajaxPending = true;
                $post.spin();
                try {
                    await axios.delete("/app/publish/posts/" + this.post.id);
                    this.$emit("updated", { ...this.post, _deleted: true });
                } catch (e) {
                    axiosErrorHandler(e);
                }
                $post.spin(false);
                this.ajaxPending = false;
            });
        },
        editTime(reset_result) {
            if (!this.canEdit) return;
            if (this.ajaxPending) return;
            let oldTime, now = this.$momentUTC();
            if(this.post.publish_at) {
                oldTime = this.$momentUTC(this.post.publish_at);
                if(oldTime < now){
                    oldTime = now;
                }
            } else {
                oldTime = now;
            }
            let api = $("#edit_post_publish_at").data("datetimepicker");
            const _this = this;

            if (!api) return;
            api.date(oldTime);
            $("#edit_time_popup")
                .modal("show")
                .on("hidden.bs.modal", async function() {
                    const $post = $(_this.$el);
                    $(this).off("hidden.bs.modal");
                    if (oldTime.format("x") !== api.date().format("x")) {
                        // save new time
                        $post.spin();
                        _this.ajaxPending = true;
                        try {
                            const res = await axios.patch("/app/publish/posts/" + _this.post.id, {
                                draft: reset_result ? false : _this.post.draft,
                                publish_at: api.date().format("YYYY-MM-DD HH:mm:ss"),
                                reset_result
                            });
                            if (res.data.success) {
                                let new_data = {
                                    publish_at: api
                                        .date()
                                        .utc()
                                        .format("YYYY-MM-DD HH:mm:ss")
                                };
                                _.extend(new_data, res.data.post);
                                if (reset_result) new_data.error = null;
                                _this.$emit("updated", _.extend({}, _this.post, new_data));
                            }
                        } catch (e) {
                            (() => {
                                if (e.response && e.response.data) {
                                    const errors = e.response.data.errors;
                                    if (errors) {
                                        Object.keys(errors).forEach(k => {
                                            alertify.error(errors[k].join(" \n"));
                                        });
                                        return;
                                    }
                                    if (e.response.data && e.response.data.message) {
                                        return alertify.error(e.response.data.message);
                                    }
                                }
                                alertify.error(e.message || "An error occurred.");
                            })();
                        }
                        $post.spin(false);
                        _this.ajaxPending = false;
                    }
                });
                $("#edit_post_publish_at").find('.datepicker-days')
                    .off('click', '.day.disabled') // Remove any previously bound event
                    .on('click', '.day.disabled', function() {
                        alertify.error('Sorry, you cannot select date in the past!');
                    });
        },
        async approve(approved = false) {
            if (!this.canApprove) {
                return;
            }
            const process = async data => {
                if (this.ajaxPending) return;
                const $post = $(this.$el);
                $post.spin();
                this.ajaxPending = true;
                try {
                    const res = await axios.patch(
                        "/app/publish/posts/" + this.post.id,
                        approved
                            ? {
                                  approved: true
                              }
                            : {
                                  approved: false,
                                  reject_reason: data.reason
                              }
                    );
                    if (res.data.success) {
                        this.$emit("updated", _.extend({}, this.post, res.data.post));
                    }
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                }
                $post.spin(false);
                this.ajaxPending = false;
            };
            if (!approved) {
                // ask for reason
                alertify.prompt("Enter the reason for rejection of this post.", "", async (evt, value) => {
                    await process({
                        reason: value
                    });
                });
            } else {
                await process();
            }
        },
        async onDuplicatePost(){
            await createContent.duplicatePost(this.post);
            // Emit the republish event after duplicating the post
            this.$emit('republish');
        },
        async onEditPost() {
            if (this.canEdit) {
                await createContent.editPost(this.post, post => this.$emit("updated", post));
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.post-container {
    margin-bottom: 10px;
    border-color: rgb(238, 238, 238);
    border-color: #E3E6EB;

    .post-accounts {
        padding-bottom: 12px;
        margin-top: 0px;

        .profile-pic1 {
            display: inline-block;
            border-radius: 50%;
            height: 40px;
            width: 40px;
        }

    }

    .post-body .post-image {
        max-width: 100px;
        width: 100px;
        height: 100px;
        object-fit: cover;
    }
    .post-text {
        word-break: break-word;
        padding-bottom: 10px;
    }
    .cursor-default {
        cursor: default;
    }
    .edit-post-textarea {
        resize: none;
        border: none;
        box-shadow: none;
        min-height: 64px;
        padding: 0;
        //margin: -20px -10px;
        //margin-top: -10px;
    }

    .story-badge {
        color: white;
        background-color: #6699fd;
    }
    .network-icon {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 23px;
        right: -1px;
    }
    @media (min-width: 770px) {
        .added-by{
            visibility: hidden;
        }        
    }
    
    &:hover {
        .added-by{
            visibility: visible;
            color: #657085;
        }
    }
    .icon-size {
        width: 20px;
        height: 20px;
    }
}
.edit-time-icon{
    margin-right: 6px;
}
.badge{
    padding: 2px 6px !important;
    font-size: 14px !important;
}
.text-warning{
    color: #B56202 !important;
}
.post-insights{
    color: #353F54;
}
.document-link{
    width: 100px;
    height: 100px;
    word-break: break-all;
}
</style>
