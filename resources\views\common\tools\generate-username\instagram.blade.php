@extends('layout.default')
@php
    $title = 'Free Instagram Username Generator';
    $description = 'Find your ideal Instagram handle with our free Instagram username generator. Create a unique and memorable Instagram presence.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/generate-username/instagram';

    $form = new \App\Generate\DynamicForm\Form('username_generator');
    $fields = $form->getFields();

    $fields = array_filter($fields, function ($field) {
        return $field['id'] !== 'network';
    });
@endphp

@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush

@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Usernames',
                    type: 'username_generator',
                    splitDataBy:',',
                    inputData:{
                        network: 'instagram'
                    },
                });
            });
        });
    </script>
@endpush

@section('content')
    <main class="main-content">
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12 text-center mb-5">
                        <h2 class="d-md-block d-none display-4 mb-3 pb-0">
                            Free Instagram Username ideas Generator
                        </h2>
                        <h4 class="d-md-none display-4 mb-3 pb-0">
                            Free Instagram Username ideas Generator
                        </h4>
                        <p class="mb-6">
                            Unique Instagram username ideas in seconds with our AI Instagram username generator
                        </p>
                    </div>
                    <div class="col-md-8 mx-auto w-100 mh-350">
                        <div id="generate-tool-output">
                            <div class="text-center">
                                <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

         <section>    
            <div class="container-fluid d-md-block d-none p-0 pt-7">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username.webp" alt="Generate username">
            </div>
            <div class="container-fluid d-md-none d-block p-0 pt-6">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username-mobile.webp" alt="Generate username">
            </div>
        </section>
        
        <section class="section rounded-xl p-xxl-5 px-20">
            <div class="container d-flex flex-column align-items-center text-center bg-primary text-white rounded-xl rounded-4 p-6 p-md-5 p-lg-8">
                <h2 class="display-2 fw-bold mb-6">Got your perfect username,<br>why stop there?</h2>
                <p class="lead-2 mb-6">Run your whole Social Media with Automations.</p>
                <a href="#" class="lead-2 btn bg-white btn-light text-primary d-flex align-items-center gap-2 px-4 py-2 ">
                    Automate your socials →
                </a>
            </div>
        </section>

       <section class="section">
            <div class="container px-20">
                <div class="row align-items-stretch">
                    <div class="col-12 px-20">
                        <h4 class="d-md-block d-none display-3 mb-2">Create Your Perfect Insta Handle in Seconds</h4>
                        <h2 class="d-md-none display-6 mb-4 pb-1">Create Your Perfect Insta Handle in Seconds</h2>
                        <p class="pt-1 mb-6">
                            No more frustration or endless searches. Enter a few details and instantly get personalized suggestions with our free Instagram username generator. It’s simple, fast, and fun! Here’s how it works:
                        </p>
                    </div>
                    <div class="col-md-4 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">1. Drop Your Keywords</h6>
                                <p class="mb-5">What’s your vibe? Are you into fashion, fitness, or art? Separate words like "style, bold, creative" with commas for an Instagram username generator with name or niche flair.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">2. Add a Short Description</h6>
                                <p class="mb-0">Tell us your goal. Looking for a cute Instagram username generator, a random username generator for Instagram, or something professional?</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">3. Pick Your Tone & Category</h6>
                                <p class="mb-0">For rare Instagram username generator results, choose a tone—formal, chill, or playful—and a category like business, lifestyle, or gaming.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">4. Customize Your Username</h6>
                                <p class="mb-0">Set your length (8 characters, 10, or more) and add symbols with our Instagram username generator with symbols—or go aesthetic with our aesthetic Instagram username generator.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">5. Hit Generate & Repeat</h6>
                                <p class="mb-0">In seconds, our random Instagram username generator delivers 12 Instagram username ideas generator suggestions. Didn't like what you got? Generate again for more!</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-5">
                        <div class="card bg-light rounded-xl h-100 d-flex flex-column">
                            <div class="pt-md-6 pb-md-6 pt-5 pb-6 px-5 d-flex flex-column align-items-center justify-content-between">
                                <h6 class="display-5">Why don't you try for yourself?</h6>
                                <a href="#top" class="btn btn-primary mt-6">Generated Usernames</a>
                            </div>
                        </div>
                    </div>                                           
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="text-left">
                    <h4 class="display-4 text-left mb-4">
                        What's the Best Instagram Username Ideas Generator?
                    </h4>
                    <p class="pt-1 mb-0">
                        SocialBu's Instagram Username ideas Generator creates fresh, memorable usernames fast for influencers, gamers, and businesses.
                    </p>
                </div>
                <div class="mt-md-8 mt-4 pt-md-0 pt-1">
                    <div class="row">
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-primary-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Enter Keywords</h6>
                                    <p class="mb-0">Add words, describe your vibe.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-success-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Choose Style</h6>
                                    <p class="mb-0">Select tone and category easily.</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-danger-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Customize Options</h6>
                                    <p class="mb-0">Set length and characters quickly.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-warning-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Pick Favorite</h6>
                                    <p class="mb-0">Choose and use your handle.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        @include('common.internal.cta-image')
        
        <section>
            <div class="container">
                <div class="row gap-y justify-content-center">
                    <div class="col-md-9">
                        <h6 class="d-md-block d-none display-4 mb-3 mx-3">FAQs</h6>
                        <h2 class="d-md-none display-6 mb-4">FAQs</h2>
                        <div class="accordion accordion-arrow-right border border-light rounded-2xl px-5 py-2" id="frequent-questions">
                            @foreach([
                            ['id' => '1', 'question' => 'What is an Instagram username generator?', 'answer' => 'It’s a tool like our free Instagram username generator that creates unique, personalized handles based on your input. This is perfect for standing out on Instagram.'],
                            ['id' => '2', 'question' => 'Where can I find free Instagram usernames?', 'answer' => 'Right here! Our free Instagram username generator offers unlimited ideas to claim your perfect handle.'],
                            ['id' => '3', 'question' => 'How do I choose the best Instagram username?', 'answer' => 'Use our Instagram username ideas generator. Input keywords, choose a tone, and customize to match your style or brand.'],
                            ['id' => '4', 'question' => 'Can I check if an Instagram username is available?', 'answer' => 'Our random Instagram username generator gives fresh ideas, but you’ll need to check availability directly on Instagram. No free Instagram username checker here—just creative inspiration!'],
                            ['id' => '5', 'question' => 'Does this tool include a free Instagram username search or lookup?', 'answer' => 'We focus on generating ideas, not searching. Use our free Instagram username generator and verify your favorites on Instagram.'],
                            ['id' => '6', 'question' => 'How can I create a professional Instagram username?', 'answer' => 'Select the “Business” category and a formal tone in our Instagram username generator for sleek, brand-friendly options.'],
                            ['id' => '7', 'question' => 'What should I do if my desired Instagram username is taken?', 'answer' => 'Tweak it with symbols or numbers using our Instagram username generator with symbols, or generate new rare Instagram username generator ideas.'],
                            ['id' => '8', 'question' => 'Can businesses use an Instagram username generator?', 'answer' => 'Yes! Our tool crafts professional, brandable handles with the cool username generator for Instagram tailored to your business niche.'],
                            ['id' => '9', 'question' => 'How do I make my Instagram username more brandable?', 'answer' => 'Add niche keywords and a formal tone in our AI username generator for Instagram for memorable, brand-aligned results.'],
                            ['id' => '10', 'question' => 'Is it possible to change my Instagram username later?', 'answer' => 'Absolutely, Instagram lets you update your handle anytime, so experiment with our random username generator for Instagram freely.'],
                            ['id' => '11', 'question' => 'Are there any rules for Instagram usernames?', 'answer' => 'Yes, they must be under 30 characters, with no spaces—our Instagram username generator keeps it compliant.'],
                            ['id' => '12', 'question' => 'Can I use an Instagram username generator for other platforms?', 'answer' => 'Definitely! The aesthetic Instagram username generator ideas work across socials, just tweak as needed.'],
                            ['id' => '13', 'question' => 'Does a good Instagram username help with SEO?', 'answer' => 'A clear, niche-specific handle from our Instagram username generator with a name can boost discoverability.'],
                            ['id' => '14', 'question' => 'How does this Instagram username generator work?', 'answer' => 'Our AI username generator for Instagram uses your keywords, tone, and preferences to craft custom handles in seconds.'],
                            ['id' => '15', 'question' => 'Can I generate Instagram usernames based on my niche?', 'answer' => 'Yes! Add niche keywords (e.g., "travel, food") for a random Instagram username generator tailored to your passion.'],
                            ['id' => '16', 'question' => 'Is this Instagram username generator free?', 'answer' => 'Totally! Our free Instagram username generator offers unlimited cute Instagram username generator ideas at no cost.'],
                            ['id' => '17', 'question' => 'How can I generate aesthetic Instagram usernames?', 'answer' => 'Enable symbols and pick a playful tone in our aesthetic Instagram username generator for stylish, eye-catching results.'],
                            ['id' => '18', 'question' => 'What are some trending Instagram username ideas?', 'answer' => 'Try trendy keywords like "vibe, glow, chic" in our cool username generator for Instagram for fresh, hot handles.'],
                            ['id' => '19', 'question' => 'How do I make my Instagram username stand out?', 'answer' => 'Mix symbols, unique keywords, and a bold tone with our Instagram username generator with symbols for a handle that pops.'],
                            ] as $index => $faq)
                            <div class="card shadow-none mb-2">
                                <div class="card-header px-0 pt-5 mb-1 {{ !$loop->first && !$loop->last ? 'border-bottom pb-5' : 'pb-2' }}" data-toggle="collapse" href="#collapse-{{ $faq['id'] }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    <div class="card-title d-flex align-items-center justify-content-between">
                                        <h6 class="mb-0 display-5 font-weight-600">{{ $faq['question'] }}</h6>
                                        <i class="ph {{ $index === 0 ? 'ph-caret-up' : 'ph-caret-down' }} ph-md"></i>
                                    </div>
                                </div>
                                <div id="collapse-{{ $faq['id'] }}" class="collapse {{ $index === 0 ? 'show' : '' }}" data-parent="#frequent-questions">
                                    <div class="card-body pb-3 px-0 pt-0 {{  !$loop->last ? 'border-bottom' : '' }}">
                                        <p class="mb-3">{!! $faq['answer'] !!}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                    </div>
                </div>
            </div>
        </section>


    </main>

@endsection
