<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class QuoteImageGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'quote',
                'label' => 'Describe your quote',
                'placeholder' => '',
                'type' => 'textarea',
                'rules' => 'required|string|max:500',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'instruction',
                'label' => 'Add any instructions',
                'placeholder' => '',
                'type' => 'text',
                'rules' => 'string|max:400',
                'class' => 'col-md-6',
            ],
        ];
    }

    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'POST',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json',
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "Generate a single, direct, and detailed prompt (max 20 words) for an AI image generation model to create a quote image.",

                                    "Quote Text: Use the exact text: \"{{form.quote}}\". Do not change, rewrite, paraphrase, or modify the text in any way. The quote must be well positioned based on the text length and the background (centered, bottom, top, or any position).",

                                    "If you are familiar with the author of the quote, add the author's name after the quote otherwise don't add author name.",

                                    "Typography & Layout: Ensure the entire quote is fully visible, with no truncation or cropping. Adjust font size and line spacing as needed. Use high-contrast colors for maximum readability.",

                                    "Visual Design: Suggest a background that is highly relevant to the quote and also goes well with the text and its readability. The background should visually enhance and match the mood or meaning of the quote.",

                                    "{% if form.instruction %} Additional details: {{form.instruction}} {% endif %}",

                                    "Output: Respond with a single, cohesive sentence or paragraph that combines all instructions above into a direct prompt for the image generation model."
                                ])),
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Prompt:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ]),
                ],
            ],
            [
                'step' => 'http',
                'input' => [
                    'method' => 'POST',
                    'url' => 'https://api.openai.com/v1/images/generations',
                    'type' => 'json',
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'timeout' => 1000,
                    'data' => array_filter([
                        'model' => 'gpt-image-1',
                        'prompt' => '{{step1.data.choices.0.message.content}}.',
                        'n' => 1,
                        'size' => '1536x1024',
                        'quality' => 'medium',
                        'user' => user() ? (string) user()->id : null,
                    ]),
                ],
            ],
        ];
    }

    public function outputData(): array
    {
        return [
            'image_url' => 'data:image/png;base64,{{step2.data.data.0.b64_json}}',
        ];
    }

    public function outputComponents(): array
    {
        return [];
    }
}