@extends('layout.default')
@php

    $title = 'Free TikTok Caption Generator - Create Viral Captions with AI';
    $description = 'Generate catchy, engaging TikTok captions that boost views and drive engagement. No sign-up required, instant results with our AI-powered caption generator.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/tiktok-caption-generator';
    $form = new \App\Generate\DynamicForm\Form('tiktok_caption_generator');
    $fields = $form->getFields();
    $fields = array_filter($fields, function ($field) {
        return $field['id'] !== 'network';
    });
@endphp
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />
    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush

@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Caption',
                    type: 'tiktok_caption_generator',
                    splitDataBy:'---END_CAPTION---',
                });
            });
        });
        
    </script>
@endpush
@section('content')
    <header class="header">
        <div class="container">
            <div class="row align-items-center h-100">
                <div class="col-md-12 text-center">
                    <h4 class="d-md-block d-none display-4 mb-3 pb-0">
                        Free TikTok Caption Generator 
                    </h4>
                    <h2 class="d-md-none display-6">
                        Free TikTok Caption Generator
                    </h2>
                    <p class="mb-6 pb-2">Generate 2 months of engaging TikTok captions in 2 minutes. No signup required.</p>
                </div>
                <div class="col-md-8 mx-auto w-100 mh-350">
                    <div id="generate-tool-output">
                        <div class="text-center">
                            <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="section">
            <div class="container">
                <div class="row align-items-center h-100">
                    <div class="col-md-6 col-12 order-md-first">
                        <div class="d-flex align-items-center">
                            <div>
                                <h2 class="display-3 mb-5">Transform Your TikTok <br> Content with AI-Generated Captions</h2>
                                <p>
                                    Creating the perfect TikTok caption shouldn't take hours of brainstorming. Whether you're struggling with writer\'s block or need fresh caption ideas that resonate with your audience, our free TikTok caption generator has you covered.
                                </p>
                                <p class="mt-6">
                                    This AI-powered tool crafts attention-grabbing captions in seconds, complete with relevant hashtags and emojis. Describe your content, choose your preferred tone, and watch as our AI creates multiple caption variations that match your brand\'s voice and style.     
                                </p>
                                <p class="mt-6">
                                    From casual and humorous to formal and inspiring, generate captions that connect with your audience and increase your chances of going viral. Best of all, it's completely free with no account creation required.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-12 order-md-second order-first mb-md-0 mb-6">
                        <img class="lozad" src="/images/post-generator.webp" alt="Tiktok caption generator">
                    </div>
                </div>
            </div>
        </section>
        <section class="section" id="more_features" style="display:none;">
            <div class="container px-20">
                <div class="row align-items-stretch">
                    <div class="col-12 px-20">
                        <h4 class="d-md-block d-none display-3 mb-6 pb-2">How to Generate Perfect TikTok Captions</h4>
                        <h2 class="d-md-none display-6 mb-4 pb-1">How to Generate Perfect TikTok Captions</h2>
                    </div>
                    <div class="col-md-4 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">1. Describe Your Content </h6>
                                <p class="mb-5">Briefly describe your TikTok video or enter a prompt about your post topic. Keep it simple but specific enough for our AI to understand your content theme.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">2. Select Your Tone</h6>
                                <p class="mb-0">Choose the writing style that matches your brand voice from our dropdown menu. Whether you want to be funny, professional, or inspiring, we have the right tone for you.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">3. Choose Caption Length</h6>
                                <p class="mb-0">Decide if you want shorter, snappy captions for quick impact or longer captions for detailed storytelling and deeper audience connection.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">4. Set Your Preferences</h6>
                                <p class="mb-0">Toggle emojis and hashtags on or off based on your content strategy. Select how many caption variations you want generated simultaneously.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">5. Generate and Copy</h6>
                                <p class="mb-0">Click "Generate Post" to instantly create your captions. Review the options, select your favorite, and copy it with one click to use in your TikTok post.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">6. Reset for New Ideas</h6>
                                <p class="mb-0">Use the "Start Over" button to generate fresh caption ideas whenever you need different approaches or want to explore new creative directions.</p>
                            </div>
                        </div>
                    </div>                                          
                </div>
            </div>
        </section>
        <section class="section">
            <div class="container-fluid bg-darker">
            <section class="section">
                <div class="container">
                    <div class="row">
                        <div class="col-md-12">
                            <h2 class="text-white display-3 mb-6 pb-2">Key Features That Make Caption <br class="d-md-block d-none"> Creation Effortless</h2>
                        </div>
                        <div class="col-md-4 col-12 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-sparkle border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Smart Content Analysis</h3>
                                    <p>Enter a brief description of your TikTok video, and our AI understands the context to create relevant, engaging captions that complement your content perfectly.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-list-dashes border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Multiple Tone Options</h3>
                                    <p>Select from a range of writing styles, including humorous, inspirational, casual, formal, witty, enthusiastic, and more, to match your brand\'s personality and audience preferences.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-md-0 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-pencil-simple-line border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Customizable Caption Length</h3>
                                    <p>Generate shorter, punchy captions for quick engagement or longer, detailed descriptions for storytelling content, tailored to your specific needs.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-list-checks border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Multiple Caption Variations</h3>
                                    <p>Create up to 5 different caption versions at once, giving you options to test what resonates best with your audience and maximizes engagement.</p>
                                    <br>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-smiley border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Emoji Integration</h3>
                                    <p>Toggle emoji inclusion on or off to add visual appeal and personality to your captions, making them more eye-catching and engaging for TikTok users.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-hash-straight border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Hashtag Optimization</h3>
                                    <p>Automatically include relevant hashtags in your captions to improve discoverability and help your content reach the right audience on TikTok\'s algorithm.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-md-0 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-copy border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">One-Click Copy Function</h3>
                                    <p>Copy generated captions instantly with a single click, making it easy to paste directly into your TikTok posts without any additional formatting needed.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-12 mb-md-0 mb-5">
                            <div class="card bg-transparent-4 text-white rounded-xl">
                                <div class="card-body">
                                    <i class="ph ph-repeat border-dark-1 rounded-lg p-3"></i>
                                    <h3 class="display-5 mt-6 pt-2 mb-4">Reset and Regenerate</h3>
                                    <p>Start fresh anytime with the reset button to generate completely new caption ideas when you need different approaches or creative directions.</p>
                                    <br>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        </section>
        <section class="section bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="display-3">Why Choose Our AI TikTok <br class="d-md-block d-none"> Caption Generator</h2>
                    </div>
                    <div class="col-md-12 bg-white rounded-lg mt-6">
                        <div class="row my-2 py-20">
                            <div class="col-md-4 col-12 px-0">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div class="border-right-dashed">
                                            <i class="ph ph-clock ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Save Hours of Creative Work</h5>
                                            <p class="mr-6 mb-0">Skip the time-consuming brainstorming process and generate engaging captions in seconds. Focus your energy on creating amazing video content while our AI handles the caption writing.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-12 px-0">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div class="border-right-dashed">
                                            <i class="ph ph-star ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Maintain Consistent Brand Voice </h5>
                                            <p class="mr-6 mb-0">Keep your messaging on-brand with customizable tone options that ensure every caption reflects your unique personality and connects authentically with your audience.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-12 px-0">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div class="border-right-dashed">
                                            <i class="ph ph-rocket-launch ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Boost Engagement Rates</h5>
                                            <p class="mr-6 mb-0">Well-crafted captions encourage comments, likes, and shares. Our AI creates captions designed to spark conversations and increase overall engagement on your posts.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-12 px-0 mt-4">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div class="border-right-dashed">
                                            <i class="ph ph-trend-up ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Stay Trend-Relevant</h5>
                                            <p class="mr-6 mb-0">Generate captions that feel current and relevant to TikTok\'s fast-paced environment, helping your content stay competitive in the ever-changing social media landscape.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-12 px-0 mt-4">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div class="border-right-dashed">
                                            <i class="ph ph-magic-wand ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Overcome Creative Blocks</h5>
                                            <p class="mr-6 mb-0">Never struggle with blank screens again. Our caption generator provides instant inspiration and creative ideas whenever you need fresh content approaches.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-12 px-0 mt-4">
                                <div class="card">
                                    <div class="card-body pr-0">
                                        <div>
                                            <i class="ph ph-list-magnifying-glass ph-3xl text-primary"></i>
                                            <h5 class="mt-5 mb-3">Optimize for Discoverability</h5>
                                            <p class="mr-6 mb-0">Built-in hashtag suggestions help your content reach the right audience and improve visibility on TikTok\'s "For You" page algorithm.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="display-3 mb-0">Perfect for Every Tiktok Marketing Need</h2>
                    </div>
                    <div class="col-md-12">
                        @include('common.internal.testimonials-block')
                    </div>
                </div>
            </div>
        </section>
        <section class="section">
            @include('common.internal.explore-socialbu')
        </section>
        <section class="section">
            <div class="container">
                <header class="section-header mb-6 pb-2">
                    <h2 class="display-3 mb-0">Frequently Asked Questions</h2>
                </header>
                <div class="row gap-y justify-content-center">
                    <div class="col-md-9">
                        <div class="accordion accordion-arrow-right border border-light rounded-2xl px-5 py-2" id="frequent-questions"> 
                            @foreach([
                            ['id' => '1', 'question' => 'What is an AI TikTok caption Generator? ', 'answer' => 'An AI TikTok caption generator is a smart tool that uses artificial intelligence to create engaging, relevant captions for your TikTok videos. It analyzes your content description and generates multiple caption options with hashtags and emojis.'],
                            ['id' => '2', 'question' => 'How to generate TikTok captions?', 'answer' => 'Simply describe your video content, select your preferred tone and length, choose whether to include emojis and hashtags, then click generate. Our AI creates multiple caption variations instantly for you to choose from.'],
                            ['id' => '3', 'question' => 'What caption to use for TikTok? ', 'answer' => 'The best TikTok captions are concise, engaging, and match your content\'s mood. Use captions that encourage interaction, include relevant hashtags, and reflect your brand\'s personality while complementing your video content.'],
                            ['id' => '4', 'question' => 'How does the AI TikTok caption generator work?', 'answer' => 'Our generator uses advanced AI technology to analyze your content description and preferences, then creates captions that match your specified tone, length, and style requirements with relevant hashtags and emojis.'],
                            ['id' => '5', 'question' => 'Can I generate multiple versions of captions?', 'answer' => 'Yes, you can generate up to 5 different caption variations simultaneously. This gives you multiple options to test and choose the one that best fits your content and audience preferences.'],
                            ['id' => '6', 'question' => 'Can I edit the captions generated for TikTok?', 'answer' => 'Absolutely. All generated captions can be copied and edited to perfectly match your needs. Use our output as a starting point and customize it further to align with your specific brand voice.'],
                            ['id' => '7', 'question' => 'What types of captions can it generate?', 'answer' => 'Our generator creates various caption styles including humorous, inspirational, casual, formal, witty, enthusiastic, empathetic, and convincing captions to match different content types and brand personalities.'],
                            ['id' => '8', 'question' => 'How to Use the Free TikTok Caption Generator?', 'answer' => 'Enter your content description, select your preferred tone and settings, choose the number of variations, toggle emojis and hashtags as needed, then click generate. Copy your favorite caption with one click.'],
                            ['id' => '9', 'question' => 'What are the best practices for generating AI TikTok captions?', 'answer' => 'Keep descriptions clear and specific, choose tones that match your brand, use relevant hashtags, include emojis for visual appeal, test different caption lengths, and always review generated content before posting.'],

                            ] as $index => $faq)
                            <div class="card shadow-none mb-2">
                                <div class="card-header px-0 pt-5 mb-1 {{ !$loop->first && !$loop->last ? 'border-bottom pb-5' : 'pb-2' }}" data-toggle="collapse" href="#collapse-{{ $faq['id'] }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    <div class="card-title d-flex align-items-center justify-content-between">
                                        <h6 class="mb-0 display-6 font-weight-500">{{ $faq['question'] }}</h6>
                                        <i class="ph {{ $index === 0 ? 'ph-caret-up' : 'ph-caret-down' }} ph-md"></i>
                                    </div>
                                </div>
                                <div id="collapse-{{ $faq['id'] }}" class="collapse {{ $index === 0 ? 'show' : '' }}" data-parent="#frequent-questions">
                                    <div class="card-body pb-3 px-0 pt-0 {{  !$loop->last ? 'border-bottom' : '' }}">
                                        <p class="mb-3">{!! $faq['answer'] !!}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection