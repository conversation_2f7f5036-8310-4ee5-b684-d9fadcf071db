
<div id="add_mastodon_modal" class="modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ph ph-mastodon-logo social-icon active mastodon fa-lg"></i> Add Mastodon Account
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <!-- form with one input for domain -->
            <form method="get" action="<?php echo e(route('accounts.auth', ['provider' => 'mastodon'])); ?>">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="mastodon_domain">Mastodon domain</label>
                                <div class="input-group">
                                    <input type="text" name="domain" class="form-control" id="mastodon_domain" aria-describedby="mastodonHelp" placeholder="Enter Mastodon domain" required />
                                    <div class="input-group-append">
                                    <span class="input-group-text">
                                        <!-- dropdown -->
                                        <div class="dropdown">
                                            <button type="button" class="btn btn-sm btn-link text-muted" data-toggle="dropdown">
                                                <i class="fa fa-angle-down"></i>
                                            </button>
                                            <div class="dropdown-menu">
                                                <h6 class="dropdown-header">Example Mastodon domains</h6>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.social">mastodon.social</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.world">mastodon.world</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mstdn.social">mstdn.social</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.art">mastodon.art</a>
                                            </div>
                                        </div>
                                    </span>
                                    </div>
                                </div>
                                <small id="mastodonHelp" class="form-text text-muted">
                                    For example, if your Mastodon account is <pre>@<EMAIL></pre>, then enter <strong>mastodon.social</strong> in the field above.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer text-right">
                    <button type="submit" class="btn btn-primary">Continue</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="add_youtube_modal" class="modal" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title">
                    <i class="fa fa-youtube-play social-icon active youtube fa-lg"></i> Add YouTube Channel
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col">
                        <p class="lead small mb-4">
                            You will be redirected to Google to authorize <?php echo e(config('app.name')); ?> to access your YouTube account.
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <p class="alert alert-primary mb-4">
                            <?php echo e(config('app.name')); ?>'s use and transfer to any other app of information received from Google APIs will adhere to <a target="_blank" href="https://developers.google.com/terms/api-services-user-data-policy#additional_requirements_for_specific_api_scopes" class="alert-link">Google API Services User Data Policy</a>, including the Limited Use requirements.
                        </p>
                        <div class="text-center">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'google', 'service' => 'youtube'])); ?>" class="btn btn-primary hide_modal_onclick ">
                                    <span class="d-flex align-items-center">
                                        <i class="fa fa-youtube-play fa-2x mr-2"></i> Continue
                                    </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="add_bluesky_modal" class="modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="si si-bluesky social-icon active bluesky fa-lg"></i> Add Bluesky Account
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <form method="post" action="<?php echo e(route('accounts.add_from_req', ['provider' => 'bluesky'])); ?>">
                <?php echo e(csrf_field()); ?>

                <div class="modal-body">
                    <div class="form-row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="bsky_handle">Bluesky Handle</label>
                                <input type="text" name="handle" class="form-control" id="bsky_handle" aria-describedby="bskyHelp" placeholder="you.bsky.social" required />
                                <small id="bskyHelp" class="form-text text-muted"></small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label for="bsky_pass">Bluesky App Password</label>
                                <input type="password" name="password" class="form-control" id="bsky_pass" aria-describedby="bskyPassHelp" required />
                                <small id="bskyPassHelp" class="form-text text-muted">
                                    Generate an App Password via <b>Settings > Privacy and security > App Passwords</b> in your Bluesky account.
                                </small>
                            </div>
                        </div>
                        <div class="col-12 collapse" id="bsky_service_input">
                            <div class="form-group">
                                <label for="bsky_service">Bluesky Service</label>
                                <input type="text" name="service" class="form-control" value="bsky.social" id="bsky_service" aria-describedby="bskyServiceHelp" required />
                                <small id="bskyServiceHelp" class="form-text text-muted">
                                    The service domain of your Bluesky account. Leave as <b>bsky.social</b> if you are unsure what it is.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer text-right">
                    <!-- toggle btn for service input -->
                    <button type="button" class="btn btn-link btn-sm" data-toggle="collapse" href="#bsky_service_input" role="button" aria-expanded="false" aria-controls="bsky_service_input">
                        Advanced
                    </button>
                    <button type="submit" class="btn btn-primary">Continue</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if(!empty($auth_facebook)): ?>
    <div id="fb_auth_facebook_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('accounts.select'); ?></h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('accounts.add_from_req', ['provider' => 'facebook'])); ?>" method="POST">
                        <?php echo e(csrf_field()); ?>

                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select account" data-actions-box="true" data-live-search="true">
                                <optgroup label="Facebook Pages">
                                    <?php $__currentLoopData = $auth_facebook; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'page'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e($acc['name']); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                                <optgroup label="Group">
                                    <?php $__currentLoopData = $auth_facebook; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'group'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e($acc['name']); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                                <optgroup label="Instagram Accounts">
                                    <?php $__currentLoopData = $auth_facebook; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'instagram'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e('@' . (isset($acc['username']) ? $acc['username'] : $acc['id'])); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                            </select>
                            <button type="submit" class="btn btn-primary"><?php echo app('translator')->get('generic.add'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="<?php echo e(route('accounts.add_from_req', ['provider' => 'facebook'])); ?>">
        <?php echo e(csrf_field()); ?>

        <input type="hidden" name="cancel" value="1"/>
    </form>

<?php elseif(!empty($auth_linkedin)): ?>

    <div id="linkedin_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('accounts.select'); ?></h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('accounts.add_from_req', ['provider' => 'linkedin'])); ?>" method="POST">
                        <?php echo e(csrf_field()); ?>

                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select account" data-actions-box="true" data-live-search="true">
                                <optgroup label="<?php echo app('translator')->get('generic.profile'); ?>">
                                    <?php $__currentLoopData = $auth_linkedin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'profile'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e($acc['name']); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                                <optgroup label="Organization">
                                    <?php $__currentLoopData = $auth_linkedin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'org'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e($acc['name']); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                                <optgroup label="Brand">
                                    <?php $__currentLoopData = $auth_linkedin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($acc['_type'] == 'brand'): ?>
                                            <option value="<?php echo e($acc['id']); ?>"><?php echo e($acc['name']); ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </optgroup>
                            </select>
                            <button type="submit" class="btn btn-primary"><?php echo app('translator')->get('generic.add'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="<?php echo e(route('accounts.add_from_req', ['provider' => 'linkedin'])); ?>">
        <?php echo e(csrf_field()); ?>

        <input type="hidden" name="cancel" value="1"/>
    </form>

<?php elseif(!empty($auth_google)): ?>

    <div id="google_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('accounts.select'); ?></h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('accounts.add_from_req', ['provider' => 'google'])); ?>" method="POST">
                        <?php echo e(csrf_field()); ?>

                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select accounts" data-actions-box="true" data-live-search="true">
                                <?php if(isset($auth_google['gmb'])): ?>
                                    <?php $__currentLoopData = $auth_google['gmb']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accIndex => $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <optgroup label="GMB: <?php echo e($acc['account_name']); ?>">
                                            <?php if(!empty($acc['locations'])): ?>
                                                <?php $__currentLoopData = $acc['locations']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locIndex => $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="gmb.<?php echo e($accIndex . '.locations.' . $locIndex); ?>">
                                                        <?php echo e($location['name']); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </optgroup>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                                <?php if(isset($auth_google['youtube'])): ?>
                                    <optgroup label="YouTube">
                                        <?php $__currentLoopData = $auth_google['youtube']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $channel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="youtube.<?php echo e($channel['id']); ?>"><?php echo e($channel['name']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </optgroup>
                                <?php endif; ?>
                            </select>
                            <button type="submit" class="btn btn-primary"><?php echo app('translator')->get('generic.add'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="<?php echo e(route('accounts.add_from_req', ['provider' => 'google'])); ?>">
        <?php echo e(csrf_field()); ?>

        <input type="hidden" name="cancel" value="1"/>
    </form>

<?php elseif(!empty($auth_reddit)): ?>

    <div id="reddit_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('accounts.select'); ?></h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x "></i></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('accounts.add_from_req', ['provider' => 'reddit'])); ?>" method="POST">
                        <?php echo e(csrf_field()); ?>

                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select accounts" data-actions-box="true" data-live-search="true">
                                <?php $__currentLoopData = $auth_reddit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($id); ?>"><?php echo e($acc['name']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <button type="submit" class="btn btn-primary"><?php echo app('translator')->get('generic.add'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="<?php echo e(route('accounts.add_from_req', ['provider' => 'reddit'])); ?>">
        <?php echo e(csrf_field()); ?>

        <input type="hidden" name="cancel" value="1"/>
    </form>

<?php endif; ?>

<?php $__env->startPush('footer_html'); ?>
    <script>

        // redirect to return_url if needed
        <?php if(session()->get('accounts.connect.return_url')): ?>
        document.location.replace('<?php echo e(session()->get('accounts.connect.return_url')); ?>');
        <?php endif; ?>

        // postMessage if needed
        <?php if(session()->get('accounts.connect.send_window_message')): ?>
        if(window.opener) {
            window.opener.postMessage('account_connected', '*');
        }
        <?php endif; ?>

        __loadScript("accounts", function (s) {
            <?php
                if(isset($open_add_account_modal) && $open_add_account_modal){
                    echo 's.showAddModal();';
                }
                if(isset($open_add_mastodon_modal) && $open_add_mastodon_modal){
                    echo 's.showAddMastodonModal();';
                }
                if(isset($open_add_bluesky_modal) && $open_add_bluesky_modal){
                    echo 's.showAddBlueskyModal();';
                }
                if($auth_facebook){
                    echo 's.onAuthFacebook();';
                } else if($auth_linkedin){
                    echo 's.onAuthLinkedin();';
                } else if($auth_google){
                    echo 's.onAuthGoogle();';
                } else if($auth_reddit){
                    echo 's.onAuthReddit();';
                }
            ?>
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/user/accounts/partials/connect-popups.blade.php ENDPATH**/ ?>