<?php

namespace App;

use App\Notifications\ConversationAssigned;
use App\Respond\InboxManager;
use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * App\InboxConversation
 *
 * @property int $id
 * @property int $account_id
 * @property int|null $assigned_to
 * @property int|null $social_contact_id
 * @property \Illuminate\Support\Carbon|null $closed_at
 * @property string|null $external_id
 * @property string $status
 * @property array|null $data
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Account $account
 * @property-read \App\SocialContact $contact
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\InboxConversationItem[] $items
 * @property-read int|null $items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Tag[] $tags
 * @property-read int|null $tags_count
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation available($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation query()
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereAssignedTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereClosedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereSocialContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereType($value)
 * @property \Illuminate\Support\Carbon|null $fetched_at
 * @method static \Illuminate\Database\Eloquent\Builder|InboxConversation whereFetchedAt($value)
 */
class InboxConversation extends Model
{
    use HasOptions;

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
    */
    protected $fillable = [
        'account_id', 'external_id', 'social_contact_id', 'status', 'data', 'assigned_to', 'closed_at', 'type', 'fetched_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
    */
    protected $casts = [ 
        'data' => 'array',
        'options' => 'array'
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'closed_at',
        'created_at',
        'updated_at',
        'fetched_at', // for storing the time when the conversation was last fetched - useful for polling the data
    ];

    /**
     * Get the account associated with this object.
     */
    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Get the conversation items.
    */
    public function items()
    {
        return $this->hasMany(InboxConversationItem::class);
    }

    /**
     * Get the tags associated with this conversation.
     */
    public function tags(){
        return $this->belongsToMany(Tag::class);
    }

     /**
     * Get all conversations available to user
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int|null $id
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function scopeAvailable(\Illuminate\Database\Eloquent\Builder $query, int $id = null){
        if($id){
            $user = User::find($id);
        } else {
            $user = user();
        }

        // accounts that user has
        $myAccountIds = $user->accounts()->pluck('id')->toArray();

        // get accounts of teams where user has access to inbox.manage
        $teamsWithAccess = $user->joinedTeams->filter(function(/** @var Team $team */ $team) use($user){
            return $team->hasPermission($user, 'inbox.manage');
        });
        $teamAccountIds = $teamsWithAccess->map(function(/** @var Team $team */ $team){
            return $team->accounts()->pluck('id')->toArray();
        })->flatten()->toArray();

        // make sure we have unique account ids
        $accountIds = array_unique(array_merge($myAccountIds, $teamAccountIds));

        // either it's assigned to me or it belongs to an account that I have access to
        return $query->where('assigned_to', $user->id)->orWhereIn('account_id', $accountIds);
    }

    /**
     * Get the contact associated with this object.
     */
    public function contact()
    {
        return $this->belongsTo(SocialContact::class);
    }

    public static function transform(self $conversation)
    {
        return collect([
            'id' => $conversation->id,
            'contact' => SocialContact::transform($conversation->contact),
            'latest' => InboxConversationItem::transform($conversation->items()->latest()->first()),
            'account' => Account::transform($conversation->account),
            'tags' => $conversation->tags->map(function($tag){
                return $tag->name;
            }),
        ]);
    }

    public function findItem(string $type, string $externalId)
    {
        return $this->items()
            ->where('type', $type)
            ->where('external_id', $externalId)
            ->first();
    }

    /**
     * Get the conversation by external id
     *
     * @param string $externalId
     * @param int|null $accountId
     * @return InboxConversation|null
     */
    public static function findConversation(string $externalId, int $accountId = null): ?InboxConversation
    {
        $query = self::query();

        if($accountId){
            $query->where('account_id', $accountId);
        }

        return $query->where('external_id', $externalId)->first();
    }

    /**
     * Get the conversation from the data (if it doesn't exist, create it)
     * @param array{external_id: string, type: string, status?: string, data?: array, options?: array, timestamp?: int} $data
     * @param array{external_id: string, name: string, type: string, profile_pic?: string, options?: object} $sender
     * @throws \Exception
     */
    public static function findOrCreateConversation(array $data, array $sender, Account $account): InboxConversation
    {
        if (SocialContact::shouldIgnore($sender['type'], $sender['external_id'], $account->id)) {
            throw new \Exception('Sender is blocked or spam');
        }

        // we need to create a conversation if needed
        $conversation = InboxConversation::findConversation($data['external_id'], $account->id);

        if(!$conversation){
            $conversation = new InboxConversation();
            $conversation->account_id = $account->id;
            $conversation->external_id = $data['external_id'];

            if(isset($data['status'])){

                if(!in_array($data['status'], ['open', 'closed', 'pending'])){
                    throw new \Exception('Invalid conversation status: ' . $data['status']);
                }

                $conversation->status = $data['status'];
            } else {
                $conversation->status = 'open';
            }

            $conversation->type = $data['type'];

            // we only need to set contact id if it is not our own account
            if($sender['external_id'] !== $account->id){
                $contact = SocialContact::findOrCreateContact($account, $sender);
                $conversation->social_contact_id = $contact->id;
            }

            // if we have timestamp, use original timestamp
            if(isset($data['timestamp'])){
                $conversation->created_at = Carbon::createFromTimestamp($data['timestamp']);
            }

            $conversation->fetched_at = Carbon::now(); // set the fetched_at timestamp to now

            if(isset($data['options']) && is_array($data['options'])){
                $conversation->options = $data['options'];
            }

            if (isset($data['data']) && is_array($data['data'])){
                $conversation->data = $data['data'];
            }

            $conversation->save();
        }

        return $conversation;
    }

    /**
     * @throws \Exception
     */
    public function getApiObject(): Respond\BaseObject
    {
        $mngr = new InboxManager($this->account);
        return $mngr->getApiObject($this);
    }

    public function getTitle(): string
    {
        $title = $this->data['title'] ?? null;
        $title = $title ?: $this->data['text'] ?? null;
        $title = $title ?: $this->contact->name ?? 'Conversation #' . $this->id;
        return Str::limit($title, 50, '...');
    }

    public function updateLastItem($senderOrContact, InboxConversationItem $item = null)
    {
        $data = $this->data ?? [];

        // only update if the timestamp is greater than the current last message timestamp
        $lastTimestamp = $this->data['last_item']['timestamp'] ?? 0;

        if($item->created_at->getTimestamp() < $lastTimestamp){
            // no need
            return;
        }

        if($senderOrContact instanceof SocialContact){
            $sender = [
                'external_id' => $senderOrContact->external_id,
                'name' => $senderOrContact->name,
                'profile_pic' => $senderOrContact->profile_pic,
                'type' => $senderOrContact->type,
                'options' => [
                    'permalink' => $senderOrContact->options['permalink'] ?? null,
                ],
            ];
        } else {
            if(!is_array($senderOrContact)) {
                throw new \InvalidArgumentException('$senderOrContact must be an array');
            }
            $sender = $senderOrContact;
        }

        if(!isset($sender['external_id'])){
            throw new \InvalidArgumentException('Sender must have external_id');
        }
        if(!isset($sender['name'])){
            throw new \InvalidArgumentException('Sender must have name');
        }
        if(!isset($sender['type'])){
            throw new \InvalidArgumentException('Sender must have type');
        }

        if(!isset($sender['profile_pic'])){
            $sender['profile_pic'] = null;
        }

        if(!isset($sender['options'])){
            $sender['options'] = [];
        }

        $data['last_item'] = [
            'text' => $item->data['text'],
            'timestamp' => $item->created_at->getTimestamp(),
            'sender' => $sender,
            'contact_id' => $item->social_contact_id,
            'id' => $item->id,
        ];

        $this->data = $data;

        $this->save();
    }

    /**
     * Assign the conversation to a user, optionally checking team permissions.
     *
     * @param User $user
     * @param Team|null $team
     * @throws \Exception
     */
    public function assignTo(User $user, Team $team = null)
    {
        if ($team) {
            // Check if user has 'team.manage' permission on the team
            if (!$team->hasPermission($user, 'inbox.manage')) {
                throw new \Exception('User does not have inbox.manage permission for this team.');
            }
        }
        if ($this->assigned_to !== $user->id) {
            $this->assigned_to = $user->id;
            $this->save();


            $user->notify(new ConversationAssigned($this, user()));
        }
    }

    /**
     * @param string $status
     * @return void
     */
    public function setStatus(string $status)
    {
        // should be one of: open, closed, pending
        if(!in_array($status, ['open', 'closed', 'pending'])){
            throw new \InvalidArgumentException('Invalid conversation status: ' . $status);
        }

        if($this->status !== $status){
            $this->status = $status;

            if ($status === 'closed') {
                $this->closed_at = now();
            } else {
                $this->closed_at = null; // reset closed_at if status is not closed
            }

            $this->save();
        }
    }

    private function getOptions(){
        return $this->options;
    }

    private function setOptions($options){
        $this->options = $options;
        $this->save();
        return $this;
    }
}

