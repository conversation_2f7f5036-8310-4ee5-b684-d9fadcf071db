<?php

namespace App\Respond\Handlers;

use App\Helpers\ApiHelper;
use App\Helpers\TwitterOauthCustom;
use App\Respond\BaseInboxHandler;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Mimey\MimeTypes;

class TwitterProfile extends BaseInboxHandler
{

    private function getRapidApiClient(): \GuzzleHttp\Client
    {
        // we use the unofficial Twitter API via RapidAPI
        // because the official Twitter API has unrealistic limits
        return ApiHelper::getRapidAPI('twitter241.p.rapidapi.com');
    }

    /**
     * @return TwitterOauthCustom|mixed
     * @throws \Exception
     */
    private function getApi(): TwitterOauthCustom {
        return $this->getAccount()->getApi();
    }

    public function shouldPoll(): bool
    {
        return true;
    }

    /**
     * @throws \Exception
     */
    public function poll(): bool
    {
        $token = json_decode($this->getAccount()->token, true);
        $lastMentionTimestamp = (int) $this->getAccount()->getOption('inbox.last_mention_timestamp', 0);
        $newestTimestamp = $lastMentionTimestamp;
        $pollingGotData = false;
        $cursor = null;
        $newMentions = [];
        do {
            $response = $this->getRapidApiClient()->get('/search-v2', [
                'query' => array_filter([
                    'type' => 'Latest',
                    'count' => 200,
                    'query' => '(to:' . $token['username'] . ') filter:replies',
                    'cursor' => $cursor,
                ]),
            ]);
            $json = json_decode($response->getBody()->getContents(), true);
            $cursor = Arr::get($json, 'cursor.bottom', null);
            $entries = Arr::get($json, 'result.timeline.instructions.0.entries', []);
            $entries = array_filter($entries, function ($entry) {
                return isset($entry['content']['itemContent']['tweet_results']['result']);
            });
            if (empty($entries)) {
                break;
            }
            foreach ($entries as $entry) {
                $tweet = Arr::get($entry, 'content.itemContent.tweet_results.result');
                $tweetData = $this->extractTweetData($tweet);
                $tweetTimestamp = $tweetData['timestamp'];
                if ($tweetTimestamp <= $lastMentionTimestamp) {
                    break 2;
                }
                $newMentions[] = $tweetData;
            }
        } while ($cursor);
        // Sort by timestamp ascending (oldest first)
        usort($newMentions, function ($a, $b) {
            return $a['timestamp'] <=> $b['timestamp'];
        });
        // Process each new mention
        foreach ($newMentions as $tweetData) {
            $this->processMention($tweetData, 'open');
            $pollingGotData = true;
        }

        // Update the newest timestamp if we got new data
        if (!empty($newMentions)) {
            $newestTimestamp = max(array_column($newMentions, 'timestamp'));
        }

        if ($pollingGotData && $newestTimestamp > $lastMentionTimestamp) {
            $this->getAccount()->setOption('inbox.last_mention_timestamp', $newestTimestamp);
        }
        return $pollingGotData;
    }

    /**
     * @throws \Exception
     */
    public function backFill(): void
    {
        $token = json_decode($this->getAccount()->token, true);
        $MENTIONS_TO_FETCH = 100;
        $fetchedCount = 0;
        $matchingTweets = [];
        $cursor = null;
        do {
            $response = $this->getRapidApiClient()->get('/search-v2', [
                'query' => array_filter([
                    'type' => 'Latest',
                    'count' => 200,
                    'query' => '(to:' . $token['username'] . ') filter:replies',
                    'cursor' => $cursor,
                ]),
            ]);
            $json = json_decode($response->getBody()->getContents(), true);
            $cursor = Arr::get($json, 'cursor.bottom', null);
            $entries = Arr::get($json, 'result.timeline.instructions.0.entries', []);
            $entries = array_filter($entries, function ($entry) {
                return isset($entry['content']['itemContent']['tweet_results']['result']);
            });
            if (empty($entries)) {
                break;
            }
            foreach ($entries as $entry) {
                $tweet = Arr::get($entry, 'content.itemContent.tweet_results.result');
                $tweetId = Arr::get($tweet, 'rest_id');
                if (isset($matchingTweets[$tweetId])) {
                    continue;
                }
                $tweetData = $this->extractTweetData($tweet);
                $matchingTweets[$tweetId] = $tweetData;
                $fetchedCount++;
            }
        } while ($fetchedCount < $MENTIONS_TO_FETCH && $cursor);
        // now we have all the mentions in $matchingTweets; enough to do the backfill
        // sort by timestamp so the oldest ones are first
        usort($matchingTweets, function ($a, $b) {
            return $a['timestamp'] <=> $b['timestamp'];
        });

        // normalize the array
        $matchingTweets = array_values($matchingTweets);

        // now process each mention
        foreach ($matchingTweets as $tweetData) {
            $this->processMention($tweetData, 'closed');
        }

        // backfill done
        $this->getAccount()->setOption('inbox.last_mention_timestamp', Carbon::now()->getTimestamp());
    }

    /**
     * Extracts a tweet result and returns a standard array.
     */
    private function extractTweetData($tweetResult): array
    {

        $tweetId = Arr::get($tweetResult, 'rest_id');

        $sender = [
            'id' => Arr::get($tweetResult, 'core.user_results.result.rest_id'),
            'type' => 'user',
            'name' => Arr::get($tweetResult, 'core.user_results.result.core.name'),
            'profile_pic' => Arr::get($tweetResult, 'core.user_results.result.avatar.image_url'),
            'options' => array_filter([
                'username' => Arr::get($tweetResult, 'core.user_results.result.core.screen_name'),
                'verified' => Arr::get($tweetResult, 'core.user_results.result.is_blue_verified'),
            ]),
        ];

        $media = Arr::get($tweetResult, 'legacy.extended_entities.media', []);
        $mediaFiles = [];
        $mimes = new MimeTypes();

        foreach ($media as $mediaItem) {
            if (isset($mediaItem['type']) && $mediaItem['type'] === 'photo') {
                $mediaFiles[] = [
                    'url' => Arr::get($mediaItem, 'media_url_https'),
                    'mime' => $mimes->getMimeType(
                        explode('.', Arr::get($mediaItem, 'media_url_https', ''))[1] ?: 'jpg'
                    ),
                    'type' => 'image',
                ];
            } elseif (isset($mediaItem['type']) && $mediaItem['type'] === 'video') {
                $lastVariant = Arr::last(Arr::get($mediaItem, 'video_info.variants', []));
                $mediaFiles[] = [
                    'url' => Arr::get($lastVariant, 'url'),
                    'mime' => Arr::get($lastVariant, 'content_type'),
                    'type' => 'video',
                ];
            } else {
                report(new \Exception('Unsupported media type - ' . Arr::get($mediaItem, 'type') .': ' . json_encode($mediaItem)));
            }
        }

        return [
            'id' => $tweetId,
            'text' => Arr::get($tweetResult, 'legacy.full_text'),
            'timestamp' => Carbon::parse(Arr::get($tweetResult, 'legacy.created_at'))->getTimestamp(),
            'sender' => $sender,
            'media' => $mediaFiles,
            'in_reply_to' => Arr::get($tweetResult, 'legacy.in_reply_to_status_id_str'),
        ];
    }

    /**
     * Processes a mention tweet, creating or updating conversations and items.
     * @param array $tweetData Output from extractTweetData
     * @param string $status The status of the conversation, default is 'open'
     * @return void
     * @throws \Exception
     */
    private function processMention(array $tweetData, string $status = 'open'): void
    {
        $parentItem = null;
        $parentConversation = null;
        $inReplyTo = $tweetData['in_reply_to'] ?? null;

        if ($inReplyTo) {
            // Try to find the parent item by external_id
            $parentItem = \App\InboxConversationItem::findItem('tweet', $inReplyTo);
            if ($parentItem) {
                $parentConversation = $parentItem->conversation;
            } else {
                // Try to find the parent conversation by external_id
                $parentConversation = \App\InboxConversation::findConversation($inReplyTo, $this->getAccount()->id);
            }
        }

        if ($parentConversation) {
            $conversation = $parentConversation;
        } else {
            // Create or find a conversation for this tweet
            $conversation = \App\InboxConversation::findOrCreateConversation([
                'external_id' => $tweetData['id'],
                'status' => $status,
                'type' => 'tweet',
                'data' => [
                    'original' => $tweetData,
                ],
                'timestamp' => $tweetData['timestamp'],
            ], $tweetData['sender'], $this->getAccount());
        }

        // Insert the tweet as an item if not already present
        $item = $conversation->findItem('tweet', $tweetData['id']);
        if (!$item) {
            $item = \App\InboxConversationItem::createItem('tweet', [
                'account' => $this->getAccount(),
                'conversation' => $conversation,
                'conversation_type' => 'tweet',
                'conversation_external_id' => $conversation->external_id,
                'conversation_status' => $status,
                'parent_id' => $parentItem ? $parentItem->id : null,
                'external_id' => $tweetData['id'],
                'type' => 'tweet',
                'data' => [
                    'text' => $tweetData['text'],
                    'original' => $tweetData,
                ],
                'timestamp' => $tweetData['timestamp'],
            ], $tweetData['sender']);
        }

        // we have the item now
    }

    /**
     * @throws \Exception
     */
    public function handle($webhookData): bool
    {
        throw new \Exception('Not supported');
    }
}
