<template>
    <div class="wrapper mt-md-0 mt-5">
        <div v-if="loading" v-html="spinnerHtml"></div>
        <div v-else>
                
        <div v-if="currentStepNumber" class="d-flex justify-content-between align-items-center">
            <p class="lead-3 font-weight-700 text-dark mb-md-3 mb-4">
                {{  currentStep.title }}
            </p>
            <p class="d-md-block d-none mb-3">
                <span v-if="steps.length - completedSteps.length === 1">
                    Last Step
                </span>
                <span v-else>
                    Step {{ currentStepNumber }} of {{ steps.length }}
                </span>
            </p>
            <p class="d-md-none mb-4">
                {{ currentStepNumber }}/{{ steps.length }}
            </p>
        </div>
            <div class="row px-md-0 px-4">
                <div class="col px-0 mx-1"
                     v-for="(step, i) in steps">
                    <div :class="{'border-primary': step.completed(), 'border-bottom-4 border-primary': currentStep === step, 'border-bottom-4': currentStep !== step }">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div v-if="currentStep">
                        <div v-if="currentStep.id === 'verify_email'">
                            <div class="row">
                                <div class="container text-md-center text-left mx-md-7 mt-6 pt-md-0 pt-2">
                                    <p class="mb-0">A confirmation link was sent, please check your email.</p>
                                </div>
                                <div class="col-12 col-md-9 m-auto">
                                    <div class="card">
                                        <div class="card-body px-md-6 px-0 mt-md-0 mt-2 pb-md-0 pb-5">
                                            <div v-if="saving" v-html="overlayLoaderHtml"></div>
                                            <div class="d-flex flex-column flex-md-row justify-content-center mb-md-4">
                                                <a class="btn btn-outline-light btn-sm col col-md-5 mr-4 mb-4 mb-md-0" href="https://mail.google.com/" target="_blank"><img class="mr-1" src="/images/redesign/gmail.svg"> Open Gmail</a>
                                                <a class="btn btn-outline-light btn-sm col col-md-5 mb-4 mb-md-0" href="https://www.icloud.com/mail" target="_blank"> <img class="mr-1" src="/images/redesign/apple.svg"> Open Apple Mail </a>
                                            </div>
                                            <div class="d-flex flex-column flex-md-row justify-content-center mb-6 pb-md-0 pb-2">
                                                <a class="btn btn-outline-light btn-sm col col-md-5 mr-4 mb-4 mb-md-0" :href="'https://outlook.live.com/mail/?login_hint='+form.email" target="_blank"><img class="mr-1" src="/images/redesign/outlook.svg"> Open Outlook </a>
                                                <a class="btn btn-outline-light btn-sm col col-md-5" href="https://mail.yahoo.com/d/search/keyword=from%253Asupport%2540socialbu.com" target="_blank"><img class="mr-1" src="/images/redesign/yahoo.svg"> Open Yahoo Mail </a>  
                                            </div>
                                            <div class="container text-md-center text-left px-md-4 px-0">
                                                <form @submit.prevent="saveEmail">
                                                    <p class="mb-6 pb-md-0 pb-2">Haven't received the confirmation email? Check your spam <br class="d-md-block d-none"> folder or 
                                                        <button class="btn btn-link p-0" type="submit">
                                                            Resend it.
                                                        </button> 
                                                    </p>
                                                    <p class="mb-md-0 mb-2">
                                                        Wrong email address?
                                                        <button class="btn btn-link p-0" type="button" @click="changeEmail = true">
                                                            Change it.
                                                        </button> 
                                                    </p>
                                                    <div v-if="changeEmail">
                                                    
                                                        <div class="form-group">
                                                            <input type="text" class="form-control rounded-md " placeholder="Email address"
                                                                v-focus
                                                                :disabled="saving"
                                                                v-model="form.email" />
                                                        </div>
                                                        <div class="form-group text-md-center text-left">
                                                            <button class="btn btn-primary btn-lg" type="submit"
                                                                    :disabled="saving">
                                                                Resend Email
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="currentStep.id === 'set_password'">
                            <div class="row">
                                <div class="container text-md-center text-left mx-md-7 mt-6 pt-md-0 pt-2">
                                    <p class="mb-0">Set up a password for your SocialBu account to log in using email and password.</p>
                                </div>
                                <div class="col-12 col-md-8 m-auto px-0">
                                    <div class="card h-100">
                                        <div class="card-body px-md-6 px-0 pb-0 mt-md-0 mt-2">
                                            <div v-if="saving" v-html="overlayLoaderHtml"></div>
                                                <form @submit.prevent="savePassword">
                                                    <div class="form-group mb-4 pb-md-0 pb-1">
                                                        <label class="text-dark" for="password">New Password</label>
                                                        <div class="position-relative">
                                                            <div class="position-relative" >
                                                                <input :type="toggle.password ? 'text' : 'password'" class="form-control " placeholder="password should be 8+ characters"
                                                                    v-focus
                                                                    :disabled="saving"
                                                                    v-model="form.password" />
                                                                <i class="ph ph-md position-absolute cursor-pointer toggle-password-btn" :class="{'ph-eye' : toggle.password, 'ph-eye-slash': !toggle.password}" @click="toggle.password = !toggle.password"></i>
                                                            </div>
                                                            <!-- progressbar for the password score -->
                                                            <div class="progress bg-white mt-1" style="height: 2px;"
                                                                :class="{'opacity-0': !form.password}"
                                                                :data-score="(passwordScore = zxcvbn(form.password).score)">
                                                                <div class="progress-bar" role="progressbar"
                                                                    :class="{'bg-danger': passwordScore < 2, 'bg-warning': passwordScore < 3, 'bg-success': passwordScore > 2}"
                                                                    :style="{ width: (((passwordScore <= 0 ? 0.1 : passwordScore) / 4) * 100)  + '%' }"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="form-group mb-6 pb-md-0 pb-2">
                                                        <label class="text-dark" for="passwordConfirmation">Confirm Password</label>
                                                        <div class="position-relative" :class="{'border-danger': form.password_confirmation && form.password_confirmation !== form.password, 'border-success': form.password_confirmation && form.password_confirmation === form.password }">
                                                            <input :type="toggle.confirmPassword ? 'text' : 'password'" class="form-control " placeholder="Type your password again"
                                                                :disabled="saving"
                                                                v-model="form.password_confirmation" />
                                                            <i class="ph ph-md position-absolute cursor-pointer toggle-password-btn" :class="{'ph-eye' : toggle.confirmPassword, 'ph-eye-slash': !toggle.confirmPassword}" @click="toggle.confirmPassword = !toggle.confirmPassword"></i>
                                                        </div>
                                                    </div>
                                                    <div class="text-md-center text-left mb-md-0 mb-5">
                                                        <button class="btn btn-primary btn-lg" type="submit"
                                                                :disabled="saving">
                                                            Save &amp; Continue
                                                        </button>
                                                    </div>
                                                </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="currentStep.id === 'user_details'">
                            <div class="row">
                                <div class="container text-md-center text-left mx-md-7 mt-6 pt-md-0 pt-2">
                                    <p class="mb-0">Your details remain private and won't be shared with others. Help us serve you better!</p>
                                </div>
                                <div class="col-12 col-md-12">
                                    <div class="card">
                                        <div class="card-body px-md-6 px-0 pb-0 mt-md-0 mt-2">
                                            <div v-if="saving" v-html="overlayLoaderHtml"></div>
                                            <form @submit.prevent="saveUserDetails">
                                                <div class="form-group mb-0">
                                                    <label class="d-md-block d-none text-dark mb-4">
                                                        What Do You Do? Choose one or more <span class="text-danger">*</span>
                                                    </label>
                                                    <label class="d-md-none h5 mb-0 pb-20">
                                                        What Do You Do? <br> Choose one or more
                                                    </label>
                                                    <div class="row"
                                                         :data-selected="(selectedTitles = form.persona.split(',').map(l=>l.trim()))"
                                                        :data-opts="(titles = ['Marketing Manager', 'Social Media Manager', 'Digital Agency Owner', 'Small Business Owner', 'Influencer', 'Other'])">
                                                        <div class="col-md-6 col-12 mb-3"
                                                             v-for="title in titles">
                                                             
                                                            <button type="button" class="btn btn-block py-4 btn-outline-secondary bg-white active text-left position-relative"
                                                                    :class="{'border-primary': selectedTitles.includes(title)}"
                                                                    @click="()=>{
                                                                        
                                                                        let currTitles = [ ...selectedTitles ];
                                                                        if(currTitles.includes(title)){
                                                                            currTitles = currTitles.filter(t=>t!==title);
                                                                        }else{
                                                                            currTitles.push(title);
                                                                        }
                                                                        form.persona = currTitles.join(', ');
                                                                    }">
                                                                
                                                                {{ title }}
                                                                <i class="position-absolute" style="top: 21px; right: 16px;"
                                                                   :class="{'ph ph-check-circle ph-fill text-primary': selectedTitles.includes(title), 'ph ph-circle text-secondary': !selectedTitles.includes(title)}"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group mt-5 mb-4 pb-md-0 py-1">
                                                    <label class="text-dark mb-md-1 mb-6px">
                                                        Company Name
                                                    </label>
                                                    <div class="row">
                                                        <div class="col-md-6 col-12">
                                                            <input type="text" class="form-control" placeholder="Enter your company or organization name"
                                                                v-focus
                                                                :disabled="saving"
                                                                v-model="form.company" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group mt-2 mb-6">
                                                    <label class="text-dark mb-md-1 mb-6px">
                                                        Phone Number
                                                    </label>
                                                    <div class="row">
                                                        <div class="col-md-6 col-12">
                                                            <input type="tel" class="form-control" placeholder="******-567-8912"
                                                                :disabled="saving"
                                                                v-model="form.phone" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group pt-2" :data-channels="(channels = ['Search engine (Google, Yahoo, etc.)', 'Recommended by friend or colleague', 'Social media (Facebook, Twitter, etc.)', 'Advertisement', 'Podcast', 'Blog', 'Other',])">
                                                    <label class="text-dark h5 mb-3 pb-md-0 pb-2">
                                                        How Did You Hear About Us? <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="ml-5">
                                                        <div class="form-check mb-3 pb-md-0 pb-2"
                                                             v-for="channel in channels">
                                                            <label class="form-check-label">
                                                                <input class="form-check-input" type="radio" name="radio_channel"
                                                                       @click="form.acquisition_channel = channel"
                                                                       :checked="form.acquisition_channel === channel || (channel === 'Other' && form.acquisition_channel.includes('Other'))" />
                                                                {{ channel }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-if="form.acquisition_channel.includes('Other')">
                                                        <div class="col-md-6 col-12">
                                                            <textarea class="form-control px-4" placeholder="Optional" rows="6"
                                                                :disabled="saving"
                                                                :value="form.acquisition_channel.replace('Other: ', '').replace('Other', '')"
                                                                @change="
                                                                form.acquisition_channel = 'Other: ' + $event.target.value
                                                                "  />

                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group pt-md-4 pt-5">
                                                    <button class="btn btn-primary" type="submit"
                                                            :disabled="saving">
                                                        Save Details
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div v-else-if="currentStep.id === 'set_timezone'">
                            <div class="row">
                                <div class="container text-md-center text-left mx-md-7 mt-6 pt-md-0 pt-2">
                                    <p class="mb-0">Select your local timezone for accurate post scheduling. An <br class="d-md-block d-none"> incorrect choice may lead to unexpected scheduling.</p>
                                </div>
                                <div class="col-12 col-md-8 mx-auto">
                                    <div class="card h-100">
                                        <div class="card-body px-0 pb-0">
                                            <div v-if="saving" v-html="overlayLoaderHtml"></div>
                                            <div class="text-md-center text-left d-flex justify-content-center py-md-0 py-2 mb-6">
                                                Your Timezone seems to be: <span class="font-weight-600 ml-2">{{ Intl.DateTimeFormat().resolvedOptions().timeZone }}</span>
                                            </div>
                                            <form @submit.prevent="saveTimezone">
                                                <div class="form-group mb-6">
                                                    <label class="font-weight-500 text-dark">
                                                        Timezone
                                                    </label>
                                                    <select class="form-control" v-model="form.timezone" v-selectpicker="{liveSearch: true}">
                                                        <option :value="tz" v-for="tz in timezones">
                                                            {{ tz }}
                                                        </option>
                                                    </select>
                                                </div>
                                                <div class="form-group text-md-center text-left py-md-0 pt-2 pb-5 mb-0">
                                                    <button class="btn btn-primary btn-lg" type="submit"
                                                            :disabled="saving">
                                                        Save Timezone
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div v-else-if="currentStep.id === 'select_plan'">
                            <div class="row">                            
                                <div class="col-md-12 mt-6 py-md-0 py-2">
                                    <div class="lead-1 text-md-center text-left">
                                        <span v-if="data.can_get_trial">
                                            All plans come with 7 days free trial.
                                        </span>
                                        <span v-else>
                                            You can cancel or downgrade your subscription anytime.
                                        </span>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-body px-md-6 px-0 pb-0">
                                            <div v-if="saving" v-html="overlayLoaderHtml"></div>

                                            <div class="row">
                                                <div class="col-12 mb-6 pb-md-0 pb-2 d-flex justify-content-center align-items-center">
                                                    <div class="btn-group btn-group-toggle cursor-pointer plan_type_input bg-white p-1 border border-primary rounded-sm" data-toggle="buttons">
                                                        <label class="btn rounded-sm small-2 px-md-5 px-4 py-3 btn-outline-secondary cursor-pointer border-0"
                                                               :class="{'active btn-outline-primary': form.plan_duration === 'monthly'}">
                                                            <input type="radio" value="monthly" autocomplete="off"
                                                                   @click="form.plan_duration = 'monthly'"
                                                                   :checked="form.plan_duration === 'monthly'" /> Pay monthly
                                                        </label>
                                                        <label class="btn rounded-sm small-2 px-md-5 px-4 py-3 btn-outline-primary cursor-pointer border-0"
                                                               v-tour:onboarding_yearly_plan="'Get 2 months off with an yearly subscription.'"
                                                               :class="{'active': !form.plan_duration || form.plan_duration === 'yearly'}">
                                                            <input type="radio" value="yearly" autocomplete="off"
                                                                   @click="form.plan_duration = 'yearly'"
                                                                   :checked="!form.plan_duration || form.plan_duration === 'yearly'" /> Pay yearly
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="card border" v-if="data.coupon_name">
                                                        <div class="card-body">
                                                            We will add the discount ({{ data.coupon_name }}) once you start your subscription.
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-12 px-3"
                                                        v-for="plan in plans.filter(p => p.id !== 'free')">
                                                        <div class="card mb-4 plan-card cursor-pointer border position-relative rounded-xl" style="border-width: 2px;"
                                                            @click="(form.plan = plan.id) && initStripe($event)"
                                                            :class="{'border-primary': plan.id === form.plan}">

                                                            <div class="card-body">
                                                                <div class="d-flex justify-content-between align-items-start">
                                                                    <h5 class="font-weight-500 mb-2 text-dark" :class="{'font-weight-bold': plan.id === form.plan}">
                                                                        {{ plan['name'] }}
                                                                    </h5>
                                                                    <div class="position-absolute"
                                                                         style="top: 5px; right: 10px;"
                                                                         v-if="plan.id === form.plan">
                                                                        <i class="ph ph-fill ph-check-circle bg-white text-primary"></i>
                                                                    </div>
                                                                    <a class="text-muted small" href="/pricing" target="_blank" title="Details"
                                                                        v-tooltip>
                                                                        <i class="ph ph-arrow-up-right"></i>
                                                                    </a>
                                                                </div>
                                                                <h2 class="mb-0"
                                                                    v-if="form.plan_duration === 'yearly'">
                                                                    ~${{ (plan['price_yearly'] / 12).toFixed() }} <small class="text-muted">/ mo</small>
                                                                </h2>
                                                                <h2 class=""
                                                                    v-else>
                                                                    ${{ plan['price'] }} <small class="text-muted">/ mo</small>
                                                                </h2>
                                                                <p class="text-dark"
                                                                    v-if="form.plan_duration === 'yearly'">
                                                                        ${{ plan['price_yearly'] }}/year
                                                                </p>
                                                                <ul class="list-unstyled mt-3 mb-0 text-left">
                                                                    <li class="lead-2 mb-2"><i class="ph ph-check"></i> {{ plan['limits']['accounts'] }} accounts</li>
                                                                    <li class="lead-2 mb-2" v-if="plan['limits']['teams'] === 0"><i class="ph ph-x ph-md"></i> No teams</li>
                                                                    <li class="lead-2 mb-2" v-else><i class="ph ph-check"></i> {{ plan['limits']['teams'] }} teams</li>
                                                                    <li class="lead-2">
                                                                        <i class="ph ph-check" v-if="['super', 'supreme'].includes(plan.id)"></i>
                                                                        <i class="ph ph-x ph-md" v-else></i>
                                                                        Phone Support
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-12 col-lg-8 offset-lg-2 px-0">
                                                    <div class="card">
                                                        <div class="card-body py-0">
                                                            <form @submit.prevent="savePlan">
                                                                <div class="form-group" v-show="stripeInitialized && (form.plan && form.plan !== 'free')">
                                                                    <div class="divider mb-4">
                                                                        <div class="divider-text">
                                                                            Add your card details
                                                                        </div>
                                                                    </div>
                                                                    <!-- card form -->
                                                                    <div class="row">
                                                                        <div class="col-12">
                                                                            <div class="form-row">
                                                                                <div class="form-group col-12">
                                                                                    <label for="card-element">
                                                                                        Credit or debit card
                                                                                    </label>
                                                                                    <div class="form-control" id="card-element" ref="card_element">
                                                                                        <!-- A Stripe Element will be inserted here. -->
                                                                                    </div>
                                                                                    <!-- Used to display Element errors. -->
                                                                                    <div ref="card_errors" role="alert" class="alert alert-danger" style="display:none"></div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="form-row">
                                                                                <div class="form-group col-12">
                                                                                    <label for="card-holder">
                                                                                        Name on Card
                                                                                    </label>
                                                                                    <input class="form-control card_name" id="card-holder" ref="card_holder" type="text" required placeholder="Name" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="form-row">
                                                                                <div class="form-group col-12">
                                                                                    <label for="card-country">
                                                                                        Country
                                                                                    </label>
                                                                                    <select id="card-country" class="form-control" title="Country" ref="card_country">
                                                                                        <option :value="country.code"
                                                                                                v-for="country in countries">{{ country.name }}</option>
                                                                                        <option value="00">Other</option>
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group text-md-center text-left my-md-4 my-5">
                                                                    <button class="btn btn-light btn-lg mr-2" type="button"
                                                                            @click.prevent="(form.plan = 'free') && savePlan($event)"
                                                                            :disabled="saving">
                                                                        Skip
                                                                    </button>
                                                                    <button class="btn btn-primary btn-lg" type="submit"
                                                                            @click.prevent="()=>{
                                                                                if(form.plan === 'free'){
                                                                                    form.plan = null;
                                                                                }
                                                                                savePlan($event);
                                                                            }"
                                                                            :disabled="saving">
                                                                        Continue
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>           
                    <div class="d-flex justify-content-center align-items-center h-200 mt-6"
                        v-else>
                        <div class="text-center">
                            <h1>
                                <i class="ph ph-check-circle ph-2xl text-success"></i>
                            </h1>
                            <p class="lead-3 font-weight-700 text-dark mb-2">All Done!</p>
                            
                            <div>
                                <p class="lead-2 mb-2">
                                    Your SocialBu Account Is Ready To Use
                                </p>
                                <a class="btn btn-primary btn-sm rounded mt-4"
                                   :href="fromUrl ? fromUrl : '/app/'">
                                    Continue
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
import {spinnerHtml, axios, overlayLoaderHtml, alertify, axiosErrorHandler} from "../../components";
import { backOff } from "exponential-backoff";
import { extend } from "lodash";
import zxcvbn from "zxcvbn";
import $ from "jquery";
let stripe, stripeCard;
export default {
    name: "Onboarding",
    data() {
        return {
            loading: true,
            data: {
                email: "",
                company: "",
                timezone: "",
                plan: "",
                persona: "",
                acquisition_channel: "",
                can_get_trial: true,
                coupon_name: "", // stripe coupon name if any
            },
            currentState: {
                // will be updated from the backend
                is_verified: false, // required
                has_password: false, // required
                has_company: false, // optional
                has_phone: false, // optional
                has_persona: false, // required
                has_acquisition_channel: false, // required
                has_timezone: false, // required
                added_accounts: false, // optional
                selected_plan: false // optional
            },
            form: { // this will be extended with the data object
                email: "",

                password: "",
                password_confirmation: "",

                company: "",
                phone: "",
                persona: "",
                acquisition_channel: "",

                timezone: "",

                plan: "",
                plan_duration: "yearly",

            },
            saving: false, // when saving any data

            timezones: [], // populated from the backend

            plans: [], // available plans from the backend

            countries: [], // populated from the backend - {name, code}

            stripeInitialized: false,
            selectedPlan: null,

            changeEmail:false,
            toggle: {
                password:false,
                confirmPassword:false,
            }
        };
    },
    watch: {
        data(v) {
            // extend the form object with the data object
            this.form = extend({}, this.form, v);
        },
        saving(v) {
            // when this toggles, that means something was saved probably, so update data
            if (!v) this.loadData();
        },
        timezones(v) {
            // when the timezones are loaded, set the guessed timezone
            if (v.length && !this.form.timezone) {
                this.form.timezone = this.localTimezone;
            }
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        overlayLoaderHtml: () => overlayLoaderHtml,
        fromUrl(){
            const getQueryStringParams = query => {
                return query
                    ? (/^[?#]/.test(query) ? query.slice(1) : query)
                        .split('&')
                        .reduce((params, param) => {
                                let [key, value] = param.split('=');
                                params[key] = value ? decodeURIComponent(value.replace(/\+/g, ' ')) : '';
                                return params;
                            }, {}
                        )
                    : {}
            };
            const params = getQueryStringParams(window.location.search);
            return params.from && params.from.startsWith("https://socialbu.com") ? params.from : null;
        },
        steps() {
            if (Object.keys(this.currentState).length === 0) {
                return [];
            }
            return [
                {
                    title: "Verify your email",
                    id: "verify_email",
                    completed: () => this.currentState.is_verified
                },
                {
                    title: "Set your password",
                    id: "set_password",
                    completed: () => this.currentState.has_password
                },
                {
                    title: "Tell us about yourself",
                    id: "user_details",
                    completed: () => this.currentState.has_persona && this.currentState.has_acquisition_channel,
                },
                {
                    title: "Set your timezone",
                    id: "set_timezone",
                    completed: () => this.currentState.has_timezone
                },
                {
                    title: "Select your plan",
                    id: "select_plan",
                    completed: () => this.currentState.selected_plan
                },
                // {
                //     title: "Add your social accounts",
                //     id: "add_accounts",
                //     completed: () => this.currentState.added_accounts
                // }
            ];
        },
        currentStep() {
            return this.steps.find(step => !step.completed());
        },
        currentStepNumber() {
            if (!this.currentStep) return null;
            return this.steps.indexOf(this.currentStep) + 1;
        },
        completedSteps() {
            return this.steps.filter(step => step.completed());
        },

        localTimezone() {
            return this.$moment.tz.guess();
        },
    },
    methods: {
        zxcvbn,
        async loadData(showFeedback = false) {

            if(!this.loading && !showFeedback && !this.currentStep){
                // all done
                return;
            }

            try {
                if (showFeedback) this.loading = true;
                const { data } = await axios.get("/app/onboarding/data");
                this.currentState = data.state;
                if(showFeedback)
                    this.data = data.data;

                if(showFeedback){ // first time only
                    this.timezones = data.timezones;
                    this.plans = data.plans;
                    this.countries = data.countries;
                }

            } catch (error) {
                if (showFeedback) alert(error.message);
                console.log(error);
                if(!this.data.email){
                    alert("Loading data again...");
                    // the data was not loaded
                    return await this.loadData();
                }
            }
            if (showFeedback) this.loading = false;
        },
        pollData() {
            backOff(
                async () => {
                    const currState = JSON.stringify(this.currentState);
                    await this.loadData(false);
                    if (JSON.stringify(this.currentState) === currState) {
                        throw new Error("No change in state"); // so it will retry request again
                    }
                },
                {
                    delayFirstAttempt: true,
                    startingDelay: 2000,
                    maxDelay: 60000,
                    timeMultiple: 2
                }
            )
                .catch(() => {})
                .finally(() => {
                    this.pollData();
                });
        },
        async getStripe(){

            if(!stripe) {
                // make sure stripe is loaded
                let checks = 0;
                while (!window.Stripe && checks < 30) {
                    await new Promise((res, rej) => setTimeout(res, 1000));
                    ++checks;
                }
                if (!window.Stripe) {
                    window.alert("Some components failed to load. Reloading page...");
                    document.location.reload();
                    return;
                }

                // init stripe sdk
                stripe = Stripe(process.env.MIX_STRIPE_KEY);
            }

            return stripe;
        },
        async initStripe(){

            if(!stripeCard){

                const stripe = await this.getStripe();
                if(!stripe){
                    return;
                }

                this.stripeInitialized = true;

            }

            this.$nextTick(()=>{
                if(!stripeCard){

                    const elements = stripe.elements();
                    // Create an instance of the card Element.
                    const card = elements.create("card", {
                        style: {
                            base: {
                                fontSize: "16px",
                                color: "#32325d"
                            }
                        }
                    });
                    const cardElement = this.$refs.card_element;

                    setTimeout(() => {
                        const $errors = $(this.$refs.card_errors);

                        // Add an instance of the card Element into the `card-element` <div>.
                        card.mount(cardElement);

                        card.addEventListener("change", ({ error }) => {
                            if (error) {
                                $errors.text(error.message).show();
                            } else {
                                $errors.empty().hide();
                            }
                        });
                    }, 500);

                    stripeCard = card;

                }
            });
        },

        async saveEmail() {
            try {
                this.saving = true;
                const { data } = await axios.post("/app/onboarding/verify_email", {
                    email: this.form.email
                });
                if( data === "sent"){
                    this.data.email = this.form.email;
                    alertify.success("Please check your inbox for a verification email");
                } else if( data === "already_verified"){
                    alertify.success("Your email is already verified");
                    this.currentState.is_verified = true;
                } else {
                    alertify.error("Something went wrong");
                }
            } catch (e){
                alertify.error(axiosErrorHandler(e, true));
            } finally {
                this.saving = false;
            }
        },

        async savePassword() {
            if(this.form.password !== this.form.password_confirmation){
                alertify.error("Passwords do not match");
                return;
            }
            try {
                this.saving = true;
                const { data } = await axios.post("/app/onboarding/set_password", {
                    password: this.form.password,
                    password_confirmation: this.form.password_confirmation
                });
                this.currentState.has_password = true;
            } catch (e){
                alertify.error(axiosErrorHandler(e, true));
            } finally {
                this.saving = false;
            }
        },

        async saveUserDetails() {
            const personaList = this.form.persona.split(',').map(l => l.trim()).filter(l => l);
            if(!personaList.length){
                alertify.error("Please select at least one option about what you do");
                return;
            } else if(!this.form.acquisition_channel.trim()){
                alertify.error("Please select one option about how did you hear about us");
                return;
            }
            try {
                this.saving = true;
                const { data } = await axios.post("/app/onboarding/user_details", {
                    company: this.form.company,
                    phone: this.form.phone,
                    persona: personaList.join(", "),
                    acquisition_channel: this.form.acquisition_channel,
                });
                this.data.persona = this.form.persona = personaList.join(", ");
                this.currentState.has_persona = true;
            } catch (e){
                alertify.error(axiosErrorHandler(e, true));
            } finally {
                this.saving = false;
            }
        },

        async saveTimezone() {
            if(this.form.timezone.trim() === ""){
                alertify.error("Please select a timezone");
                return;
            }
            try {
                this.saving = true;
                const { data } = await axios.post("/app/onboarding/set_timezone", {
                    timezone: this.form.timezone
                });
                this.currentState.has_timezone = true;
                this.data.timezone = this.form.timezone;
            } catch (e){
                alertify.error(axiosErrorHandler(e, true));
            } finally {
                this.saving = false;
            }
        },

        async savePlan() {

            let stripeIntent;

            if(!this.form.plan){
                alertify.error("Please select a plan");
                return;
            } else if(this.form.plan !== 'free'){
                // should have stripe card
                if(!stripeCard){
                    alertify.error("Please add your card details");
                    return;
                } else if(!this.$refs.card_holder.value){
                    alertify.error("Please add your name");
                    return;
                } else if(!this.$refs.card_country.value){
                    alertify.error("Please select the card holder's country");
                    return;
                }

                let clientSecret;

                this.saving = true;
                try {
                    // get intent secret
                    const { data } = await axios.post("/app/settings/create_payment_intent");
                    clientSecret = data.client_secret;
                } catch (e) {
                    const $errors = $(this.$refs.card_errors);
                    // Inform the customer that there was an error.
                    $errors.text(e.message);
                    this.saving = false;
                    return;
                }

                const { setupIntent, error } = await stripe.confirmCardSetup(
                    clientSecret, {
                        payment_method: {
                            card: stripeCard,
                            billing_details: { name: this.$refs.card_holder.value }
                        }
                    }
                );

                this.saving = false; // will be set again where needed below

                if (error) {
                    const $errors = $(this.$refs.card_errors);
                    // Inform the customer that there was an error.
                    $errors.text(error.message);
                    return;
                }
                stripeIntent = setupIntent;
            }

            if(this.form.plan === 'free'){
                // user wants free, so we confirm
                const shouldContinue = await new Promise(res => {
                    alertify
                        .confirm('Are you sure you do not want to choose a plan yet?' + (this.data.can_get_trial? "<br/>" + ' You are eligible to get a free 7 days trial if you start your subscription now.' : ''), ret => {
                            res(false);
                        }, () => {
                            res(true);
                        })
                        .set('labels', {
                            ok: 'No, I want full access',
                            cancel: 'Limited access only',
                        })
                        .set('reverseButtons', true)
                        .set('closable', false);
                });
                if(!shouldContinue) return;
            }

            try {
                this.saving = true;

                if(this.form.plan !== 'free'){
                    await axios.post("/app/onboarding/select_plan", {
                        payment_method: stripeIntent.payment_method,
                        card_name: this.$refs.card_holder.value,
                        plan: this.form.plan,
                        plan_duration: this.form.plan_duration,
                        country: this.$refs.card_country.value,
                    });
                    __recordEvent("AddPaymentInfo");
                } else {
                    await axios.post("/app/onboarding/select_plan", {
                        plan: this.form.plan,
                    });
                }
                this.currentState.selected_plan = true;
                this.data.plan = this.form.plan;
            } catch (e){
                alertify.error(axiosErrorHandler(e, true));
            } finally {
                this.saving = false;
            }
        },
    },
    mounted() {
        this.loadData(true);

        // start polling
        this.pollData();
    }
};
</script>

<style lang="scss" scoped>
.wrapper {
    min-height: 340px;
}

.toggle-password-btn{
    bottom: 18px;
    right: 15px;
    color: #4A5465;
    z-index: 10
}
.form-group label{
    font-family: 'Plus Jakarta Sans' !important;
}
.mb-6px{
    margin-bottom: 6px;
}
.border-bottom-4{
    border-bottom: 4px solid #E4E7ED;
    border-radius: 8px !important;
}
</style>
