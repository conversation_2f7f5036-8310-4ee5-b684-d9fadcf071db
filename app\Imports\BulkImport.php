<?php

namespace App\Imports;

use App\Account;
use App\Helpers\ApiHelper;
use App\Http\Controllers\User\QueueController;
use App\PublishQueue;
use App\User;
use Carbon\Carbon;
use Google_Service_Drive;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Mimey\MimeTypes;
use Illuminate\Http\Request;

class BulkImport implements ToCollection, WithHeadingRow
{
    use Importable;

    private $cachedAccounts = null;

    /** @var User $user */
    private $user;

    private $count;

    private $total = 0;

    private $filesByRow = [];

    public function __construct($user, $alreadyImported = 0)
    {
        $this->user = $user;
        $this->count = $alreadyImported;
    }

    /**
     * @param Collection $collection
     * @throws \Exception
     */
    public function collection(Collection $collection)
    {
        // set total
        $this->total = $collection->count();

        $MAX_ROWS = 200; // max rows allowed importing at once

        $userPlan = $this->user->getPlan(true);

        if($userPlan === 'super'){
            $MAX_ROWS = 500;
        } else if($userPlan === 'supreme'){
            $MAX_ROWS = 2000;
        }

        if($this->total > $MAX_ROWS){
            throw new \Exception('You can only import a maximum of ' . $MAX_ROWS . ' posts at a time. Please upgrade your account for increased limits.');
        }

        // get the next 100 posts after $alreadyImported
        $allRows = $collection->slice($this->count, 100)->toArray();

        $rowNo = 0;
        // the first loop is for validation and preparation
        foreach ($allRows as $_itemIndex => $_item){
            ++$rowNo;

            if(isset($_item['publish_at'])){
                $_item['publish_at'] = strtolower($_item['publish_at']);
            }

            // trim all values
            foreach ($_item as &$_val){
                if($_val && !is_array($_val)){ // should always be non-array
                    $_val = trim($_val);
                }
            }

            $saveToQueue = isset($_item['add_to_queue']);
            /** @var PublishQueue $queue */
            $queue = null;

            /**
             * @var Account[]|Collection $accounts
             */
            $accounts = [];

            // validation for normal posts
            if(!$saveToQueue){
                try {
                    Validator::make($_item, [
                        'account_type' => [
                            'bail',
                            'required',
                            'string',
                            function ($attribute, $value, $onFailure) use ($rowNo) {
                                if (!$this->getAccountTypes($value)) {
                                    $onFailure('Unknown account type: ' . $value);
                                }
                            }
                        ],
                        'account' => [
                            'bail',
                            'required',
                            'string',
                            function ($attribute, $value, $onFailure) use ($_item, $rowNo) {
                                $accountTypes = $this->getAccountTypes($_item['account_type']);
                                if (!$accountTypes) {
                                    $onFailure('Unknown account: ' . $value);
                                    return;
                                }
                                $account = $this->accounts()->whereIn('type', $accountTypes)->where('name', mb_strtolower($value))->first();

                                if (!$account) {
                                    $onFailure('Unknown account: ' . $value . '. Please make sure 1 matching account exists');
                                }
                                // if the account is not active
                                if(!$account->active){
                                    throw new \Exception('[' . $rowNo . ']' . ' the mentioned account is disconnected.');
                                }


                                // check limit
                                $_user = getResourceOwner($account);
                                if (getUsage('monthly_posts', $account) + 1 > getPlanUsage('monthly_posts', $account)) {
                                    // show error
                                    if ($_user->id !== $this->user->id) {
                                        // is team account
                                        $onFailure('Please upgrade the subscription (of the team owner\'s account) for more posts on: ' . $account->name . '.');
                                    } else {
                                        $onFailure('Please upgrade your subscription from Settings > Subscription & Billing for more posts on: ' . $account->name . '.');
                                    }
                                }
                            }
                        ],
                        'publish_at' => [
                            'bail',
                            'required',
                        ]
                    ])->validate();
                } catch (ValidationException $exception){
                    throw new \Exception ('[' . $rowNo . '] ' . array_first(array_flatten($exception->errors())) );
                }
                // validate date format manually
                if(isset($_item['publish_at'])){
                    $e = null;
                    try{
                        $_item['publish_at'] = $this->getFormattedDate($_item['publish_at'], 'd/m/Y h:ia');
                    }catch(\Exception $exception){
                        $e = $exception;
                    }

                    if ($e) {
                        throw new \Exception('[' . $rowNo . '] Invalid date format for `' . $_item['publish_at'] . '. Correct example format: `16/03/2020 01:30pm`');
                    } else if (now() > Carbon::createFromFormat('d/m/Y h:ia', $_item['publish_at'], timezone()) ) {
                        throw new \Exception('[' . $rowNo . '] Publishing time cannot be in the past');
                    }
                }


                try {
                    $account = $this->accounts()->whereIn('type', $this->getAccountTypes($_item['account_type']))->where('name', mb_strtolower($_item['account']))->first();
                } catch (\Exception $exception){
                    throw new \Exception($exception->getMessage());
                }

                $accounts = collect([$account]);

            } else {
                // we can do queue specific validations and stuff
                /** @var PublishQueue $queue */
                $queue = PublishQueue::available($this->user()->id)->firstWhere('name', $_item['add_to_queue']);
                if(!$queue){
                    throw new \Exception('[' . $rowNo . '] ' . 'Unable to find the queue named '. $_item['add_to_queue'].  '. Please enter the correct name.');
                }

                $accounts = $queue->accounts();
            }

            $mapOptions = [];
            $mapOptions['attach_thumbnail'] = 'thumbnail';
            $mapOptions['video_thumbnail'] = 'thumbnail';
            $mapOptions['thumbnail_picture'] = 'thumbnail';
            $mapOptions['thumbnail_photo'] = 'thumbnail';
            $mapOptions['thumbnail_url'] = 'thumbnail';
            $mapOptions['thumbnails'] = 'thumbnail';

            foreach($accounts as $account){ // loop through all accounts to do account-specific mapping
                if((strpos($account->type,'google.location') !== false)){
                    $mapOptions['post_type'] = 'topic_type';
                    $mapOptions['title'] = 'event_title';
                    $mapOptions['start_at'] = 'event_start';
                    $mapOptions['end_at'] = 'event_end';
                    $mapOptions['offer_title'] = 'event_title';
                    $mapOptions['offer_start'] = 'event_start';
                    $mapOptions['offer_end'] = 'event_end';
                } else if((strpos($account->type,'pinterest') !== false)){
                    $mapOptions['select_board'] = 'board_name';
                    $mapOptions['board'] = 'board_name';
                    $mapOptions['link'] = 'pin_link';
                    $mapOptions['external_link'] = 'pin_link';
                    $mapOptions['note'] = 'pin_note';
                    $mapOptions['note_to_self'] = 'pin_note';
                    $mapOptions['title_for_pin'] = 'pin_title';
                    $mapOptions['title'] = 'pin_title';
                    $mapOptions['pin_title'] = 'pin_title';
                } else if($account->type === 'tiktok.profile'){
                    $mapOptions['privacy'] = 'privacy_status';
                } else if((strpos($account->type,'reddit') !== false)){
                    $mapOptions['mark_as_spoiler'] = 'is_spoiler';
                    $mapOptions['mark_as_nsfw'] = 'is_nsfw';
                } else if((strpos($account->type,'youtube') !== false)){
                    $mapOptions['tags'] = 'video_tags';
                    $mapOptions['title'] = 'video_title';
                    $mapOptions['privacy'] = 'privacy_status';
                    $mapOptions['visibility'] = 'privacy_status';
                }
            }

            // options that might have multiple columns
            $multiColumns = [
                'alt_text' => 'media_alt_text',
                'image_alt_text' => 'media_alt_text',
                'threaded_replies' => 'threaded_replies',
                'threaded_reply' => 'threaded_replies'
            ];

            // map options
            foreach($_item as $key => $value){
                if(in_array($key, array_keys($mapOptions))){
                    $_item[$mapOptions[$key]] = $value;
                }

                foreach($multiColumns as $multiKey => $multiColumn){
                    if(strpos($key, $multiKey) !== false){
                        if(!isset($_item[$multiColumn]) || !is_array($_item[$multiColumn])) {
                            $_item[$multiColumn] = [];
                        }
                        if($multiColumn === 'threaded_replies'){
                            // threaded replies should be an array
                            $_item[$multiColumn][] = [
                                'text' => $value,
                            ];
                        } else {
                            $_item[$multiColumn][] = $value;
                        }
                        unset($_item[$key]); // remove the original key, not needed
                    }
                }
            }

            if( isset($_item['post_type']) && strtolower($_item['post_type']) === 'offer'){
                if (!isset($_item['event_title'])) {
                    throw new \Exception('[' . $rowNo . '] The offer title field is required');
                }
            }

            // account-type-specific validations
            foreach($accounts as $account){
                if($account->type === 'google.location'){
                    if(isset($_item['event_start'])){
                        $e = null;
                        try{
                            $_item['event_start'] = $this->getFormattedDate($_item['event_start'], 'd/m/Y h:i a');
                        }catch(\Exception $exception){
                            $e = $exception;
                        }

                        if ($e) {
                            throw new \Exception('[' . $rowNo . '] Invalid date format for `' . $_item['event_start'] . '. Correct example format: `16/03/2020 01:30pm`');
                        } else if (now() > Carbon::createFromFormat('d/m/Y h:ia', $_item['event_start'], timezone()) ) {
                            throw new \Exception('[' . $rowNo . '] Event start time cannot be in the past');
                        }
                    }
                    if(isset($_item['event_end'])){
                        $e = null;
                        try{
                            $_item['event_end'] = $this->getFormattedDate($_item['event_end'], 'd/m/Y h:i a');
                        }catch(\Exception $exception){
                            $e = $exception;
                        }
                        if ($e) {
                            throw new \Exception('[' . $rowNo . '] Invalid date format for `' . $_item['event_end'] . '. Correct example format: `16/03/2020 01:30pm`');
                        } else if (now() > Carbon::createFromFormat('d/m/Y h:ia', $_item['event_end'], timezone())) {
                            throw new \Exception('[' . $rowNo . '] Event end time cannot be in the past');
                        }
                    }
                } else if(strpos($account->type,'pinterest') !== false){

                    if(!isset($_item['board_name'])){
                        throw new \Exception('[' . $rowNo . '] ' . 'Board name is required.');
                    }

                    $extra_data = $account->getOption('extra_data');
                    if(isset($extra_data['boards'])){
                        $boardFound = false;
                        foreach ($extra_data['boards'] as $board) {
                            if (strtolower($board['name']) == strtolower($_item['board_name'])) {
                                $boardFound = true;
                                break;
                            }
                        }
                        if(!$boardFound){
                            throw new \Exception('[' . $rowNo . '] ' . 'Unable to find the board name ' .$_item['board_name'] . '. Please enter the correct name');
                        }
                    } else {
                        throw new \Exception('[' . $rowNo . '] ' . 'Unable to find the boards.');
                    }
                } else if((strpos($account->type,'reddit') !== false) && isset($_item['flair']) ){
                    $extra_data = $account->getOption('extra_data');

                    if($extra_data && isset($extra_data['flairs'])){
                        $flairs = $extra_data['flairs'];

                        $flair = collect($flairs)->filter(function ($flair) use ($_item) {
                            return strtolower($_item['flair']) === strtolower($flair['text']);
                        });

                        if(isset($flair) && isset($flair[0]) && isset($flair[0]['id'])){
                            $_item['flair_id'] = $flair[0]['id'];
                        }else {
                            throw new \Exception('[' . $rowNo . '] ' . 'Unable to find the flair named '. $_item['flair'].  '. Please enter the correct name.');
                        }

                    } else {
                        throw new \Exception('[' . $rowNo . '] ' . 'Unable to find the flair named '. $_item['flair'].  '. Please enter the correct name.');
                    }
                }
            }

            if(isset($_item['type']) && strtolower($_item['type']) === 'story'){
                //need to set the type to post and add post as story column so we can publish this post as story
                $_item['type'] = 'post';
                $_item['post_as_story'] = true;
            }

            // save the array as it will be re-used
            $allRows[$_itemIndex] = $_item;

            // convert to object for easy use
            // IMPORTANT: $_item is modified at this point
            $item = (object) $_item;

            $media = $item->media ?? null; // this CAN be a comma-separated column having multiple attachment links

            if($media){

                $mediaLinks = collect(explode(',', $media))->map(function($link){
                    return trim($link);
                })->filter()->values()->toArray();

                // check if all links are valid urls, if not, maybe it's one URL with comma in it?
                $invalidLinks = collect($mediaLinks)->filter(function($link){
                    return !filter_var($link, FILTER_VALIDATE_URL);
                })->values()->toArray();

                if(count($invalidLinks) > 0){
                    // treat this as a single link
                    $mediaLinks = [ $media ];
                }

                foreach($mediaLinks as $mediaLink){
                    try {
                        $file = $this->getAttachment($mediaLink);

                        $this->setFileByRow(
                            $rowNo,
                            'media',
                            $file
                        );

                    } catch (\Exception $exception){
                        // cleanup
                        try {
                            foreach ($this->getAllFiles() as $uploadedFile) {
                                $path = $uploadedFile->getRealPath();
                                if ($path) @unlink($path);
                            }
                        } catch (\Exception $exception2){ }
                        throw new \Exception('[' . $rowNo . '] Unable to fetch file: ' . $exception->getMessage());
                    }
                }
            }

            if(isset($item->thumbnail)){
                try {
                    $file = $this->getAttachment($item->thumbnail);
                    
                    $extension = $file->getClientOriginalExtension();
                    //only proceed if the attachment is an image
                    if(!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'])){
                        throw new \Exception('Only images are allowed as thumbnail.');
                    }

                    $this->setFileByRow(
                        $rowNo,
                        'thumbnail',
                        $file
                    );

                    // modify original item
                    $_item['thumbnail'] = upload_file_to_cloud($file);
                } catch (\Exception $exception){
                    // cleanup
                    try {
                        foreach ($this->getAllFiles() as $file) {
                            $path = $file->getRealPath();
                            if ($path) @unlink($path);
                        }
                    } catch (\Exception $exception2){ }
                    throw new \Exception('[' . $rowNo . '] Unable to fetch thumbnail: ' . $exception->getMessage());
                }
            }
            
            // saving item row because it was modified again probably
            $allRows[$_itemIndex] = $_item; 

            if($saveToQueue) {
                // code after this block is for normal posts
                continue;
            }

            try {
                $account->publishPost([
                    'content' => $item->content,
                    'attachments' => $this->getFilesByRow($rowNo, 'media'),
                    'options' => $this->getPostOptions($_item, [$account->type]),
                ], Carbon::createFromFormat('d/m/Y h:ia', $item->publish_at, timezone())->timezone('UTC'), true);
            } catch (\Exception $exception){
                // cleanup temp files for the collection
                try {
                    foreach ($this->getAllFiles() as $_file) {
                        if ($_file)
                            @unlink($_file->getRealPath());
                    }
                } catch (\Exception $exception2){}
                if($exception instanceof ValidationException){
                    throw new \Exception('[' . $rowNo . '] Validation error: ' . implode('. ', array_first($exception->errors())), 0, $exception);
                }
                throw new \Exception('[' . $rowNo . '] Error when validating post: ' . $exception->getMessage(), 0, $exception);
            }
        }

        $rowNo = 0;
        foreach ($allRows as $_item){
            ++$rowNo;

            $saveToQueue = isset($_item['add_to_queue']);

            $item = (object) $_item;
            // now actually post
            // here all rows are validated already

            if($saveToQueue){
                /** @var PublishQueue $queue */
                $queue = PublishQueue::available($this->user()->id)->firstWhere('name', $_item['add_to_queue']);

                $accountTypes = $queue->accounts()->map(function($acc){
                    return $acc->type;
                })->values()->toArray();

                try {
                    $customRequest =  new Request([
                        'content' => $_item['content'],
                        'accounts' => $queue->accounts()->map(function($acc){ return $acc->id; })->toArray(),
                        'options' => $this->getPostOptions($_item, $accountTypes),
                    ]);
                    $files = $this->getFilesByRow($rowNo, 'media');
                    if(count($files) > 0){
                        $customRequest->files->add([
                            'attachments' => $files,
                        ]);
                    }
                    (new \App\Http\Controllers\Api\QueueController())->addPost($queue->id, $customRequest);
                } catch (\Exception $e) {

                    try {
                        foreach ($this->getAllFiles() as $_file){
                            if($_file)
                                @unlink($_file->getRealPath());
                        }
                    } catch (\Exception $exception){ }

                    throw new \Exception('[' . $rowNo . '] Error adding post to queue: ' . $e->getMessage());
                }

            } else {

                $accountTypes = $this->getAccountTypes($item->account_type);

                /** @var Account $account */
                $account = $this->accounts()->whereIn('type', $accountTypes)->where('name', mb_strtolower($item->account))->first();

                $account->publishPost([
                    'content' => $item->content,
                    'attachments' => $this->getFilesByRow($rowNo, 'media'),
                    'options' => $this->getPostOptions($_item, [$account->type]),
                ], Carbon::createFromFormat('d/m/Y h:ia', $item->publish_at, timezone())->timezone('UTC'));
            }
        }

        $this->count += $rowNo;

        // clean up
        try {
            foreach ($this->getAllFiles() as $_file){
                if($_file)
                    @unlink($_file->getRealPath());
            }
        } catch (\Exception $exception){
            report($exception);
        }
    }

    private function setFileByRow(int $row, string $column, UploadedFile $file){
        if(!isset($this->filesByRow[$row])){
            $this->filesByRow[$row] = [];
        }

        if(!isset($this->filesByRow[$row][$column])){
            $this->filesByRow[$row][$column] = [];
        }

        $this->filesByRow[$row][$column][] = $file;
    }

    /**
     * @param int $row
     * @param string $column
     * @return UploadedFile[]
     */
    private function getFilesByRow(int $row, string $column): array
    {
        return $this->filesByRow[$row][$column] ?? [];
    }

    private function getAllFiles(): array
    {
        $allFiles = [];
        foreach ($this->filesByRow as $columns){
            foreach ($columns as $files){
                foreach ($files as $file){
                    $allFiles[] = $file;
                }
            }
        }
        return $allFiles;
    }

    private $lastMedia;

    /**
     * @param $media
     * @return UploadedFile
     * @throws \Exception
     */
    public function getAttachment($media): UploadedFile
    {

        if(is_string($media) && $media) {
            $mediaHost = @parse_url($media, PHP_URL_HOST);

            if( ($this->lastMedia && $this->lastMedia === $mediaHost) || (Str::contains($media, ['drive.google.com', 'dropbox.com', 'box.com', ])) ){
                usleep(rand(0, 3) * 1000000); // random wait
            }

            $this->lastMedia = $mediaHost;
        }

        try {
            $media = $this->getDriveFileResponseOrLink($media);
        } catch (\Exception $exception){
            throw new \Exception($exception->getMessage());
        }

        $mimes = new MimeTypes;

        $randomProxy = null;
        $guzzleOpts = [
            'timeout' => 60 * 5,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36', // chrome
            ],
        ];
        
        if($media && is_string($media)) {
            if(app()->environment('production')) {
                // set random proxy; required for Google Drive links
                // needed because a lot of requests can result in getting temporarily rate-limited
                $randomProxy = array_random(config('app.proxies'));
            } else {
                \Log::info('Bulk Import: skipping using a random proxy for google drive link because its not production env');
            }

            // options to pass to guzzle
            // we also set header to mimic a normal browser
            $guzzleOpts = array_merge($guzzleOpts, array_filter([
                'proxy' => $randomProxy,
            ]));
        }

        // guzzle for fetching media file
        $client = guzzle_client($guzzleOpts);
        
        $mediaExt = null;
        if($media){
            if($media instanceof \GuzzleHttp\Psr7\Response) {
                // this is a Google Drive file we have using the drive sdk
                $response = $media;
            } else {
                // normal links
                $tries = 0;
                while(true){
                    try {
                        $response = $client->head($media);
                    } catch (\Exception $exception){
                        $tries++;
                        if($tries < 5){
                            // we do some retries
                            continue;
                        } else {
                            throw new \Exception('Unable to fetch media: ' . $exception->getMessage());
                        }
                    }
                    break;
                }
            }
            $mediaMime = $response->getHeader('content-type');
            if(!empty($mediaMime)) $mediaMime = $mediaMime[0];
            if(!$mediaMime){
                throw new \Exception('Unable to determine type of media: ' . $media);
            }
            $mediaExt = $mimes->getExtension($mediaMime);
        }

        $temp = tempnam(sys_get_temp_dir(), config('app.name'));

        if($media instanceof \GuzzleHttp\Psr7\Response){

            // responses, g-drive files
            try {
                $outHandle = fopen($temp, 'w+');

                // Until we have reached the EOF, read 1024 bytes at a time and write to the output file handle.
                while (!$media->getBody()->eof()) {
                    fwrite($outHandle, $media->getBody()->read(1024));
                }

                // Close the output file handle.
                fclose($outHandle);

            } catch (\Exception $exception){
                @unlink($temp);
                throw new \Exception($exception->getMessage(), 0, $exception);
            }

        } else {

            // normal links
            try {
                $client->get($media, [
                    'sink' => $temp,
                ]);
            } catch (\Exception $exception) {
                @unlink($temp);
                throw new \Exception($exception->getMessage(), 0, $exception);
            }

        }
        return new UploadedFile($temp, 'file.' . $mediaExt, $mediaMime);
    }

    /**
     * validate and convert the date format to the preferred format
     * @param string $date
     * @param string $format Datetime format
     * @return string $date
     * @throws \Exception
     */
    public function getFormattedDate(string $date, string $format = 'd/m/Y h:ia')
    {
        $DATE_FORMATS = [
            'd/m/Y h:ia',
            'd-m-Y h:ia',
        ];
        $cb = null;
        // the first format is the preferred one
        foreach ($DATE_FORMATS as $index => $f) {
            $cb = Carbon::createFromFormat($f, $date, timezone());
            $cb = $cb->format($format); // also update the data in case the format is different from preferred

            break; // if success, no need to try further formats
        }
        return $cb;
    }

    /**
     * Get the Google Drive file response if the link is a Google Drive link or return the link as it is
     * @param $link
     * @return string|\GuzzleHttp\Psr7\Response|\Google\Service\Drive\DriveFile
     * @throws \Exception
     */
    public function getDriveFileResponseOrLink($link){

        if(!filter_var($link, FILTER_VALIDATE_URL)) {
            return $link;
        }

        if(Str::contains($link, 'drive.google.com')){

            // drive file id
            $id = null;

            if(Str::contains($link, '/d/')){
                // example link: https://drive.google.com/file/d/1Bc1x9PzUCPOfAoB4TbnoC9diZoZ6P0Ut/view?usp=sharing
                $link_parts = explode('/d/', $link);
                if(count($link_parts) > 1){
                    $id = explode('/', $link_parts[1])[0];
                }
            } else if(Str::contains($link, 'id=')){
                // example link: https://drive.google.com/uc?id=1zKP0EoEOzvSWkHMoboNvLwaSJ4uFHZKo&export=download
                $query_str = parse_url($link, PHP_URL_QUERY);
                parse_str($query_str, $query_params);
                if(isset($query_params['id'])){
                    $id = $query_params['id'];
                }
            }

            if(!$id){
                return $link;
            }

            try {

                // get download link using php sdk
                $google = ApiHelper::getGoogle();

                $google->setAuthConfig(storage_path('google_drive_service.json'));

                $google->addScope(Google_Service_Drive::DRIVE);

                $service = new Google_Service_Drive($google);

                // returns \GuzzleHttp\Psr7\Response
                return $service->files->get($id, [
                    'alt' => 'media' // this returns the response body and not the File object
                ]);

            } catch (\Google_Exception $e) {

                if(Str::contains($e->getMessage(), 'File not found')){
                    throw new \Exception('File not found on Google Drive. Make sure the link is correct and public.');
                }

                report($e);
                $link = 'https://drive.google.com/uc?export=download&id=' . $id; // last resort, works sometimes for low number of requests
            }

        }

        return $link;
    }

    /**
     * @param $value
     * @return array|null
     */
    private function getAccountTypes($value){
        $MAP = [
            'facebook' => 'facebook.page',
            'facebook page' => 'facebook.page',
            'facebook group' => 'facebook.group',

            'instagram' => ['instagram.direct', 'instagram.api'],

            'twitter' => 'twitter.profile',

            'linkedin profile' => 'linkedin.profile',
            'linkedin page' => 'linkedin.org',
            'linkedin organization' => 'linkedin.org',
            'linkedin brand' => 'linkedin.brand',
            'linkedin' => [
                'linkedin.org',
                'linkedin.profile',
                'linkedin.brand',
            ],

            'google my business' => 'google.location',
            'google location' => 'google.location',
            'gmb' => 'google.location',
            'google business profile' => 'google.location',
            'gbp' => 'google.location',
            'google maps' => 'google.location',

            'mastodon' => 'mastodon.profile',

            'tiktok profile' => 'tiktok.profile',
            'tiktok account' => 'tiktok.profile',
            'tiktok' => 'tiktok.profile',

            'youtube' => 'google.youtube',
            'youtube channel' => 'google.youtube',
            'youtube account' => 'google.youtube',
            'youtube profile' => 'google.youtube',

            'reddit' => ['reddit.profile', 'reddit.subreddit'],
            'reddit profile' => 'reddit.profile',
            'reddit subreddit' => 'reddit.subreddit',

            'pinterest' => 'pinterest.profile',
            'pinterest profile' => 'pinterest.profile',
            'pinterest business' => 'pinterest.profile',
            'pinterest account' => 'pinterest.profile',

            'threads' => 'threads.profile',
            'threads account' => 'threads.profile',
            'threads profile' => 'threads.profile',

            'bluesky' => 'bluesky.profile',
            'bluesky account' => 'bluesky.profile',
            'bluesky profile' => 'bluesky.profile',
        ];
        $type = trim(strtolower($value));
        $val = isset($MAP[$type]) ? $MAP[$type] : null;
        if($val){
            if(!is_array($val)) $val = [$val];
            return $val;
        }
        return null;
    }

    /**
     * @param array $item
     * @param array $accountTypes
     * @return array
     */
    private function getPostOptions(array $item, array $accountTypes = []): array
    {
        $options = [];

        // exclude normal columns and treat other columns as options
        $excluded = [
            'account',
            'account_type',
            'publish_at',
            'add_to_queue',
        ];

        foreach($item as $key => $val){
            if(!in_array($key, $excluded)){
                // \Log::info('Bulk Import: Option: ' . $key . ' => ');
                $options[$key] = $val;
            } else {
                continue;
            }

            if($key === 'post_as_story' && (strtolower($val) == 'yes' || $val === 'TRUE')){
                $options[$key] = true;
            } else if($key === 'first_comment'){
                $key = 'comment';
                $options[$key] = $val;
            } else if($key === 'privacy_status' && in_array('tiktok.profile', $accountTypes)){ // for TikTok
                if(in_array(strtolower($val), ['public', 'public to everyone', 'public_to_everyone'])){
                    $options[$key] = 'PUBLIC_TO_EVERYONE';
                }else if(in_array(strtolower($val), ['mutual','mutual follow friends', 'mutual_follow_friends'])){
                    $options[$key] = 'MUTUAL_FOLLOW_FRIENDS';
                }else if(in_array(strtolower($val), ['follower','followers', 'followers of creator','follower of creator', 'follower_of_creator'])){
                    $options[$key] = 'FOLLOWER_OF_CREATOR';
                }else if(in_array(strtolower($val), ['self','self only', 'self_only'])){
                    $options[$key] = 'SELF_ONLY';
                }
            }
        }

        // gmb specific options
        $gmbOptions = [
            'call_to_action',
            'call_to_action_url',
            'topic_type',
            'event_title',
            'event_start',
            'event_end',
            'offer_coupon',
            'offer_link',
            'offer_terms'
        ];
        foreach($gmbOptions as $opt){
            if(in_array($opt, array_keys($item)) && isset($item[$opt])){
                if($opt === 'call_to_action' || $opt === 'topic_type'){
                    if($opt === 'topic_type' && strtolower($item[$opt]) === 'standard'){
                        $options[$opt] = null;
                    }else {
                        $options[$opt] = strtoupper(str_replace(" ", "_", $item[$opt])); //needed for validations in $account->publishPost()
                    }
                }else {
                    $options[$opt] = $item[$opt];
                }
            }else {
                $options[$opt] = null; //needed for validations
            }
        }

        return $options;
    }

    /**
     * @return User|\Illuminate\Contracts\Auth\Authenticatable
     */
    private function user(){
        if(!$this->user)
            $this->user = \Auth::user();
        return $this->user;
    }

    /**
     * @return Collection
     */
    private function accounts(){
        if(!$this->cachedAccounts){
            $this->cachedAccounts = $this->user()->getAvailableAccounts(['*'], true)->map(function($acc){
                $acc->name = mb_strtolower($acc->name);
                return $acc;
            });
        }
        return $this->cachedAccounts;
    }

    public function getCount(){
        return $this->count;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

}
