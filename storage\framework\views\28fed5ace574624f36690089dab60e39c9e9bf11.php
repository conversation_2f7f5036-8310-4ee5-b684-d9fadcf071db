<?php $__env->startSection('title', 'Accounts | ' . config('app.name')); ?>
<?php $__env->startSection('content'); ?>

    <div class="row">
        <div class="col-12">
            <div class="mb-4 d-flex align-items-center justify-content-between">

                <h3 class="">
                    <?php echo app('translator')->get('generic.accounts'); ?>
                </h3>
                <div class="float-right">
                    <?php if($show == 'inactive'): ?>
                        <a class="btn btn-outline-light btn-sm mr-2" href="<?php echo e(route('accounts.index')); ?>">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" checked="checked" class="show-inactive rounded mr-2">
                                Show all
                            </div>
                        </a>
                    <?php else: ?>
                        <a class="btn btn-outline-light btn-sm mr-2" href="<?php echo e(route('accounts.index')); ?>?show=inactive">     
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="show-inactive rounded mr-2">
                                Show Inactive
                            </div>                               
                        </a>
                    <?php endif; ?>
                    <button class="btn btn-light btn-sm" type="button" data-toggle="modal" data-target="#add_account_modal">
                        <div class="d-flex align-items-center">

                            <i class="ph ph-bold ph-plus mr-2" title="<?php echo app('translator')->get('accounts.add'); ?>"></i> <?php echo app('translator')->get('accounts.add'); ?>
                        </div>
                    </button>
                </div>
            </div>
            <?php if(empty($accounts) || $accounts->count() == 0): ?>
                <div class="card pt-8" data-tour="id=no_accounts_yet;title=Your Accounts;text=When you add your social media accounts, they will show up here;position=top">
                    <div class="card-body">
                        <div class="text-center">
                            <?php if($show == 'inactive'): ?>
                                <h3>Great, you have no inactive account</h3>
                                <p>If an account starts having problems or its link gets broken, you will be notified and it will show here.</p>
                            <?php else: ?>
                                <h4 class="font-weight-400">Looks like you have no account</h4>
                                <p class="mb-5">
                                    Add an account to start.
                                </p>
                                <button class="btn btn-primary btn-sm" type="button" data-toggle="modal" data-target="#add_account_modal">
                                    <i class="ph-bold ph-plus"></i> Add An Account
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive table-border rounded-lg">
                        <table class="table table-hover mb-0">
                            <thead class="card-header">
                                <tr>
                                    <th class="py-3 pl-5" scope="col"><?php echo app('translator')->get('generic.name'); ?></th>
                                    <th class="py-3" scope="col"><?php echo app('translator')->get('generic.type'); ?></th>
                                    <th class="py-3 pr-5" scope="col"><?php echo app('translator')->get('generic.status'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $accounts->items(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="cursor-pointer" role="button" data-toggle="modal" data-target="#edit_account_modal"
                                    data-id="<?php echo e($acc->id); ?>">
                                    <td class="pl-5" >
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo e($acc->getProfilePic(false, true)); ?>" class="avatar avatar-xs lozad mr-2" />
                                            <span data-toggle="tooltip" title="<?php echo e($acc->name); ?>"><?php echo e((strlen($acc->name) > 22) ? substr($acc->name , 0, 22) . '...' : $acc->name); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img class="rounded-circle position-relative mr-2 account-network lozad" src='/images/redesign/networks/<?php echo e(explode(".",$acc->type)[1] == 'youtube' ? 'youtube' :  explode(".",$acc->type)[0]); ?>-circular.svg' />
                                            <span><?php echo e($acc->getType()); ?></span>
                                        </div>
                                    </td>
                                    <!-- <td><?php echo e($acc->updated_at->diffForHumans()); ?></td> -->
                                    <td class="pr-5">
                                        <i class="ph ph-lg mr-2 <?php echo e($acc->active ? "ph-check text-success" : "ph-x text-danger"); ?>"></i> <?php echo e($acc->active ? 'Connected' : 'Disconnected'); ?>

                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
                <?php if($accounts->hasPages()): ?>
                    <div class="d-flex justify-content-end pt-2 pb-0">
                        <?php echo e($accounts->appends(array_filter(['show' => $show === 'inactive' ? $show : null, ]))->links()); ?>

                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <div id="add_account_modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-dialog-slideout right custom-modal-width">
            <div class="modal-content p-5">
                <div class="modal-header p-0 mb-4">
                    <h5 class="modal-title">Select platform</h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x ph-md"></i></button>
                </div>
                <div class="modal-body p-0">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item py-4 px-0">

                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'facebook'])); ?>"
                               data-provider="facebook"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 facebook d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/facebook-circular.svg">
                                    Facebook (Pages)
                                </h6>
                            </a>

                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'twitter'])); ?>"
                               data-provider="twitter"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 twitter d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/twitter-circular.svg">
                                    X/Twitter (Profiles)
                                </h6>
                            </a>
                        </li>

                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'instagram'])); ?>"
                                data-provider="instagram"
                                class="hide_modal_onclick">
                                 <h6 class="font-weight-400 mb-0 instagram d-flex align-items-center">
                                     <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/instagram-circular.svg">
                                     Instagram (Business Accounts)
                                 </h6>
                             </a>
                        </li>

                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'linkedin'])); ?>"
                               data-provider="linkedin"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 linkedin d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/linkedin-circular.svg">
                                    LinkedIn (Profiles, Brands, Organizations)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'google', 'service' => 'gmb'])); ?>"
                               data-provider="gmb"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 google-business-profile d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/google-circular.svg">
                                    Google Business Profile (Locations)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="#" data-toggle="modal" data-target="#add_mastodon_modal"
                               data-provider="mastodon"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 mastodon d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/mastodon-circular.svg">
                                    Mastodon (Profiles)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'tiktok'])); ?>"
                               data-provider="tiktok"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 tiktok d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/tiktok-circular.svg">
                                    TikTok (Profiles)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'threads'])); ?>"
                               data-provider="threads"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 threads d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/threads-circular.svg">
                                    Threads (Profiles)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'pinterest'])); ?>"
                               data-provider="pinterest"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 pinterest d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/pinterest-circular.svg">
                                    Pinterest (Profiles)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="#" data-toggle="modal" data-target="#add_youtube_modal"
                               data-provider="youtube"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0 youtube d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/youtube-circular.svg">
                                    YouTube (Channels)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="<?php echo e(route('accounts.auth', ['provider' => 'reddit'])); ?>"
                               data-provider="reddit"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400  mb-0 reddit d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/reddit-circular.svg">
                                    Reddit (Profiles, Subreddits)
                                </h6>
                            </a>
                        </li>
                        <li class="list-group-item py-4 px-0">
                            <a href="#" data-toggle="modal" data-target="#add_bluesky_modal"
                               data-provider="bluesky"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400  mb-0 bluesky d-flex align-items-center">
                                    <img class="rounded-circle position-relative network-icon lozad mr-2" src="/images/redesign/networks/bluesky-circular.svg">
                                    Bluesky (Profiles)
                                </h6>
                            </a>
                        </li>
                    </ul>
                    <div class="divider mt-0 mb-2">
                        or
                    </div>
                    <button type="button" class="text-dark btn btn-link font-weight-400 p-0" data-dismiss="modal" id="connect_using_link">
                            <i class="ph ph-link mr-1"></i>
                            Let someone else connect the accounts
                        </button>
                </div>
                <div class="modal-footer">
                        <p>Need assistance? Take a look at this <a href="https://help.socialbu.com/article/72-adding-a-facebook-page" data-beacon-article-modal="5ea057e004286364bc98cb60">helpful guide</a>.</p>

                </div>
            </div>
        </div>
    </div>

    <div id="edit_account_modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content invisible">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo app('translator')->get('accounts.edit'); ?>
                        <small id="shared_with">
                            <?php echo app('translator')->get('accounts.shared'); ?> <span class="badge" id="shared_with_count"></span>
                        </small>
                    </h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x ph-md"></i></button>
                </div>
                <form action="" method="POST">
                    <div class="modal-body">
                        <?php echo e(csrf_field()); ?>

                        <?php echo e(method_field('PATCH')); ?>

                        <div class="form-group">
                            <label for="input_account_name">
                                <?php echo app('translator')->get('generic.name'); ?>
                            </label>
                            <input name="name" type="text" class="form-control input_account_name"
                                   id="input_account_name" placeholder="<?php echo app('translator')->get('generic.name'); ?>" value=""/>
                        </div>

                    </div>
                    <div class="modal-footer d-flex justify-content-between">
                        <div class="_buttons">
                            <button type="button" class="btn btn-danger btn-sm _delete mr-1"><?php echo app('translator')->get('generic.delete'); ?></button>
                            <button type="button" class="btn btn-light btn-sm _test"><?php echo app('translator')->get('accounts.test_connection'); ?></button>
                        </div>
                        <div class="_buttons">
                            <button type="button" class="btn btn-info btn-sm _reconnect mr-1"><?php echo app('translator')->get('accounts.reconnect'); ?></button>
                            <button type="submit" class="btn btn-primary btn-sm"><?php echo app('translator')->get('generic.save'); ?></button>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>

    <?php echo $__env->make('user.accounts.partials.connect-popups', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\socialtool\resources\views/user/accounts/index.blade.php ENDPATH**/ ?>