<?php

namespace App\Console\Commands;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Helpers\LinkedInClient;
use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use App\Account;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Helpers\ApiHelper;
use Revolution\Mastodon\MastodonClient;

class GetAccountMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounts:metrics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get account metrics';

    private static $skipErrorsContaining = [
        'limit reached',
        'timed out',
        'connection refused',
        'connection reset',
        'unreachable',
        'unknown error',
        'retry your request',
        'connection timeout',
        'does not exist', // when cannot access a post/object
        'unknown ssl protocol',
        'null returned', // strange mastodon client error where some requests don't return any response, so it throws exception
        'on null', // LinkedIn: Call to a member function getBody() on null
        'internal error', // google sometimes
    ];

    // keep count and we only process 50 requests per run
    private $redditRequests = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        $startTime = now();

        $accounts = Account::where('active', true)
            ->whereIn('type', [
                'facebook.page',

                'twitter.profile',

                'instagram.api',

                // 'linkedin.profile',
                'linkedin.org',
                'linkedin.brand',

                'google.location',

                'mastodon.profile',

                'google.youtube',
                'pinterest.profile',

                'reddit.subreddit',
                'reddit.profile',

                'threads.profile',
                'bluesky.profile',
            ])
            ->where(function(/** @var Builder $query */ $query){
                // where last fetched was x hrs ago at least
                return $query->whereRaw('`insights_fetched_at` <= now() - interval 24 hour')
                    ->orWhere('insights_fetched_at', null);
            })
            ->orderBy('insights_fetched_at') // so we get rows that need update first
            ->limit(1000) // limit to 1000 rows per run
            ->get();

        $accounts->each(function (/** @var Account $account */ $account) use(&$startTime){

            if (Str::contains($account->type, 'reddit')) {
                $this->redditRequests++;
                if ($this->redditRequests > 50) {
                    return true; // skip for now
                }
            }

            $this->info('Fetching data for account #' . $account->id);

            try {
                $metrics = $this->{'process' . studly_case(str_replace('.', '_', $account->type))}($account);

                $rows = [];

                if($metrics && is_array($metrics) && count($metrics)){
                    foreach ($metrics as $metric => $value) {
                        if(is_array($value)){
                            // we have metric value and timestamp, both
                            $metric_value = $value['value'];
                            $metric_timestamp = $value['timestamp'];
                        } else {
                            // we have only metric value
                            $metric_value = $value;
                            $metric_timestamp = now();
                        }

                        $metric_value = (int) $metric_value;

                        if($metric_value < 0){
                            // we cannot have negative values
                            $metric_value = 0;
                        }

                        $rows[] = [
                            'account_id' => $account->id,
                            'metric_type' => $metric,
                            'metric_value' => (int) $metric_value,
                            'timestamp' => $metric_timestamp,
                        ];
                    }

                    $insert = get_insights_db()->table('account_metrics')->insert($rows);

                    if (!$insert) {
                        $this->error('Error while inserting row');
                        report(new \Exception('Database insert failed'));
                    } else {
                        // update column
                        $account->insights_fetched_at = now();
                        $account->save();
                    }

                }
            } catch (\Exception $exception){
                $this->error($exception->getMessage());
                // what to do?
                if(!Str::contains(strtolower($exception->getMessage()), ['limit reached', 'timed out', 'server error', 'unreachable','An unknown error occurred']) && $account->testConnection(true, 'Error while fetching account metrics')){
                    report($exception);
                }
            }

            if(now()->diffInMinutes($startTime) >= 20) {
                // we have been running for 20 minutes, so stop
                $this->info('Stopping as we have been running for 20 minutes');
                return false;
            }

            return true;
        });
    }

    /**
     * @param Account $account
     * @return array
     * @throws FacebookSDKException
     */
    public function processInstagramApi(Account $account){
        /** @var Facebook $fb */
        $fb = $account->getApi();

        $data = [];

        try {
            $res = $fb->get('/' . $account->account_id . '?fields=followers_count')->getGraphNode();
            if(!$res){
                return [];
            }
        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        if(isset($res['followers_count'])){
            $data['followers'] = $res['followers_count'];
        }

        if($account->getOption('no_insights_access')){
            // if we don't have access to insights, we can't get any more data
            return $data;
        }

        $daily_metrics = [
            'impressions',
            'reach',
        ];

        try {
            $res = $fb->get('/' . $account->account_id . '/insights?period=day&metric=' . implode(',', $daily_metrics))->getDecodedBody();
            if($res){
                foreach($res['data'] as $metric){
                    $data[$metric['name']] = [
                        'value' => $metric['values'][0]['value'], // yesterday's value
                        'timestamp' => Carbon::parse($metric['values'][0]['end_time']), // so we pass the custom timestamp
                    ];
                }
            }
        } catch(\Exception $exception){
            if(Str::contains($exception->getMessage(), 'does not have permission')){
                $account->setOption('no_insights_access', true);
            } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                report($exception);
            }
        }

        //lifetime Does not include current day's data.
        $lifetime_metrics_breakdowns = [
            // breakdowns
            'country',
            'age,gender', //lifetime Does not include current day's data.
        ];

        try {
            foreach($lifetime_metrics_breakdowns as $breakdown){
                $res = $fb->get('/' . $account->account_id . '/insights?period=lifetime&metric=follower_demographics&breakdown=' . $breakdown . '&metric_type=total_value&fields=name,total_value')->getDecodedBody();
                if($res && !empty($res['data'])){
                    $resData = $res['data'][0];
                    $breakdownData = $resData['total_value']['breakdowns'][0]['results'];
                    if($breakdown === 'country'){
                        foreach($breakdownData as $datum){
                            $key = implode('_', $datum['dimension_values']);
                            $data['followers_by_country/' . $key] = $datum['value'];
                        }
                    } else if($breakdown === 'age,gender') {
                        foreach($breakdownData as $datum){
                            $key = implode('_', $datum['dimension_values']);
                            $data['followers_by_gender_age/' . $key] = $datum['value'];
                        }
                    }
                }
            }
        } catch(\Exception $exception){
            if(Str::contains($exception->getMessage(), 'does not have permission')){
                $account->setOption('no_insights_access', true);
            } else if(!Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                report($exception);
            }
        }

        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processFacebookPage(Account $account){

        if($account->getOption('no_insights_access')){
            // if we don't have access to insights, we can't get any more data
            return [];
        }

        /** @var Facebook $fb */
        $fb = $account->getApi();

        $daily_metrics = [
            'page_total_actions',
            'page_post_engagements',

            'page_fan_adds_by_paid_non_paid_unique',

            'page_impressions',
            'page_impressions_paid',
            'page_impressions_viral',

            'page_fans',
            'page_fans_country', // same as instagram
            'page_fan_adds',
            'page_fan_removes', // unlikes

            'page_video_views',
            'page_video_views_paid',
            'page_video_views_organic',
            'page_video_complete_views_30s',
            'page_video_view_time',

            'page_views_total',
        ];

        try {
            $res = $fb->get('/' . $account->account_id . '/insights?period=day&metric=' . implode(',', $daily_metrics))->getDecodedBody();
            if(!$res || !isset($res['data'])){
                return [];
            }
        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            } else if (Str::contains($exception->getMessage(), '#190')){
                $account->setOption('no_insights_access', true);
                return [];
            }
            if(Str::contains(strtolower($exception->getMessage()), ['does not have sufficient administrative permission', 'business requires Two Factor Authentication'])){
                $account->setOption('insights_permission_error_at', time());
                return [];
            }
            throw $exception;
        }

        $data = [];
        foreach($res['data'] as $metric){
            if($metric['name'] === 'page_fans_online'){
                $values = $metric['values'][0]['value'];
                foreach($values as $hour => $count){
                    $data['fans_online/' . $hour] = [
                        'value' => $count,
                        'timestamp' => Carbon::parse($metric['values'][0]['end_time']), // we pass custom timestamp because the data is for yesterday
                    ];
                }
            } else if($metric['name'] === 'page_fans_country'){
                $values = $metric['values'][0]['value'];
                foreach($values as $countryCode => $count){
                    $data['fans_by_country/' . $countryCode] = [
                        'value' => $count,
                        'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
                    ];
                }
            } else if($metric['name'] === 'page_fans_gender_age'){
                $values = $metric['values'][0]['value'];
                foreach($values as $genderAgeCode => $count){
                    $data['fans_by_gender_age/' . $genderAgeCode] = [
                        'value' => $count,
                        'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
                    ];
                }
            }  else if($metric['name'] === 'page_fan_adds_by_paid_non_paid_unique'){
                $values = $metric['values'][0]['value'];
                foreach($values as $type => $count){
                    $data['fan_adds_by_paid_non_paid_unique/' . $type] = [
                        'value' => $count,
                        'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
                    ];
                }
            } else {
                $key = str_replace('page_', '', $metric['name']);
                $data[$key] = [
                    'value' => $metric['values'][0]['value'],
                    'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
                ];
            }
        }

        $lifetime_metrics = [
            'post_clicks',
            'post_video_avg_time_watched',
        ];

        try {
            $res = $fb->get('/' . $account->account_id . '/insights?period=lifetime&metric=' . implode(',', $lifetime_metrics))->getDecodedBody();
            if(!$res){
                return [];
            }
        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            if(Str::contains($exception->getMessage(), ['#190'])){
                // weird fb token error
                // (#190) This method must be called with a Page Access Token
                if($account->testConnection(true, 'Facebook bug')){
                    // just ignore
                    $res['data'] = [];
                } else {
                    return [];
                }
            } else {
                throw $exception;
            }
        }

        foreach($res['data'] as $metric){
            $key = str_replace('page_', '', $metric['name']);
            $data[$key] = [
                'value' => $metric['values'][0]['value'],
                'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
            ];
        }

        return $data;

    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processTwitterProfile(Account $account){

        /** @var TwitterOAuth $tw */
        $tw = $account->getApi();

        $login_via_new_x_api = $account->getOption('login_via_new_x_api');

        if($login_via_new_x_api){
            // need to use v2 endpoints
            $tw->setApiVersion('2');

            try {
                $res = $tw->get("users/me", ['user.fields' => 'public_metrics,verified_followers_count']);
            } catch (\Exception $exception){
                if(Str::contains(strtolower($exception->getMessage()), 'unknown ssl')){
                    return [];
                }
                throw $exception;
            }

            if($tw->getLastHttpCode() == 503){
                return [];
            } else if ($tw->getLastHttpCode() !== 200) {
                throw new \Exception('Twitter returned error ' . $tw->getLastHttpCode());
            }

            $data = [];

            $responseData = $res->data ?? null;

            if(!$responseData){
                // no data returned, so we cannot get any metrics
                throw new \Exception('Twitter returned error ' . $tw->getLastHttpCode() . ' - no data returned');
            }

            if(isset($responseData->verified_followers_count)){
                $data['verified_followers'] = $responseData->verified_followers_count;
            }

            if(isset($responseData->public_metrics)){
                $data['followers'] = $responseData->public_metrics->followers_count ?? 0;
            }

            return $data;

        } else {

            try {
                $res = $tw->get("account/verify_credentials");
            } catch (\Exception $exception){
                if(Str::contains(strtolower($exception->getMessage()), 'unknown ssl')){
                    return [];
                }
                throw $exception;
            }

            if($tw->getLastHttpCode() == 503){
                return [];
            } else if ($tw->getLastHttpCode() !== 200) {
                throw new \Exception('Twitter returned error ' . $tw->getLastHttpCode());
            }

            $followers = $res->followers_count;

            return [
                'followers' => $followers
            ];
        }

    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processMastodonProfile(Account $account){

        /** @var MastodonClient $m */
        $m = $account->getApi();

        try {
            $res = $m->get("/accounts/verify_credentials");
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        $r = $m->getResponse();
        if($r->getStatusCode() === 503){
            return [];
        } else if ($r->getStatusCode() !== 200) {
            throw new \Exception('Mastodon returned error ' . $r->getStatusCode());
        }

        return [
            'followers' => $res['followers_count'],
        ];
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     * @throws \Throwable
     */
    public function processLinkedinOrg(Account $account){

        /** @var LinkedInClient $li */
        $li = $account->getApi();
        $li->useRest(); // use new api path

        $accountUrn = 'urn:li:organization:' . $account->account_id;

        $data = [];

        try {
            $res = $li->get('networkSizes/' . urlencode($accountUrn) . '?edgeType=COMPANY_FOLLOWED_BY_MEMBER');
        } catch(\Throwable $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        $data['followers'] = $res['firstDegreeSize'];

        $seniorities = [
            'urn:li:seniority:1' => 'unpaid',
            'urn:li:seniority:2' => 'training',
            'urn:li:seniority:3' => 'entry',
            'urn:li:seniority:4' => 'senior',
            'urn:li:seniority:5' => 'manager',
            'urn:li:seniority:6' => 'director',
            'urn:li:seniority:7' => 'vp',
            'urn:li:seniority:8' => 'cxo',
            'urn:li:seniority:9' => 'partner',
            'urn:li:seniority:10' => 'owner',
        ];
        $functions = [
            'urn:li:function:1' => 'accounting',
            'urn:li:function:2' => 'administrative',
            'urn:li:function:3' => 'arts_and_design',
            'urn:li:function:4' => 'business_development',
            'urn:li:function:5' => 'community_and_social_services',
            'urn:li:function:6' => 'consulting',
            'urn:li:function:7' => 'education',
            'urn:li:function:8' => 'engineering',
            'urn:li:function:9' => 'entrepreneurship',
            'urn:li:function:10' => 'finance',
            'urn:li:function:11' => 'healthcare_services',
            'urn:li:function:12' => 'human_resources',
            'urn:li:function:13' => 'information_technology',
            'urn:li:function:14' => 'legal',
            'urn:li:function:15' => 'marketing',
            'urn:li:function:16' => 'media_and_communication',
            'urn:li:function:17' => 'military_and_protective_services',
            'urn:li:function:18' => 'operations',
            'urn:li:function:19' => 'product_management',
            'urn:li:function:20' => 'program_and_project_management',
            'urn:li:function:21' => 'purchasing',
            'urn:li:function:22' => 'quality_assurance',
            'urn:li:function:23' => 'real_estate',
            'urn:li:function:24' => 'research',
            'urn:li:function:25' => 'sales',
            'urn:li:function:26' => 'customer_success_and_support',
        ];
        $industries = [
            'urn:li:industry:1' => 'defense_&_space',
            'urn:li:industry:3' => 'computer_hardware',
            'urn:li:industry:4' => 'computer_software',
            'urn:li:industry:5' => 'computer_networking',
            'urn:li:industry:6' => 'internet',
            'urn:li:industry:7' => 'semiconductors',
            'urn:li:industry:8' => 'telecommunications',
            'urn:li:industry:9' => 'law_practice',
            'urn:li:industry:10' => 'legal_services',
            'urn:li:industry:11' => 'management_consulting',
            'urn:li:industry:12' => 'biotechnology',
            'urn:li:industry:13' => 'medical_practice',
            'urn:li:industry:14' => 'hospital_&_health_care',
            'urn:li:industry:15' => 'pharmaceuticals',
            'urn:li:industry:16' => 'veterinary',
            'urn:li:industry:17' => 'medical_device',
            'urn:li:industry:18' => 'cosmetics',
            'urn:li:industry:19' => 'apparel_&_fashion',
            'urn:li:industry:20' => 'sporting_goods',
            'urn:li:industry:21' => 'tobacco',
            'urn:li:industry:22' => 'supermarkets',
            'urn:li:industry:23' => 'food_production',
            'urn:li:industry:24' => 'consumer_electronics',
            'urn:li:industry:25' => 'consumer_goods',
            'urn:li:industry:26' => 'furniture',
            'urn:li:industry:27' => 'retail',
            'urn:li:industry:28' => 'entertainment',
            'urn:li:industry:29' => 'gambling_&_casinos',
            'urn:li:industry:30' => 'leisure,_travel_&_tourism',
            'urn:li:industry:31' => 'hospitality',
            'urn:li:industry:32' => 'restaurants',
            'urn:li:industry:33' => 'sports',
            'urn:li:industry:34' => 'food_&_beverages',
            'urn:li:industry:35' => 'motion_pictures_&_film',
            'urn:li:industry:36' => 'broadcast_media',
            'urn:li:industry:37' => 'museums_&_institutions',
            'urn:li:industry:38' => 'fine_art',
            'urn:li:industry:39' => 'performing_arts',
            'urn:li:industry:40' => 'recreational_facilities_&_services',
            'urn:li:industry:41' => 'banking',
            'urn:li:industry:42' => 'insurance',
            'urn:li:industry:43' => 'financial_services',
            'urn:li:industry:44' => 'real_estate',
            'urn:li:industry:45' => 'investment_banking',
            'urn:li:industry:46' => 'investment_management',
            'urn:li:industry:47' => 'accounting',
            'urn:li:industry:48' => 'construction',
            'urn:li:industry:49' => 'building_materials',
            'urn:li:industry:50' => 'architecture_&_planning',
            'urn:li:industry:51' => 'civil_engineering',
            'urn:li:industry:52' => 'aviation_&_aerospace',
            'urn:li:industry:53' => 'automotive',
            'urn:li:industry:54' => 'chemicals',
            'urn:li:industry:55' => 'machinery',
            'urn:li:industry:56' => 'mining_&_metals',
            'urn:li:industry:57' => 'oil_&_energy',
            'urn:li:industry:58' => 'shipbuilding',
            'urn:li:industry:59' => 'utilities',
            'urn:li:industry:3131' => 'mobile_games',
            'urn:li:industry:60' => 'textiles',
            'urn:li:industry:61' => 'paper_&_forest_products',
            'urn:li:industry:62' => 'railroad_manufacture',
            'urn:li:industry:63' => 'farming',
            'urn:li:industry:64' => 'ranching',
            'urn:li:industry:65' => 'dairy',
            'urn:li:industry:66' => 'fishery',
            'urn:li:industry:67' => 'primary/secondary_education',
            'urn:li:industry:68' => 'higher_education',
            'urn:li:industry:69' => 'education_management',
            'urn:li:industry:70' => 'research',
            'urn:li:industry:71' => 'military',
            'urn:li:industry:72' => 'legislative_office',
            'urn:li:industry:73' => 'judiciary',
            'urn:li:industry:74' => 'international_affairs',
            'urn:li:industry:75' => 'government_administration',
            'urn:li:industry:76' => 'executive_office',
            'urn:li:industry:77' => 'law_enforcement',
            'urn:li:industry:78' => 'public_safety',
            'urn:li:industry:79' => 'public_policy',
            'urn:li:industry:80' => 'marketing_&_advertising',
            'urn:li:industry:81' => 'newspapers',
            'urn:li:industry:82' => 'publishing',
            'urn:li:industry:83' => 'printing',
            'urn:li:industry:84' => 'information_services',
            'urn:li:industry:85' => 'libraries',
            'urn:li:industry:86' => 'environmental_services',
            'urn:li:industry:87' => 'package/freight_delivery',
            'urn:li:industry:88' => 'individual_&_family_services',
            'urn:li:industry:89' => 'religious_institutions',
            'urn:li:industry:90' => 'civic_&_social_organization',
            'urn:li:industry:91' => 'consumer_services',
            'urn:li:industry:92' => 'transportation/trucking/railroad',
            'urn:li:industry:93' => 'warehousing',
            'urn:li:industry:94' => 'airlines/aviation',
            'urn:li:industry:95' => 'maritime',
            'urn:li:industry:96' => 'information_technology_&_services',
            'urn:li:industry:97' => 'market_research',
            'urn:li:industry:98' => 'public_relations_&_communications',
            'urn:li:industry:99' => 'design',
            'urn:li:industry:100' => 'non-profit_organization_management',
            'urn:li:industry:101' => 'fundraising',
            'urn:li:industry:102' => 'program_development',
            'urn:li:industry:103' => 'writing_&_editing',
            'urn:li:industry:104' => 'staffing_&_recruiting',
            'urn:li:industry:105' => 'professional_training_&_coaching',
            'urn:li:industry:106' => 'venture_capital_&_private_equity',
            'urn:li:industry:107' => 'political_organization',
            'urn:li:industry:108' => 'translation_&_localization',
            'urn:li:industry:109' => 'computer_games',
            'urn:li:industry:110' => 'events_services',
            'urn:li:industry:111' => 'arts_&_crafts',
            'urn:li:industry:112' => 'electrical_&_electronic_manufacturing',
            'urn:li:industry:113' => 'online_media',
            'urn:li:industry:114' => 'nanotechnology',
            'urn:li:industry:115' => 'music',
            'urn:li:industry:116' => 'logistics_&_supply_chain',
            'urn:li:industry:117' => 'plastics',
            'urn:li:industry:118' => 'computer_&_network_security',
            'urn:li:industry:119' => 'wireless',
            'urn:li:industry:120' => 'alternative_dispute_resolution',
            'urn:li:industry:121' => 'security_&_investigations',
            'urn:li:industry:122' => 'facilities_services',
            'urn:li:industry:123' => 'outsourcing/offshoring',
            'urn:li:industry:124' => 'health,_wellness_&_fitness',
            'urn:li:industry:125' => 'alternative_medicine',
            'urn:li:industry:126' => 'media_production',
            'urn:li:industry:127' => 'animation',
            'urn:li:industry:128' => 'commercial_real_estate',
            'urn:li:industry:129' => 'capital_markets',
            'urn:li:industry:130' => 'think_tanks',
            'urn:li:industry:131' => 'philanthropy',
            'urn:li:industry:132' => 'e-learning',
            'urn:li:industry:133' => 'wholesale',
            'urn:li:industry:134' => 'import_&_export',
            'urn:li:industry:135' => 'mechanical_or_industrial_engineering',
            'urn:li:industry:136' => 'photography',
            'urn:li:industry:137' => 'human_resources',
            'urn:li:industry:138' => 'business_supplies_&_equipment',
            'urn:li:industry:139' => 'mental_health_care',
            'urn:li:industry:140' => 'graphic_design',
            'urn:li:industry:141' => 'international_trade_&_development',
            'urn:li:industry:142' => 'wine_&_spirits',
            'urn:li:industry:143' => 'luxury_goods_&_jewelry',
            'urn:li:industry:144' => 'renewables_&_environment',
            'urn:li:industry:145' => 'glass,_ceramics_&_concrete',
            'urn:li:industry:146' => 'packaging_&_containers',
            'urn:li:industry:147' => 'industrial_automation',
            'urn:li:industry:148' => 'government_relations',
            'urn:li:industry:150' => 'horticulture',
        ];

        $res = $li->get('organizationalEntityFollowerStatistics', [
            'q' => 'organizationalEntity',
            'organizationalEntity' => $accountUrn,
        ]);

        $countsByStaffSize = Arr::get($res['elements'][0], 'followerCountsByStaffCountRange', []);
        $countsByIndustry = Arr::get($res['elements'][0], 'followerCountsByIndustry', []);
        $countsByFunction = Arr::get($res['elements'][0], 'followerCountsByFunction', []);
        $countsBySeniority = Arr::get($res['elements'][0], 'followerCountsBySeniority', []);

        foreach($countsByStaffSize as $item){
            $data['followers_by_staff_count_range/' . $item['staffCountRange']] = $item['followerCounts']['organicFollowerCount'] + $item['followerCounts']['paidFollowerCount'];
        }
        foreach($countsByIndustry as $item){
            $data['followers_by_industry/' . $item['industry']] = $item['followerCounts']['organicFollowerCount'] + $item['followerCounts']['paidFollowerCount'];
        }
        foreach($countsByFunction as $item){
            $data['followers_by_function/' . $item['function']] = $item['followerCounts']['organicFollowerCount'] + $item['followerCounts']['paidFollowerCount'];
        }
        foreach($countsBySeniority as $item){
            $data['followers_by_seniority/' . $item['seniority']] = $item['followerCounts']['organicFollowerCount'] + $item['followerCounts']['paidFollowerCount'];
        }

        if($account->type === 'linkedin.org'){
            $res = $li->get('organizationPageStatistics', [
                'q' => 'organization',
                'organization' => $accountUrn,
            ]);
        }else if($account->type === 'linkedin.brand'){
            $res = $li->get('brandPageStatistics', [
                'q' => 'brand',
                'brand' => 'urn:li:organizationBrand:' . $account->account_id,
            ]);
        }

        if(isset($res['elements'][0]['pageStatisticsByCountry'])){
            $viewsByCountry = $res['elements'][0]['pageStatisticsByCountry'];
            foreach($viewsByCountry as $item){
                $data['views_by_country/' . $item['country']] = $item['pageStatistics']['views']['allPageViews']['pageViews'];
            }
        }

        if(isset($res['elements'][0]['totalPageStatistics'])) {
            $totalPageStats = $res['elements'][0]['totalPageStatistics'];
            $data['total_views'] = $totalPageStats['views']['allPageViews']['pageViews'];
        }

        return $data;
    }

    /**
     * @throws \Exception
     */
    public function processLinkedinBrand($account){
        return $this->processLinkedinOrg($account);
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processGoogleLocation(Account $account){
        $service = $account->getApi();   
        
        $token = json_decode($account->token, true);
        $google_client = ApiHelper::getGoogle($token);
        $google_business_performance = ApiHelper::getGoogleBusinessProfilePerformance($google_client);
        
        $data = [];
        
        $MAP = [
            'BUSINESS_IMPRESSIONS_DESKTOP_MAPS' => 'desktop_map_views',
            'BUSINESS_IMPRESSIONS_MOBILE_MAPS' => 'mobile_map_views',
            'WEBSITE_CLICKS' => 'actions_website',
            'CALL_CLICKS' => 'actions_phone',
            'BUSINESS_DIRECTION_REQUESTS' => 'actions_driving_directions',
        ];

        $metrics = [
            'BUSINESS_IMPRESSIONS_DESKTOP_MAPS',
            'BUSINESS_IMPRESSIONS_MOBILE_MAPS',
            'WEBSITE_CLICKS',
            'CALL_CLICKS',
            'BUSINESS_DIRECTION_REQUESTS'
        ];

        $date = Carbon::yesterday(); //fetching metrics for yesterday only
        $day = $date->day;
        $month = $date->month;
        $year = $date->year;

        $params = [
            'dailyRange.startDate.day' => $day,    //inclusive
            'dailyRange.startDate.month' => $month,
            'dailyRange.startDate.year' => $year,
            'dailyRange.endDate.day' => $day,      //inclusive
            'dailyRange.endDate.month' => $month,
            'dailyRange.endDate.year' => $year,
        ];
        foreach($metrics as $metric){
            $params['dailyMetric'] = $metric;
            try{
                $res = $google_business_performance->locations->getDailyMetricsTimeSeries('locations/' . explode('/locations/', $account->account_id)[1], $params);
                $value = $res['timeSeries']['datedValues'][0]['value'] ? $res['timeSeries']['datedValues'][0]['value'] : '0';

                    $arr = [
                        'value' => $value,
                        'timestamp' => $date
                    ];

                if(isset($MAP[$metric])){
                    // use custom metric key
                    $data[$MAP[$metric]] = $arr;
                } else if(in_array($metric, $MAP)){
                    // just use lowercase metric names
                    $data[strtolower($metric)] = $arr;
                }
            }catch(\Exception $exception){
                if( Str::contains($exception->getMessage(), ['500', '502', '503']) ){
                    // temp. error
                    return [];
                }
                if( Str::contains(strtolower($exception->getMessage()), '404') ){
                    return [];
                }
                if(Str::contains(strtolower($exception->getMessage()), ['the caller does not have permission'])){
                    $account->setOption('insights_permission_error_at', time());
                    return [];
                }
                throw $exception;
            }
        }

        try {
            $res = $service->accounts_locations_reviews->listAccountsLocationsReviews($account->account_id, [
                'pageSize' => 10, // doesn't matter; keep it lowest
            ]);
            $data['reviews'] = $res->getTotalReviewCount();
        } catch (\Exception $exception){
            if( !Str::contains($exception->getMessage(), ['500', '502', '503', '404']) ){
                report($exception);
            }
        }
        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processGoogleYoutube(Account $account){

        /** @var \Google\Service\YouTube $service */
        $service = $account->getApi();

        try {
            // fetch channel statistics
            $res = $service->channels->listChannels('statistics', [
                'id' => $account->account_id,
            ]);
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        $items = $res->getItems();

        if(!$items){
            return [];
        }

        $item = $items[0];

        if(!$item){
            // something wrong
            return [];
        }

        $data = [];

        $data['views'] = $item->getStatistics()->getViewCount();
        $data['subscribers'] = $item->getStatistics()->getSubscriberCount();
        $data['videos'] = $item->getStatistics()->getVideoCount();

        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processRedditSubreddit(Account $account){

        $token = json_decode($account->token, true);

        $subreddit = $token['subreddit'];

        $scraper = ApiHelper::getRapidAPI('reddit-scraper2.p.rapidapi.com');

        // fetch subreddit statistics
        $res = $scraper->get('/sub_info?sub=' . urlencode('https://www.reddit.com/r/' . $subreddit . '/'));

        $resJson = json_decode($res->getBody()->getContents(), true);

        $data = [];

        $data['subscribers'] = $resJson['data']['subscribers'];
        $data['active_users'] = $resJson['data']['online'];

        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processRedditProfile(Account $account){

        $data = [];

        $scraper = ApiHelper::getRapidAPI('reddit-scraper2.p.rapidapi.com');

        // retry_enabled
        $token = json_decode($account->token, true);

        $username = $token['username'];

        // fetch subreddit statistics
        $res = $scraper->get('/user_info?user=' . urlencode($username));

        $resJson = json_decode($res->getBody()->getContents(), true);

        $resJson = $resJson['data'];

        $data['karma'] = $resJson['karma']['total'];

        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processPinterestProfile(Account $account){
        $service = $account->getApi();
        $data = [];

        $res = $service->get('v5/user_account');
        $json_res = json_decode($res->getBody()->getContents(), true);
        
        $data['followers'] = $json_res['follower_count'];
        if($json_res['monthly_views'] < 0){ // don't know why they send negative values
            $data['monthly_views'] = 0;
        } else {
            $data['monthly_views'] = $json_res['monthly_views'];
        }
        
        $start_date = Carbon::now()->subDay(1)->format('Y-m-d'); //since yesterday
        $end_date = Carbon::now()->format('Y-m-d');

        $res = $service->get('v5/user_account/analytics', [
            'query' => [
                'start_date' => $start_date,
                'end_date' => $end_date,
            ]
            ]);

        $json_res = json_decode($res->getBody()->getContents(), true);
        $metrics = $json_res['all']['summary_metrics'];
        
        foreach($metrics as $index => $value){
            $data[strtolower($index)] = $value;
        }

        return $data;
    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processThreadsProfile(Account $account){

        if($account->getOption('no_insights_access')){
            // if we don't have access to insights, we can't get any more data
            return [];
        }

        $data = [];

        /** @var Client $threads */
        $threads = $account->getApi();

        $daily_metrics = [
            'views',
        ];

        try {
            $body = $threads->get('me/threads_insights?metric=' . implode(',', $daily_metrics))->getBody()->getContents();
            $res = json_decode($body, true);

            if(isset($res['error'])){
                if(isset($res['error']['message']) && Str::contains(strtolower($res['error']['message']), ['unsupported request - method type: get'])){
                    return [];
                }
                report(new \Exception('Threads error: ' . json_encode($res['error'])));
                return [];
            }

            foreach($res['data'] as $metric){
                $key = $metric['name'];
                $data[$key] = [
                    'value' => $metric['values'][0]['value'],
                    'timestamp' => Carbon::parse($metric['values'][0]['end_time']),
                ];
            }

        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            } else if (Str::contains($exception->getMessage(), '#190')){
                $account->setOption('no_insights_access', true);
                return [];
            }
            if(Str::contains(strtolower($exception->getMessage()), ['does not have sufficient administrative permission', 'business requires Two Factor Authentication'])){
                $account->setOption('insights_permission_error_at', time());
                return [];
            }
            if(Str::contains(strtolower($exception->getMessage()), ['unsupported request - method type: get'])){
                return [];
            }
            throw $exception;
        }

        $lifetime_metrics = [
            'likes',
            'replies',
            'reposts',
            'quotes',
            'followers_count',
        ];

        try {

            $body = $threads->get('me/threads_insights?metric=' . implode(',', $lifetime_metrics))->getBody()->getContents();
            $res = json_decode($body, true);

            foreach($res['data'] as $metric){
                $key = $metric['name'];
                $data[$key] = $metric['total_value']['value'] ?? $metric['values'][0]['value'];
            }

        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        return $data;

    }

    /**
     * @param Account $account
     * @return array
     * @throws \Exception
     */
    public function processBlueskyProfile(Account $account){

        if($account->getOption('no_insights_access')){
            // if we don't have access to insights, we can't get any more data
            return [];
        }

        $data = [];

        /** @var Client $bluesky */
        $bluesky = $account->getApi();

        try {
            $body = $bluesky->get('app.bsky.actor.getProfile?actor=' . $account->account_id)->getBody()->getContents();
            $res = json_decode($body, true);

            $data['followers'] = $res['followersCount'];

        } catch(\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), self::$skipErrorsContaining)){
                // skip for now
                return [];
            }
            throw $exception;
        }

        return $data;

    }

}
