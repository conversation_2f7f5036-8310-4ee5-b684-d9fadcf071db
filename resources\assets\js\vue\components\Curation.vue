<template>
    <div class="container p-0">
        <div class="row">
            <div class="col-12 mb-4 pb-md-0 pb-1">
                <div class="d-flex justify-content-between align-items-baseline">
                    <div>
                        <h3>
                            Curate Content 
                        </h3>
                        <div v-if="result.total">
                            <p class="mb-0">{{ result.total }} items found</p>
                        </div>
                    </div>
                    <div>
                        <!-- filter icon -->
                        <button class="btn btn-sm btn-outline-light" data-toggle="modal" data-target="#curationFilterModal">
                            <div class="d-flex align-items-center">
                                <i class="ph ph-funnel ph-md mr-2"></i>
                                Filter
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-12 text-center"
                 v-if="loading">
                <div  v-html="overlayLoaderHtml"></div>
            </div>
            <div class="col-12 pt-8" v-else-if="loadedTopics.length === 0">
                <div class="card text-center">
                    <div class="card-body">
                        
                        <h4 class="font-weight-400 mb-md-2 mb-4">
                            Please select a topic to see the content
                        </h4>
                        <p class="mb-5">
                            Use filter button to select a topic
                        </p>
                        <div>
                            <button class="btn btn-sm btn-primary d-inline-block" data-toggle="modal" data-target="#curationFilterModal">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-funnel mr-2"></i>
                                    Filter
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 pt-8" v-else-if="result.items.length === 0">
                <div class="card text-center">
                    <div class="card-body">
                        
                        <h4 class="font-weight-400 mb-md-2 mb-4">
                            No content to show
                        </h4>
                        <p class="mb-5">
                            Try to change the filter. <a href="https://help.socialbu.com/article/570-how-can-i-filter-topics-in-curate-feature" data-beacon-article-modal="67375b7cbd106f27f64888cd"> Learn more</a>

                        </p>
                    </div>
                </div>
            </div>
            <template v-if="loadedTopics.length">
                <div class="col-12 col-md-4 mb-4 px-2"
                     v-for="item in result.items" :key="item.id">
                    <div v-if="generatingPost === item.id">
                        <div class="loading-element d-flex justify-content-center align-items-center position-absolute h-100 w-100">
                            <div class="h-100px">
                                <div class="d-flex p-5 justify-content-center align-items-center">
                                    <i class="shake ph ph-magic-wand ph-2xl text-sb"></i>
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card border hover-shadow-4 border-secondary item_card rounded-lg">
                        <img class="curate-card card-img-top bg-white rounded-lg-top" alt="image"
                             :src="item.media"
                             v-lazy
                             @error="item.media=null"
                             v-if="item.media">
                        <div class="card-body pt-4 px-4 pb-0">
                            <div class="mb-2">
                                <span class="small-2">
                                    {{ $momentUTC(item.published_at).format("MMM DD, YYYY") }}
                                </span>
                                <!--
                                <span v-if="item.authors">
                                    &bull;
                                    {{ truncate(item.authors, {length: 20}) }}
                                </span>
                                -->
                                <span class="small-2" :title="getDomain(item.link)"
                                      v-if="item.link">
                                    &bull;
                                    {{ truncate(getDomain(item.link), {length: 30}) }}
                                </span>
                            </div>
                            <div>
                                <h6 class="font-weight-500 mb-0" :title="item.title" v-tooltip
                                    :key="item.id + 'title'">
                                    {{ item.title.length > 67 ? item.title.substring(0,67).concat('...') : item.title }}
                                </h6>
                                
                            </div>
                        </div>
                        <div class="h-200 overflow-hidden bg-white p-4"
                             v-if="!item.media && item.description" v-html="purifyHtml(item.description)">
                        </div>
                        <div class="container mb-2">
                            <div class="row pt-2">
                                
                                <div class="col-12 col-md-12 d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="small-2 text-gray-light" title="Score" v-tooltip v-if="item.score">
                                            🔥
                                            {{ compactInteger(item.score, 1)}}
                                        </span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <a :href="item.link" target="_blank" class="btn p-2" title="View" v-tooltip v-if="item.link">
                                            <i class="ph ph-arrow-square-out ph-md text-gray-light mr-3"></i>
                                        </a>
                                        <!-- button group with dropdown -->
                                        <div>
                                            <i class="cursor-pointer ph ph-share-fat ph-md text-gray-light p-2 mr-3" @click.prevent="share(item)" title="Share" v-tooltip></i>
                                            <i
                                                class="cursor-pointer ph ph-dots-three ph-bold ph-md p-2 text-gray-light"
                                                data-toggle="dropdown"
                                                aria-haspopup="true"
                                                aria-expanded="false">
                                            </i>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="#"
                                                    @click.prevent="share(item, true)">
                                                    <i class="ph ph-magic-wand"></i> &nbsp; Generate Post
                                                </a>
                                                <a class="dropdown-item" href="#"
                                                    @click.prevent="copy(item.link)" v-if="item.link">
                                                    <i class="ph ph-copy"></i> &nbsp; Copy Link
                                                </a>
                                                 <!-- 
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item" href="#"
                                                    @click.prevent="report(item)">
                                                    <i class="fa fa-flag"></i> &nbsp; Report
                                                </a>
                                                -->
                                            </div>
                                        </div>
                                    </div>    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 pt-4 border-top"
                     v-if="result.lastPage > 1">
                    <!-- pagination -->
                    <ul class="pagination justify-content-end">
                        <li class="page-item" :class="{active: result.currentPage === n}" v-for="n in $pagination(result.currentPage, result.lastPage)">
                            <a class="page-link" v-if="n==='...'">{{ n }}</a>
                            <a class="page-link" href="#" @click.prevent="load(n)" v-else>{{ n }}</a>
                        </li>
                    </ul>
                </div>
            </template>
        </div>
        <!-- filter modal -->
        <div class="modal fade" id="curationFilterModal" ref="modal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-slideout custom-modal-width right" role="document">
                <div class="modal-content">
                    <div class="modal-header px-md-5 px-4 pt-5 pb-4">
                        <h5 class="modal-title">
                            Filter Content
                        </h5>
                        <div>
                            <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                <i class="ph ph-x ph-md"></i>
                            </button>
                        </div>
                    </div>
                    <div class="modal-body px-md-5 px-4">
                        <form @submit.prevent="load(1, true)">
                            <div class="form-group">
                                <label>Topics</label>
                                <div>
                                    <select class="w-100" multiple v-select2="{ onChange: v => filter.topics = v }">
                                        <option v-for="topic in topics"
                                                :selected="filter.topics.includes(topic.id + '')"
                                                :value="topic.id" :key="'topic-select-' + topic.id" v-text="topic.name"></option>
                                    </select>
                                </div>
                                <p class="small-2">
                                    Filter content by topics
                                </p>
                            </div>
                            <div class="form-group">
                            <label>Time Range</label>
                            <div>
                                <select class="w-100" v-model="filter.range" v-selectpicker>
                                    <option value="last_7_days">
                                        Last 7 days
                                    </option>
                                    <option value="last_14_days">
                                        Last 14 days
                                    </option>
                                    <option value="last_30_days">
                                        Last 30 days
                                    </option>
                                    <option value="last_3_months">
                                        Last 3 months
                                    </option>
                                </select>
                            </div>
                            <p class="small-2">
                                Filter content by time range
                            </p>
                        </div>
                        </form>
                    </div>

                    <div class="modal-footer float-right">
                        <form @submit.prevent="load(1, true)">
                                <button class="btn btn-sm btn-primary" type="submit" :disabled="loading">
                                    <div class="spinner-border text-primary" role="status" v-if="loading">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <span v-else>
                                        Apply
                                    </span>
                                </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- generate post modal -->

    </div>
</template>

<script>
import {
    axios,
    axiosErrorHandler,
    overlayLoaderHtml
} from "./../../components";
import copy from "copy-to-clipboard";
import {debounce, truncate} from "lodash";
import DOMPurify from "dompurify";
import $ from "jquery";
import Cookies from "js-cookie";
import { compactInteger } from "humanize-plus";
import create_content from "../../scripts/create_content";

export default {
    name: "Curation",
    data() {
        return {
            loading: true,
            topics: [],
            filter: {
                topics: [],
                range: "last_7_days",
            },
            result: {
                total: 0,
                items: [],
                currentPage: null,
                lastPage: null,
                nextPage: null,
            },
            loadedTopics: [], // topics for which the content is loaded
            generatingPost: null, // id of the item
        };
    },
    computed: {
        overlayLoaderHtml: () => overlayLoaderHtml,
        filterQuery(){
            const queries = [];
            let from = this.$momentUTC().subtract(7, "days").format("YYYY-MM-DD");
            let to = this.$momentUTC().format("YYYY-MM-DD");
            if (this.filter.range === "last_14_days") {
                from = this.$momentUTC().subtract(14, "days").format("YYYY-MM-DD");
                to = this.$momentUTC().format("YYYY-MM-DD");
            } else if (this.filter.range === "last_30_days") {
                from = this.$momentUTC().subtract(30, "days").format("YYYY-MM-DD");
                to = this.$momentUTC().format("YYYY-MM-DD");
            } else if (this.filter.range === "last_3_months") {
                from = this.$momentUTC().subtract(3, "months").format("YYYY-MM-DD");
                to = this.$momentUTC().format("YYYY-MM-DD");
            }
            this.filter.topics.forEach(topic => queries.push("topics[]=" + topic));
            queries.push("from=" + from);
            queries.push("to=" + to);
            return queries.join("&");
        }
    },
    methods: {
        truncate,
        compactInteger,
        copy,
        purifyHtml(html) {
            return DOMPurify.sanitize(html, {
                ALLOWED_TAGS: ["b", "i", "em", "strong", "p", "br",],
            });
        },
        getDomain(link){
            return new URL(link).hostname.replace("www.", "");
        },
        decodeHtml(html){
            const txt = document.createElement("textarea");
            txt.innerHTML = html;
            return txt.value;
        },
        scrollToTop() {
            $("html, body").animate({scrollTop: 0}, 250, () => {});
        },
        addUtmParams(link){
            const url = new URL(link);
            // only if not already added
            if(!url.searchParams.has("utm_source")){
                url.searchParams.append("utm_source", "SocialBu");
            }
            if(!url.searchParams.has("utm_medium")){
                url.searchParams.append("utm_medium", "web");
            }
            if(!url.searchParams.has("utm_campaign")){
                url.searchParams.append("utm_campaign", "curation");
            }
            return url.toString();
        },
        async getTopics(){
            const { data } = await axios.get("/api/v1/curation/topics");
            this.topics = data.sort((a, b) => a.name.localeCompare(b.name));
        },
        async load(n = 1, formSubmitted = false) {

            if(this.loading || this.filter.topics.length === 0){
                return;
            }

            // set topics for which the content is loaded
            this.loadedTopics = [... this.filter.topics ];

            // scroll to top
            this.scrollToTop();

            this.loading = true;
            try {
                const { data } = await axios.get("/api/v1/curation/items?page=" + n + "&" + this.filterQuery);

                if(data.items && data.items.length){
                    data.items = data.items.map(item => {
                        // add utm params
                        if(item.link) {
                            item.link = this.addUtmParams(item.link);
                        }
                        if(item.title){
                            item.title = this.decodeHtml(item.title);
                        }
                        return item;
                    });
                }

                this.result = data;

                // close modal if open
                $(this.$refs.modal).modal("hide");

                // save cookie
                if(formSubmitted){
                    Cookies.set("curation_filter", JSON.stringify({... this.filter}), { expires: 365 });
                }

            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loading = false;
        },
        async share(item, useAI = false){
            if(!useAI){
                (await create_content.getPostComposer()).content = `${item.title} ${item.link}`;
                await create_content.newPost();
            } else {
                if(this.generatingPost){
                    return;
                }
                this.generatingPost = item.id;
                try {
                    const { data } = await axios.post("/api/v1/generated_content/generate_curated_item_post", {
                        id: item.id,
                    });

                    (await create_content.getPostComposer()).content = data.content + " " + item.link;
                    await create_content.newPost();
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.generatingPost = null;
            }
        },
        report(item){
            console.log("reported", item);
        },
    },
    async mounted() {
        // load cookie
        const curation_filter = Cookies.get("curation_filter");
        if(curation_filter){
            const newFilter = JSON.parse(curation_filter);
            this.filter = {... this.filter, ... newFilter};
        }
        await this.getTopics();
        this.loading = false;
        await this.load();
    },
}
</script>

<style lang="scss" scoped>
.shake {
    animation: shake 2.0s;
    /* When the animation is finished, start again */
    animation-iteration-count: infinite;
}

@keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-1deg); }
    20% { transform: translate(-3px, 0px) rotate(1deg); }
    30% { transform: translate(3px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(1deg); }
    50% { transform: translate(-1px, 2px) rotate(-1deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(3px, 1px) rotate(-1deg); }
    80% { transform: translate(-1px, -1px) rotate(1deg); }
    90% { transform: translate(1px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-1deg); }
}
.rounded-lg-top{
    border-top-left-radius: 0.75rem !important;
    border-top-right-radius: 0.75rem !important;
}
.curate-card{
    object-fit: cover;
    max-height: 200px;
}
.loading-element{
    z-index: 2;
    background: rgba(255, 255, 255 , .6);
}
.text-gray-light{
    color: #657085 !important;
}
</style>
