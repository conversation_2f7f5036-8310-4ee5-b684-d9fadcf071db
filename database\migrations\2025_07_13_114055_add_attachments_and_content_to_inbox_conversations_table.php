<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAttachmentsAndContentToInboxConversationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            $table->json('attachments')->nullable()->after('data');
            $table->text('content')->nullable()->after('attachments');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            $table->dropColumn(['attachments', 'content']);
        });
    }
}
