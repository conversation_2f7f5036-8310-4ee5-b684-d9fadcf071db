<template>
    <div>
        <template v-if="loaded">

            <div class="row">

                <div class="col-12 col-md-12 pb-6">

                    <div
                        v-html="overlayLoaderHtml" v-if="busy">

                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                        <h3 class="mb-0">
                            <span v-if="type === 'scheduled'">
                                Scheduled Posts
                            </span>
                            <span v-else-if="type === 'draft'">
                                Post Drafts
                            </span>
                            <template v-if="type === 'awaiting_approval'">
                                <span class="d-md-inline-block d-none">
                                    Posts Pending Approval
                                </span>
                                <span class="d-md-none">
                                    Awaiting Approval 
                                </span>
                            </template>
                            <span v-if="type === 'published'">
                                Published Posts
                            </span>
                        </h3>
                        </div>
                        <!-- bullk actions -->
                        <div class="row">                    
                            <div class="col d-flex justify-content-end mt-1">
                                <div class="ml-2">

                                    <button type="button" class="btn btn-sm btn-outline-light" title="Filter" data-toggle="modal" data-target="#posts_filter_form"
                                            v-tour:filter_posts="'Filter posts by team, accounts, and so on'" v-tooltip>
                                            <i class="ph ph-funnel ph-md"></i> Filter
                                    </button>
                                    <!--added filter count-->
                                    <span class="badge badge-secondary badge-number" v-if="filterCount">
                                            {{filterCount}}
                                    </span>

                                </div>
                            </div>
                        </div>
                    
                        
                    </div>

                    <div class="row">
                        <div class="col-12 order-1 order-md-0">

                            <!-- no posts found msg -->
                            <template v-if="!Object.keys(itemsByDay).length">
                                <div class="text-center pt-8 mt-md-0 mt-7">
                                    <template v-if="type === 'published'">
                                        <h4 class="d-md-block d-none">
                                            No posts to show
                                        </h4>
                                        <h4 class="d-md-none font-weight-600 mb-4">
                                            No posts to show
                                        </h4>
                                    </template>
                                    <template v-else>
                                        <h4 class="d-md-block d-none">
                                            You have no scheduled posts
                                        </h4>
                                        <h4 class="d-md-none font-weight-600 mb-4">
                                            You have no scheduled posts
                                        </h4>
                                    </template>
                                    <p class="mb-5" v-if="type === 'published'">
                                        Create one by using the new post button
                                    </p>
                                    <p class="mb-5" v-else>
                                        Create a new post to schedule. 
                                        <a href="https://help.socialbu.com/article/609-post-immediately-or-schedule-for-later" data-beacon-article-modal="683ffec90e341869fe3f14b6">Learn more</a>
                                    </p>
                                    <div v-if="!activeFilterQuery && type === 'published'">
                                        <button class="btn btn-primary btn-sm" @click.prevent="getContentHelper().newPost()"><div class="d-flex align-items-center"><i class="ph ph-bold ph-plus mr-2"></i> New Post</div></button>
                                    </div>
                                    <div v-if="!activeFilterQuery && type !== 'published'">
                                        <button class="btn btn-primary btn-sm" @click.prevent="getContentHelper().newPost()"><div class="d-flex align-items-center"><i class="ph ph-bold ph-plus mr-2"></i> Create Post</div></button>
                                    </div>
                                </div>
                            </template>

                            <!-- posts render -->
                            <template v-else>

                                    <div class="col-12 d-flex justify-content-end pr-0 pl-0 pt-2 bulk-selection-btn sticky-top">
                                        <template v-if="selectablePostIds.length">
                                            <div v-if="selectedItems.length" class="nav d-flex align-items-center px-2 pb-0 pt-0">
                                                <template v-if="postsForBulkActions.length">
                                                    <div class="nav-item">
                                                        {{ postsForBulkActions.length }} selected
                                                    </div>
                                                    
                                                </template>
                                                <template v-if="postsForBulkActions.length">
                                                    
                                                    <div class="dropdown show ">
                                                        <button title="Select actions for the selected posts" class="btn btn-sm btn-outline pr-0 pl-1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                            <i class="ph ph-caret-down"></i>
                                                        </button>
                                                        <div class="dropdown-menu dropdown-menu-right">
                                                            <div class="nav-item">
                                                                <a href="#" class="dropdown-item" @click.prevent="onSelectAll">
                                                                    <span v-if="selectedItems.length !== selectablePostIds.length">
                                                                        <i class="ph ph-selection-all ph-md mr-1"></i>
                                                                        Select all
                                                                    </span>
                                                                    <span v-else>
                                                                        <i class="ph ph-selection-all ph-md mr-1"></i>
                                                                        Unselect all
                                                                    </span>
                                                                </a>
                                                            </div>
        
                                                            <a v-if="type !== 'draft'" class="dropdown-item" href="#" @click.prevent="draftSelected">
                                                                <i class="ph ph-file-dashed ph-md mr-1"></i> Draft
                                                            </a>
                                                            
                                                            <a class="dropdown-item" href="#" @click.prevent="deleteSelected">
                                                                <i class="ph ph-trash ph-md mr-1"></i> Delete
                                                            </a>
                                                        </div>

                                                    </div>
                                                </template>
                                            </div>
                                        </template>    
                                    </div>

                                <div class="row mb-4" v-for="(data, day) in itemsByDay">
                                    
                                
                                    <div class="col-12 col-md-12" v-if="posts.items.length">
                                        <div class="col-12 col-md-12 pr-0 pl-0 pt-2 bg-white position-sticky position-sticky-top-fix">
                                        <h6 class="my-2 date font-weight-500">
                                            <template v-if="$momentUTC().format('YYYY') === data.moment.format('YYYY')">
                                                <span class="d-none d-md-block">
                                                    {{ data.moment.format("dddd") }}, 
                                                    {{ data.moment.format("MMM") }}
                                                    ,
                                                    {{ data.moment.format("Do") }}
                                                </span>
                                                <span class="d-block d-md-none">
                                                    {{ data.moment.format("MMM Do") }}
                                                </span>
                                            </template>
                                            <template v-else>
                                                <span class="d-none d-md-block">
                                                    {{ data.moment.format("MMM") }}
                                                    ,
                                                    {{ data.moment.format("Do") }}
                                                    ,
                                                    {{ data.moment.format("YYYY") }}
                                                </span>
                                                            <span class="d-block d-md-none">
                                                    {{ data.moment.format('MMM Do YYYY') }}
                                                </span>
                                            </template>
                                        </h6>
                                    </div>
                                    <div class="post-section" :class="{'position-relative': post.can_edit }"
                                             v-for="post in data.posts" v-bind:key="post.id">
                                            <input class="cursor-pointer post-select-input rounded" title="Select" type="checkbox" :class="{'d-block' : selectedItems.length}"
                                                   :checked="selectedItems.includes(post.id)" v-tooltip @change="onSelectPost(post.id)" v-if="post.can_edit" />
                                            <post
                                                v-on:updated="onPostUpdated" v-on:set-attachments="attachments = $event" :post="post"
                                                :class="{'border-primary': activePosts.includes(post.id) || selectedItems.includes(post.id) }"
                                                :id="'post-' + post.id">
                                            </post>

                                        </div>

                                    </div>
                                </div>

                                <div v-if="posts.lastPage > 1">
                                    <!-- pagination -->
                                    <ul class="pagination justify-content-end">
                                        <li class="page-item" :class="{active: posts.currentPage === n}" v-for="n in pagination(posts.currentPage, posts.lastPage)">
                                            <a class="page-link" v-if="n==='...'">{{ n }}</a>
                                            <a class="page-link" href="#" @click.prevent="navigateToPage(n)" v-else>{{ n }}</a>
                                        </li>
                                    </ul>
                                </div>

                            </template>

                        </div>
                    </div>
                    
                    
                </div>
                
            </div>
            <!-- Filter form modal -->
            <div id="posts_filter_form" class="modal fade" role="dialog" tabindex="-1" aria-hidden="true" >

                <div class="modal-dialog modal-dialog-slideout right custom-modal-width">
                    <div class="modal-content border border-secondary shadow">

                        <div class="modal-header px-md-5 px-4 pt-5 pb-4">
                            <h5 class="modal-title">
                                Filter Posts
                            </h5>
                            <!-- <button type="button" class="close" data-dismiss="modal" @click="toggleFilterForm"><i class="ph ph-x ph-md"></i></button> -->
                            <button type="button" class="close" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body px-md-5 px-4">
    
                            <form @submit.prevent="navigateToPage(1)">
    
                                <div class="form-group">
                                    <label for="filterContent" title="Caption, content or post body">Content</label>
                                    <input type="text" class="form-control" id="filterContent" placeholder="..." v-model="filter.keyword">
                                    <small class="small-2">
                                        Filter posts by a specific keyword
                                    </small>
                                </div>
    
                                <div class="form-group" v-if="teams.length">
                                    <label for="filterTeam">Team</label>
                                    <div>
                                        <select class="w-100" id="filterTeam" v-model.number="filter.team" title="..." v-selectpicker>
                                            <option value="" v-if="filter.team">
                                                ...
                                            </option>
                                            <option v-for="team in teams" :value="team.id">
                                                {{ team.name }}
                                            </option>
                                        </select>
                                    </div>
                                    <small class="small-2">
                                        Filter posts by team
                                    </small>
                                </div>
    
                                <div class="form-group">
                                    <label for="filterAccount">Accounts</label>
                                    <select class="w-100" id="filterAccount" multiple v-model.number="filter.accounts" title="..." v-selectpicker>
                                        <optgroup v-for="(network, i) in filterNetworks" :label='network.title' :key="network.title + '_' + i">
                                            <option v-for="account in getAccountsByType(network.type)" :key="network.type + account.id" :data-content="`<img class='border rounded-circle border-white position-relative network-icon' src='${getIconForAccount(account.type, 'circular')}'><span class='p-2'>${account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name}</span>`" :value="account.id"></option>
                                        </optgroup>
                                    </select>
                                    <small class="small-2">
                                        Filter posts by social accounts
                                    </small>
                                </div>
    
                                <div class="form-group" v-if="filter.team">
                                    <label for="filterUser">Added by</label>
                                    <div>
                                        <select class="w-100" id="filterUser" v-model.number="filter.user" title="..." v-selectpicker>
                                            <option v-for="user in filterUsers" :value="user.id">
                                                {{ user.name + (user.id === userId ? ' (me)': '') }}
                                            </option>
                                        </select>
                                    </div>
                                    <small class="small-2">
                                        Filter posts by post creator
                                    </small>
                                </div>
    
                            </form>
    
                            <div class="d-flex justify-content-between mt-2">
                                <button class="btn btn-sm btn-light"
                                        :class="{'invisible': !filterQuery && !activeFilterQuery }" @click="()=>resetFilter()">
                                    Reset
                                </button>
                                <button class="btn btn-sm btn-primary"
                                        @click="navigateToPage(1)">
                                    Apply
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- edit time modal -->
            <div id="edit_time_popup" class="modal" role="dialog" tabindex="-1" aria-hidden="true">
                <!-- modal for schedule popup -->
                <div class="modal-dialog">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" v-text="$lang.get('publish.schedule_post')"></h5>
                            <i class="ph ph-x" data-dismiss="modal"></i>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-center">
                                <div id="edit_post_publish_at"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" type="button"
                                    class="btn btn-primary" v-text="$lang.get('generic.save')"></button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modal for attachment view -->
            <div class="modal fade" id="attachmentModal" tabindex="-1" role="dialog"
                 aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pt-6 pb-5">
                            <h5 class="modal-title">Media Attachments</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true"><i class="ph ph-x ph-md"></i></span></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col mx-auto">
                                    <div class="gallery gallery-4-type2">
                                        <div class="gallery-item pt-0" v-for="attachment in attachments">
                                            <video controls :title="attachment.name"  v-if="['mp4', 'm4v', 'mov', 'qt', 'avi'].includes(attachment.type)">
                                                <source :src="attachment.url" />
                                            </video>
                                            <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" v-else
                                                 class="border rounded"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light btn-sm" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

        </template>
        <div class="card border-0" v-else>
            <div class="card-body text-center" v-html="spinnerHtml">

            </div>
        </div>
    </div>
</template>

<script>
import $ from "jquery";
import { extend } from "lodash";
import { alertify, appConfig, axios, axiosErrorHandler, overlayLoaderHtml, getIconForAccount, spinnerHtml } from "../../components";
import Post from "./Post.vue";
import createContent from "../../scripts/create_content";
import Cookies from "js-cookie";
import {getAccounts, getNetworks} from "../../data";

export default {
    name: "posts",
    components: {
        Post
    },
    data() {
        return {
            type: "scheduled", // type which is sent to php in ajax request
            loaded: false,
            busy: false, // for actions like bulk actions

            activeFilterQuery: null, // filter querystring used to get current results
            filter: {
                keyword: "",
                team: null,
                accounts: [],
                user: null
            },

            posts: {
                items: [], // holds all posts
                currentPage: null,
                lastPage: null,
                nextPage: null // is a Number if there is next page of `posts`
            },

            accounts: [],
            networks: [],

            attachments: [], // for attachment showing in modal
            activePosts: [], // active post ids, active post is highlighted

            selectedItems: [] // selected post ids for bulk actions
        };
    },
    computed: {
        userId: () => appConfig.userId,
        spinnerHtml: () => spinnerHtml,
        overlayLoaderHtml: () => overlayLoaderHtml,
        teams() {
            const teamsById = {};
            this.accounts.filter(a => a.team).forEach(a => {
                teamsById[a.team.id] = a.team;
            });
            return Object.values(teamsById);
        },
        // order all posts by date and arrange in an object with keys as date, this will be used for interface
        itemsByDay() {
            let _posts = this.posts.items;
            let sortedPosts = _posts.sort((a, b) => {
                return this.$momentUTC(a.publish_at).format("x") - this.$momentUTC(b.publish_at).format("x");
            });

            if(this.type === "published"){
                // for history, we show most recent posts on top; so reverse the posts array
                sortedPosts = sortedPosts.reverse();
            }

            let data = {};
            sortedPosts.forEach(post => {
                let day = this.$momentUTC(post.publish_at).format("MMM Do YYYY");
                data[day] = data[day] || {
                    posts: [],
                    moment: this.$momentUTC(post.publish_at)
                };
                let acc = this.getAccount(post.account_id);
                if(post.post_options && post.post_options.threaded_replies){
                    let replies = post.post_options.threaded_replies;
                    replies = replies.map(reply => {
                        if(typeof reply === 'object'){
                            return reply;
                        }else {
                            let formattedReply = {tweet: reply, media:[]}
                            return formattedReply;
                        }
                    })
                    post.post_options.threaded_replies = replies;
                }
                if (acc.id) {
                    data[day].posts.push($.extend({ accounts: [acc] }, post));
                } else {
                    console.log("no acc for ", post);
                }
            });
            return data;
        },

        // filter
        filterAccounts() {
            if (this.filter.team) {
                return this.accounts.filter(a => a.team && a.team.id === this.filter.team);
            } else {
                return this.accounts.filter(a => !a.team);
            }
        },
        filterNetworks(){
            // only return networks that we have in accounts
            return this.networks.filter(n => {
                return this.filterAccounts.find(a => a.type === n.type);
            });
        },
        filterUsers() {
            if (this.filter.team) {
                const a = this.accounts.find(a => a.team && a.team.id === this.filter.team);
                if (!a) return []; // shouldn't happen

                return a.team.members.map(m => {
                    return {
                        id: m.id,
                        name: m.name
                    };
                });
            }
            return [];
        },
        filterQuery() {
            const filters = [];
            if (this.filter.team) {
                filters.push("team=" + encodeURIComponent(this.filter.team));
            }
            if (this.filter.accounts.length) {
                filters.push(...this.filter.accounts.map(id => "accounts[]=" + encodeURIComponent(id)));
            }
            if (this.filter.user) {
                filters.push("user=" + encodeURIComponent(this.filter.user));
            }
            if (this.filter.keyword) {
                filters.push("q=" + encodeURIComponent(this.filter.keyword));
            }
            return filters.join("&");
        },
        //filter count
        filterCount(){
                let count = 0;
                if (this.filter.team) count++;
                if (this.filter.accounts.length) count++;
                if (this.filter.user) count++;
                if (this.filter.keyword.trim()) count++;
                return count;
        },

        // bulk actions
        postsForBulkActions() {
            return this.posts.items.filter(p => this.selectedItems.includes(p.id) && p.can_edit);
        },
        selectablePostIds() {
            return this.posts.items.filter(p => p.can_edit).map(p => p.id);
        }
    },
    watch: {
        activePosts(postIds) {
            if (postIds && postIds.length) {
                this.$nextTick(() => {
                    let offset = $("#post-" + postIds[0]).offset();
                    if (offset)
                        $("html, body").animate(
                            {
                                scrollTop: offset.top - 100
                            },
                            300
                        );
                    setTimeout(() => {
                        this.activePosts = []; // reset active post
                    }, 3000);
                });
            }
        },
        "filter.team": function(newVal) {
            // whenever team changes, reset accounts and user
            this.filter.accounts = [];
            this.filter.user = null;
        },
        activeFilterQuery(qs) {
            if(qs) {
                // update cookie
                Cookies.set("posts_filters", JSON.stringify({...this.filter}), {
                    sameSite: "None",
                    secure: true
                });
            } else {
                Cookies.remove("posts_filters");
            }
        }
    },
    methods: {
        getContentHelper: () => createContent,
        getIconForAccount,
        getAccountsByType(type) {        
            return this.filterAccounts.filter(account => account.type === type);
        },
        pagination(c, m) {
            let current = c,
                last = m,
                delta = 2,
                left = current - delta,
                right = current + delta + 1,
                range = [],
                rangeWithDots = [],
                l;

            for (let i = 1; i <= last; i++) {
                if (i === 1 || i === last || (i >= left && i < right)) {
                    range.push(i);
                }
            }

            for (let i of range) {
                if (l) {
                    if (i - l === 2) {
                        rangeWithDots.push(l + 1);
                    } else if (i - l !== 1) {
                        rangeWithDots.push("...");
                    }
                }
                rangeWithDots.push(i);
                l = i;
            }

            return rangeWithDots;
        },
        initialize() {
            // set filters from cookie if needed
            const filterJson = Cookies.get("posts_filters") || "";
            if (filterJson) {
                const filters = JSON.parse(filterJson);
                Object.keys(filters).forEach(k => {
                    this.filter[k] = filters[k];
                });
            }
            const showFilter = Cookies.get("posts_filters_show") || false;
            if(showFilter){
                // show the filter form too
                // $("#filter_form").modal("show");
            }

            const onLoad = () => {
                this.loaded = true;

                this.$nextTick(() => {
                    $("#edit_post_publish_at").datetimepicker({
                        inline: true,
                        sideBySide: true,
                        minDate: new Date(),
                        timeZone: appConfig.timezone,
                        icons: {
                            previous: 'ph ph-caret-left ph-md',
                            next: 'ph ph-caret-right ph-md',
                            up: 'ph ph-caret-up ph-bold',
                            down: 'ph ph-caret-down ph-bold'
                        }
                    });
                    // also close filter modal
                    // $("#filter_form").modal("hide");
                });
            };

            this.navigateToPage(1, onLoad, error =>{

                // if this.filter has anything set, reset it and retry loading
                if (this.filter.user || this.filter.accounts.length || this.filter.team || this.filter.keyword) {
                    this.resetFilter(true);
                    this.navigateToPage(1, onLoad, (err) => {
                        // if still error, show the error
                        console.error(err);
                        alertify.error("Failed to load posts: " + err.message + ". Please try again later and refresh.");
                    });
                } else {
                    // if no filter, just show the error
                    console.error(error);
                }

            });
        },
        setType(type) {
            this.type = type;
        },
        async navigateToPage(n, cb, errorCb) {
            n = n || 1;
            if (this.busy) return;
            this.busy = true;

            // close filter form if needed
            $("#posts_filter_form").modal("hide");

            try {
                if (!this.accounts.length) {
                    this.accounts = await getAccounts(true);
                    this.networks = await getNetworks(true);
                }

                const res = await axios.post(
                    "/app/json/collection/posts?page=" +
                        n +
                        "&post_type=" +
                        this.type +
                        (this.filterQuery.length ? "&" + this.filterQuery : "")
                );

                extend(this.posts, res.data);

                this.selectedItems = []; // unselect all posts if needed

                // save current filter query
                this.activeFilterQuery = this.filterQuery;

                cb && cb();
            } catch (e) {
                axiosErrorHandler(e);

                errorCb && errorCb(e);
            } finally {
                this.busy = false;
            }
        },
        getAccount(id) {
            let all = this.accounts,
                account = {};
            all.forEach(acc => {
                if (acc.id === id) account = acc;
            });
            return account;
        },
        onPostUpdated(post) {
            this.posts.items.some((_post, index) => {
                if (post.id === _post.id) {
                    if (post._published || post._deleted || post._draft || post._reviewed) {
                        if (this.type === "draft" && post._draft) {
                            this.posts.items.splice(index, 1, extend({}, _post, post));
                        } else {
                            this.posts.items.splice(index, 1);
                        }
                    } else {
                        this.posts.items.splice(index, 1, extend({}, _post, post));
                    }
                    return true;
                }
            });
        },

        resetFilter(resetOnly = false) {
            Object.keys(this.filter).forEach(n => {
                if (typeof this.filter[n] === "string") {
                    this.filter[n] = "";
                } else if (typeof this.filter[n] === "object") {
                    if (Array.isArray(this.filter[n])) {
                        this.filter[n] = [];
                    } else {
                        this.filter[n] = "";
                    }
                } else {
                    this.filter[n] = null;
                }
            });

            if(!resetOnly) {
            this.navigateToPage(1);
            }
        },

        // bulk actions
        onSelectAll() {
            const selectablePostIds = this.selectablePostIds;
            if (this.selectedItems.length !== this.selectablePostIds.length) {
                // If not all posts are selected, select all
               this.selectedItems = selectablePostIds;
            } else {
                // If all posts are already selected, unselect all
                this.selectedItems = [];
            }
        },
        onSelectPost(id) {
            if (!this.selectablePostIds.includes(id)) {
                return;
            }
            const index = this.selectedItems.indexOf(id);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(id);
            }
        },
        deleteSelected() {
            alertify.delete("All of the selected posts will be deleted. Are you sure?", async () => {
                this.busy = true;
                try {
                    await axios.post("/app/publish/posts/bulk_delete", {
                        ids: this.selectedItems
                    });

                    // delete posts from collection
                    this.posts.items = this.posts.items.filter(p => !this.selectedItems.includes(p.id));

                    this.selectedItems = []; // unselect all posts if needed
                } catch (e) {
                    axiosErrorHandler(e);
                } finally {
                    this.busy = false;
                }
                // if no post, refresh posts
                if (!this.posts.items.length) {
                    this.navigateToPage(this.posts.currentPage);
                }
            });
        },
        draftSelected() {
            alertify.confirm("All of the selected posts will be converted to drafts. Are you sure?", async () => {
                this.busy = true;
                try {
                    await axios.post("/app/publish/posts/bulk_edit", {
                        ids: this.selectedItems,
                        draft: true
                    });

                    // edit
                    this.posts.items = this.posts.items.map(p => {
                        if(this.selectedItems.includes(p.id)){
                            p.draft = true;
                        }
                        return p;
                    });

                    this.selectedItems = []; // unselect all posts if needed
                } catch (e) {
                    axiosErrorHandler(e);
                } finally {
                    this.busy = false;
                }
                // if no post, refresh posts
                if (!this.posts.items.length) {
                    this.navigateToPage(this.posts.currentPage);
                }
            });
        }
    },
    async mounted() {
        const composer = await createContent.getPostComposer();
        if (composer) {
            composer.setScheduledPostsInstance(this);
        }

        setTimeout(()=> {
            const $editTimeModal = $("#edit_time_popup");
            $editTimeModal
                .modal({
                    backdrop: "static",
                    keyboard: false,
                    show: false
                })
                .on("shown.bs.modal", async function(e) {
                })
                .on("hidden.bs.modal", function(e) {
                });
        }, 100)

    }
};
</script>
<style lang="scss">
.post-section {
    margin-bottom: 12px;
    .post-select-input {
        position: absolute;
        top: 12px;
        z-index: 1;
        left: 12px;
        display: none;
        border: 1.4px solid #E3E6EB !important;
    }
    &:hover{
        .post-select-input{
            display: block;
        }
    }
}

.bulk-selection-btn{
    top: 48px;
}
.sticky-top{
    z-index: 100 !important;
}
</style>
