<?php

namespace App\Respond\Objects\TwitterProfileTweet;

use App\Respond\BaseObject;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class Tweet extends BaseObject
{
    /**
     * @throws \Exception
     */
    public function performReply(array $data){
        try {
            \Validator::make($data, [
                'text' => 'string|max:280|required_without:attachments',
                'attachments' => 'array|nullable',
                'attachments.*' => 'file|mimes:jpg,jpeg,png,gif,mp4|max:5120', // 5MB max
            ])->validate();
        } catch (ValidationException $e) {
            throw new \InvalidArgumentException(Arr::first($e->errors()));
        }

        // todo: implement reply using Twitter API
    }
}
