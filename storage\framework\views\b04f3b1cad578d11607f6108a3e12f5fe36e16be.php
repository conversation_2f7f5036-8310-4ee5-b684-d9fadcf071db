<div id="sidebar-menu" class="sidebar-sticky d-flex flex-column">

    <div class="pl-2" data-tour="id=sidebar_left_menu;title=Using SocialBu;text=You can use SocialBu to do a lot of things. This menu is a doorway to all of our features.;position=right">

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('publish.', true) ? ' active ' : ''; ?>" href="<?php echo e(route('publish.posts')); ?>">
                    <i class="ph ph-paper-plane-tilt ph-lg mr-2"></i>
                    Publish 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#publish-submenu">
                        <i class="ph ph-caret-down ph-md text-secondary"></i>
                    </span>
                </a>
                <ul id="publish-submenu" class="submenu collapse <?php if(isActiveRoute('publish.', true)): ?>show <?php endif; ?>">
                    
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.posts') ? 'bg-lightest text-primary border-2 rounded' : ''); ?>" href="<?php echo e(route('publish.posts')); ?>">
                                Scheduled
                                <span style="display:none" class="small" data-var="posts.scheduled" data-prepend="(" data-append=")"></span>
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.drafts') ? 'bg-lightest text-primary border-2 rounded' : ''); ?>" href="<?php echo e(route('publish.drafts')); ?>">
                                Drafts
                                <span style="display:none"  class="small" data-var="posts.draft" data-prepend="(" data-append=")"></span>
                            </a>
                        </li>
                        <?php if(\Auth::user()->joinedTeams()->count() > 0): ?>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                                <a class="nav-link ml-5 pl-4 py-2 <?php echo e(isActiveRoute('publish.awaiting_approval') ? 'bg-lightest text-primary border-2 rounded' : ''); ?>" href="<?php echo e(route('publish.awaiting_approval')); ?>">
                                    Awaiting Approval
                                    <span style="display:none"  class="small" data-var="posts.awaiting_approval" data-prepend="(" data-append=")"></span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.calendar') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('publish.calendar')); ?>">
                                Calendar
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.queues.', true) ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('publish.queues.index')); ?>">
                                Queues
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.bulk_upload') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('publish.bulk_upload')); ?>">
                                Bulk Import
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-lightest position-relative">
                            <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('publish.history') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('publish.history')); ?>">
                                History
                            </a>
                        </li>
                    </ul>
            </li>
        </ul>
        
        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('generate.', true) ? ' active' : ''; ?>" href="<?php echo e(route('generate.index')); ?>">
                    <i class="ph ph-sparkle ph-lg mr-2"></i>
                    Generate 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#generate-submenu">
                    </span>
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('curate.', true) ? ' active' : ''; ?>" href="<?php echo e(route('curate.index')); ?>">
                    <i class="ph ph-list-magnifying-glass ph-lg mr-2"></i>
                    Curate
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('feeds.', true) ? ' active' : ''; ?>" href="<?php echo e(route('feeds.index')); ?>">
                    <i class="ph ph-chat-text ph-lg mr-2"></i>
                    <?php echo app('translator')->get('generic.respond'); ?>
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('analyze.', true) ? ' active pt-2 pb-2' : ''; ?>" href="<?php echo e(route('analyze.content-performance')); ?>">
                    <i class="ph ph-chart-line ph-lg mr-2"></i> 
                    Analyze 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#analyze-submenu">
                        <i class="ph ph-caret-down ph-md text-secondary"></i>
                    </span>
                </a>
                <ul id="analyze-submenu" class="submenu collapse <?php if(isActiveRoute('analyze.', true)): ?>show <?php endif; ?>">
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('analyze.content-performance') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('analyze.content-performance')); ?>">
                             Content
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('analyze.account-performance') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('analyze.account-performance')); ?>">
                             Account
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a title="Instagram Captions" class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('analyze.network-performance') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('analyze.network-performance')); ?>">
                             Network
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-lightest position-relative">
                        <a class="nav-link ml-5 px-4 py-3 <?php echo e(isActiveRoute('analyze.team-performance') ? 'bg-lightest text-primary border-2 rounded' : ' '); ?>" href="<?php echo e(route('analyze.team-performance')); ?>">
                             Team
                        </a>
                    </li>
                </ul>
            </li>
        </ul>

        <ul class="nav flex-column main-menu mb-5">
            <li class="nav-item">
                <a class="nav-link<?php echo isActiveRoute('automations.', true) ? ' active' : ''; ?>" href="<?php echo e(route('automations.index')); ?>">
                    <i class="ph ph-repeat ph-lg mr-2"></i>
                    <?php echo app('translator')->get('generic.automate'); ?>
                </a>
            </li>
        </ul>
    </div>

    <?php if(\Auth::user()->onboarding()->inProgress()): ?>
        <div  class="nav flex-column main-menu mt-auto p-0 border-top-1" id="checklistSection"
             data-tour="id=sidebar_left_menu_onboarding;title=Get Ready;text=Follow these steps to complete your account set up and get the most out of SocialBu;position=right">
            <?php ($steps = \Auth::user()->onboarding()->steps()); ?>
            <?php ($isVerified = \Auth::user()->verified); ?>
            <?php ($timeZone = \Auth::user()->getTimezone(null)); ?>
            <?php ($hasAccounts = \Auth::user()->accounts()->exists()); ?>
            <div class="p-5 pb-0">
                <div class = "d-inline-flex">
                    <h6 class="font-weight-400">Complete your setup</h6>
                    <?php if( $isVerified && $timeZone && $hasAccounts): ?>
                        <span id="removeChecklist" title="Remove" class="cursor-pointer pl-3 font-weight-400">
                            <span class="ph ph-x"> </span>
                        </span> 
                    <?php endif; ?>
                </div>
                <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="pl-2 <?php echo e($step->complete() ? ' disabled' : ''); ?>" href="<?php echo e($step->link); ?>">
                        <a class="text-body" href="<?php echo e($step->link); ?>" <?php if(!$step->complete()): ?> title="<?php echo e($step->title); ?>" data-toggle="tooltip" <?php endif; ?>>
                            <?php if(!$step->complete()): ?>
                                <?php echo e($loop->iteration); ?>. <?php echo e($step->cta); ?>

                            <?php else: ?>
                                <del><?php echo e($loop->iteration); ?>. <?php echo e($step->cta); ?></del>
                            <?php endif; ?>
                        </a>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="nav flex-column main-menu mt-auto chat_button mb-4">
        <div class="">
            <button type="button" class="ml-3 open_chat_bu_modal btn btn-outline-dark btn-lg d-flex align-items-center cursor-pointer position-relative">
                <div>
                    <i class="ph ph-fill ph-circle ph-sm text-success position-absolute chat-success-icon"></i>
                    <div class="d-flex align-items-center">
                        <i class="ph ph-chats-circle ph-lg mr-2"></i>
                        <p class="mb-0">
                            Bu AI
                        </p>
                    </div>
                </div>
                
            </button>
        </div>
    </div>

</div>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/partials/user/sidebar_left.blade.php ENDPATH**/ ?>