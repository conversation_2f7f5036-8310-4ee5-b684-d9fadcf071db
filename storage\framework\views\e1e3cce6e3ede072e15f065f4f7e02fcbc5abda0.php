
<meta charset="utf-8" />
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="x-app-config" content="<?php echo e(json_encode([
    "name" => config("app.name"),
    "timezone" => timezone(),
    "userId" => Auth::check() ? \Auth::id() : null,
    "userCompany" => Auth::check() ? user()->company : null,
    "completedTourSteps" => Auth::check() ? user()->getOption("completed_tour_steps", "") : null,
    "userEmail" => Auth::check() ? user()->email : null,
])); ?>" />

<!--
<meta name="description" content="" />
-->


<link rel="icon" href="/favicon.ico" />


<title>
    <?php echo $__env->yieldContent('title'); ?>
</title>

<link href="<?php echo e(mix('/css/new-app.css')); ?>" rel="stylesheet" type="text/css" />

<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
<script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->

<script>
    window.dataLayer = window.dataLayer || [];
    window.__recordEvent = function(name, data) {
        // this is for sending event/data to tag manager
        var dataToSend = {
            event: name
        };
        if(data){
            for (var attrname in data) { dataToSend[attrname] = data[attrname]; }
        }
        window.dataLayer.push(dataToSend);
    };
    window.__recordUsageEvent = function(name, value, recordInCrm){
        // sample event names: team_edit, post_composer_opened, post_created, etc (object_action)
        // the tag manager handles this custom dynamic event
        // we remove the prepended string and then record the event in other places like Google Analytics
        if(!value) value = "";
        if(recordInCrm === undefined){
            // record in crm by default
            recordInCrm = true;
        }

        window.__recordEvent("ProductUsage::" + name, {
            usageData: typeof value === "object" ? JSON.stringify(value) : value,
            // below is for services requiring JS object
            jsonUsageData: JSON.stringify(typeof value === "object" ? value : {
                value: value
            }),
        });
        if(window.__recordCrmEvent && recordInCrm){
            // record event by sending it to our backend (which will send it to our CRM)
            window.__recordCrmEvent(name, value);
        }
    };

    <?php if(\Auth::check()): ?>
        window.dataLayer = [{
            userId: "<?php echo e(\Auth::id()); ?>",
            userName: "<?php echo e(\Auth::user()->name); ?>",
            userEmail: "<?php echo e(\Auth::user()->email); ?>",
            userCreatedAt: "<?php echo e(\Auth::user()->created_at->toISOString()); ?>",
            userCompany: "<?php echo e(\Auth::user()->company); ?>",
            userPlan: "<?php echo e(\Auth::user()->getPlan(true)); ?>",
            postCreated: "<?php echo e(\Auth::user()->getOption('post_created', 'false') ? 'true' : 'false'); ?>",
            accountAdded: "<?php echo e(\Auth::user()->getOption('account_added', 'false') ? 'true' : 'false'); ?>",
            teamJoined: "<?php echo e(\Auth::user()->getOption('team_joined', 'false') ? 'true' : 'false'); ?>",

        }];
    <?php endif; ?>
    

    <?php if(session()->has('support_login')): ?>
        window.__recordEvent("SupportAgentActivity");
    <?php else: ?> 
        window.__recordEvent("TriggerActiveCampaign");
    <?php endif; ?>
</script>

<?php echo $__env->make('layout.includes.common_head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/includes/user/head.blade.php ENDPATH**/ ?>