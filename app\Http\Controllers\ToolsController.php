<?php

namespace App\Http\Controllers;

use App\Helpers\ContentGenerator;
use Illuminate\Http\Request;
use Intervention\Image\Facades\Image;
use Intervention\Image\Gd\Font;

class ToolsController extends Controller
{
    public function index(){
        return view('common.tools.index');
    }

    public function generateCaptions(){
        return view('common.tools.generate-captions');
    }

    public function generatePosts(){
        return view('common.tools.generate-posts');
    }
    
    public function generateUsername(Request $request, $network = null){
        if(isset($network) && !in_array($network, ['snapchat', 'facebook', 'instagram', 'tiktok', 'youtube'])){
            abort(400, 'Invalid network');
        }

        $view = isset($network) ? 'common.tools.generate-username.' . $network : 'common.tools.generate-username.index';

        if(!view()->exists($view)){
            abort(404, 'Invalid network');
        }

        return view($view);
    }

    public function generateLinkedinPosts(){
        return view('common.tools.linkedin-post-generator');
    }
    public function generateTweets(){
        return view('common.tools.tweet-generator');
    }
    public function generatePromptForText2Img(){
        return view('common.tools.generate-prompt-text2img');
    }

    public function generateBlogImage(){
        return view('common.tools.generate-blog-image');
    }

    public function generateQuoteImage(){
        return view('common.tools.generate-quote-image');
    }

    public function generateMeme(){
        return view('common.tools.generate-meme');
    }

    /**
     * @param Request $request
     * @return string
     * @throws \Exception
     */
    public function contentGenerator(Request $request){
        $this->validate($request, [
            'type' => 'required|string',
        ]);

        if($request->input('topic')){
            $this->validate($request, [
                'topic' => 'required|string|min:3|max:150',
            ]);

            $modelToUse = 'gpt-3';
            $topic = trim($request->input('topic'));
            $type = trim($request->input('type'));
            try {
                $tries = 1;
                do {
                    $data = ContentGenerator::getInstance()->generatePost($topic, null, $modelToUse, $type);

                    $text = trim( $data['post'] );

                    // trim special non-break-space
                    $text = trim($text, ' ');

                    $uniqueChars = count( array_unique( str_split( $text)));
                    if($uniqueChars < 3){
                        // probably not correct, so try again
                        $text = '';
                    } else if(strlen(trim($text)) < 3){
                        // we failed to generate content, so try again
                        $text = '';
                    }


                    ++$tries;
                } while (empty( $text ) && $tries <= 10);
                return $text;
            } catch (\Exception $exception){
                abort(400, $exception->getMessage());
            }
        } else if($request->input('content')){

            $this->validate($request, [
                'content' => 'required|string|min:3|max:1000',
            ]);
            $text = trim($request->input('content'));
            if($request->input('type') === 'prompt_text2img'){
                try {
                    $tries = 1;
                    do {
                        $data = ContentGenerator::getInstance()->generateImagePrompt($text, 'content body');
                        $prompt = trim( $data['prompt'] );
                        ++$tries;
                    } while (empty( $prompt ) && $tries <= 10);
                    return $prompt;
                } catch (\Exception $exception){
                    abort(400, $exception->getMessage());
                }
            } else if($request->input('type') === 'blog_text2img'){
                try {
                    $path = ContentGenerator::getInstance()->generateImageFromText($text, 'blog post text');
                    return response()->download($path, 'media.jpg')->deleteFileAfterSend(true);
                } catch (\Exception $exception){
                    abort(400, $exception->getMessage());
                }
            } else if($request->input('type') === 'quote_text2img'){
                try {
                    $path = ContentGenerator::getInstance()->generateImageFromText($text, 'quote text');
                    return response()->download($path, 'media.jpg')->deleteFileAfterSend(true);
                } catch (\Exception $exception){
                    abort(400, $exception->getMessage());
                }
            } else if($request->input('type') === 'meme_text2img'){
                try {
                    $memeData = ContentGenerator::getInstance()->generateMemeContents($text);

                    $contents = $memeData['contents'];
                    $path = ContentGenerator::getInstance()->generateImageUsingOpenAI($contents['image_from_second_party'], 512, 512);

                    $img = Image::make($path);

                    $imgHeight = $img->height();
                    $imgWidth = $img->width();

                    $outputImg = Image::canvas($imgWidth, $imgHeight + (int) ($imgHeight/6), '#ffffff');
                    $outputImg->insert($img, 'bottom');

                    // calculate font size by character count
                    $str1 = $contents['first_party'] .': ' . $contents['text_from_first_party'];
                    $fontSize1 = (int) ($imgWidth / (strlen($str1) / 2));

                    $str2 = $contents['second_party'] .':';
                    $fontSize2 = (int) ($imgWidth / (strlen($str2) / 2));

                    // maximum font size is 40
                    $fontSize1 = min($fontSize1, 40);
                    $fontSize2 = min($fontSize2, 40);

                    $outputImg->text($contents['first_party'] .': ' . $contents['text_from_first_party'], $imgWidth / 40, 10, function($font) use($fontSize1) {
                        /** @var $font Font */
                        $font->file(resource_path('helvetica.ttf'));
                        $font->size($fontSize1);
                        $font->color('#000000');
                        $font->valign('top');
                    });

                    $outputImg->text($contents['second_party'] .':', $imgWidth / 40, 10 + $fontSize1, function($font) use($fontSize2) {
                        /** @var $font Font */
                        $font->file(resource_path('helvetica.ttf'));
                        $font->size($fontSize2);
                        $font->color('#000000');
                        $font->valign('top');
                    });

                    $newFile = tempnam(sys_get_temp_dir(), config('app.name'));

                    $outputImg->save($newFile, null, 'jpg');

                    // delete $path file
                    unlink($path);

                    return response()->download($newFile, 'meme.jpg')->deleteFileAfterSend(true);
                } catch (\Exception $exception){
                    abort(400, $exception->getMessage());
                }
            }

        }

        return null;
    }
    public function generateFacebookPosts(){
        return view('common.tools.facebook-post-generator');
    }
    public function generateTiktokCaptions(){
        return view('common.tools.tiktok-caption-generator');
    }    
    public function generateInstagramCaptions(){
        return view('common.tools.instagram-caption-generator');
    }
    public function generateAIPosts(){
        return view('common.tools.ai-post-generator');
    }
    public function generateViralHook(){
        return view('common.tools.viral-hook-generator');
    }
    public function captionExpander(){
        return view('common.tools.caption-expander');
    }
    public function generateEmoji(){
        return view('common.tools.emoji-and-symbol-generator');
    }
}