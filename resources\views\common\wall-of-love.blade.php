@extends('layout.default')
@php($title = 'Wall of Love')
@php($description = 'SocialBu is a social media management tool that is highly valued by social media managers, marketing agencies, and brands. See what our customers are saying about our platform.')
@php($image = 'https://socialbu.com/images/site/link-preview.jpg')
@php($url = 'https://socialbu.com/love')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
<meta name="description" content="{{ $description }}" />
<link rel="canonical" href="{{ $url }}" />

<meta property="og:locale" content="en_US" />
<!--
    <meta property="og:type" content="website" />
    -->
<meta property="og:title" content="{{ $title }}" />
<meta property="og:description" content="{{ $description }}" />
<meta property="og:url" content="{{ $url }}" />
<meta property="og:site_name" content="SocialBu" />
<meta property="og:image" content="{{ $image }}" />
<meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="{{ $image }}" />
<meta name="twitter:title" content="{{ $title }}" />
<meta name="twitter:description" content="{{ $description }}" />
<meta name="twitter:site" content="@socialbuapp" />
<style>
    .logo-carousel{
        height: 50px;
    }
    .logo{
        height: 50px;
    }
    .logo-carousel-track{
        white-space: nowrap;
        animation: movelogo 35s linear infinite;
        top:0; 
        left:0; 

    }
    @keyframes movelogo {
        0% {
            transform: translate(0);
        }
        100% {
            transform: translate(-50%);
        }
    }
</style>
@endpush
@section('content')
<header class="header pt-4">
    <div class="col-12 text-center p-0 px-md-10 px-5 py-7">
        <h1 class="display-1">Wall of Love</h1>
        <p class="lead-2">A space to celebrate the kind words, amazing stories, and<br class="d-none d-lg-block"> heartfelt support from our users, fans, and friends.</p>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="display-3 mb-6">Our best customers</h2>
                <div class="logo-carousel position-relative overflow-hidden w-100" aria-label="Customer logos">
                    <div class="logo-carousel-track position-absolute d-inline-block w-auto">
                        @for ($repeat = 0; $repeat < 2; $repeat++) {{-- Duplicate list for seamless animation --}}
                            @foreach([
                                ['name' => 'Wired Productions', 'logo' => 'wired-productions.webp', 'styles' => 'rounded-lg'],
                                ['name' => 'Academia Central', 'logo' => 'academia-central.webp', 'styles' => ''],
                                ['name' => 'IntergenData', 'logo' => 'intergendata.webp', 'styles' => 'bg-dark rounded-lg p-2'],
                                ['name' => 'Kovitz', 'logo' => 'kovitz.webp', 'styles' => ''],
                                ['name' => 'SMSA', 'logo' => 'smsa.webp', 'styles' => 'rounded-lg'],
                                ['name' => 'Netways', 'logo' => 'netways.webp', 'styles' => ''],
                                ['name' => 'LUXVT', 'logo' => 'luxvt.webp', 'styles' => ''],
                                ['name' => 'ReadyWise', 'logo' => 'readywise.webp', 'styles' => ''],
                                ['name' => 'Uniclox', 'logo' => 'uniclox.png', 'styles' => ''],
                                ['name' => 'WirelessDNA', 'logo' => 'wirelessdna.webp', 'styles' => ''],
                            ] as $index => $customer)
                                <img class="lozad w-auto logo {{$customer['styles']}} mr-6" src="{{ asset('images/wall_of_love/' . $customer['logo']) }}" title="{{ $customer['name'] }}" alt="{{ $customer['name'] }} logo">
                            @endforeach
                        @endfor
                    </div>
                </div>
            </div>
        </div>
    </div>

</header>
<main class="main-content">
    <div class="px-md-10 px-5">
        <script src="https://widget.senja.io/widget/02f76a54-efd6-4c85-98af-5a4291f98eaf/platform.js" type="text/javascript" async></script>
        <div class="senja-embed" data-id="02f76a54-efd6-4c85-98af-5a4291f98eaf" data-mode="shadow" data-lazyload="false" style="display: block;"></div>
    </div>
    @include('common.internal.join-us-block')
</main>
@endsection
