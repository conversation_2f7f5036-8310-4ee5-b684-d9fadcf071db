body {
}
.text-primary{
    color: $color-primary !important;
}
.text-secondary{
    color: $gray-700 !important;
}
.text-danger{
    color: $color-danger !important;
}
.text-info{
    color: $color-info !important;
}
.text-warning{
    color: $color-warning !important;
}
.text-light{
    color: $color-text-light !important;
}
.text-dark{
    color: $color-text-dark !important;
}
.text-blue {
    color: #2370FF !important;
}
.text-gray-light {
    color: $gray-500 !important;
}
.display-1, .display-2{
    letter-spacing: -0.32px;
}
.display-5 {
    @include font-size($display5-size);
    font-weight: $display5-weight;
    line-height: $display-line-height;
}
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6{
    font-family: 'Plus Jakarta Sans';
}
@media (max-width: 450px) {
    .display-1,.display-2{
        font-size: 40px !important;
        line-height: 44px;
    }
    .display-4{
        font-size: 32px !important;
        line-height: 38.4px;
    }
    .display-6{
        font-size: 24px !important;
        line-height: 28.8px;
    }
}
//----------------------------------------------------/
// text-white
//----------------------------------------------------/
.text-white {
    color: rgba(#fff, 1) !important;
    h1, h2, h3, h4, h5, h6,
    .h1, .h2, .h3, .h4, .h5, .h6 {
        color: #fff;
    }
    a:not(.btn):not(.dropdown-item) {
        color: rgba(#fff, 1);
    }
    hr {
        border-top-color: $color-alpha-light-divider;
    }
    p {
        color: rgba(#fff, 1);
    }
    code {
        background-color: rgba(255,255,255,.1);
        color: #eee;
    }
    small,
    .small {
        color: rgba(#fff, 1);
    }
    .divider {
        color: rgba(#fff, 1);
    }
    .section-header {
    small {
        color: rgba(#fff, 1);
    }
    }
    .rating label.empty {
        color: rgba(#fff, 1);
    }
    .card-inverse {
        background-color: rgba(#fff, 1);
    }
    .btn-outline-light {
        color: rgba(#fff, 1);
        border-color: rgba(#fff, 1);
        &:hover {
            color: $color-text;
        }
    }
    .close {
        color: rgba(#fff, 1);
    }
}
.display-1,.display-2,.display-3,.display-4,.display-5,.display-6{
    line-height: 1.2 !important;  
    font-feature-settings: "liga"
}
