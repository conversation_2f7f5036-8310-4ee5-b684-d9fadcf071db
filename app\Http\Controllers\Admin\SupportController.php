<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

class SupportController extends Controller
{
    private $admins = [
        8, // umar
        5, // usama
        46222, // ghina rehman
        68455, // farwa
        5695, // ayesha
    ];

    public function loginAs($userId){

        $adminName = \Auth::user()->name;

        if(in_array(\Auth::id(), $this->admins)){

            $user = findUser($userId);

            if(!$user) abort(404);


            if(in_array($user->id, $this->admins)){
                abort(400);
            } else if($user)
                \Auth::login($user);

            // notify chat
            notify_chat('📝 ' . $adminName . ' logged in as ' . $user->name . '(' . $user->email . ')');

            session()->put('support_login', true);

            return redirect('/app');
        } else {
            abort(404);
        }
        return null;
    }

}
