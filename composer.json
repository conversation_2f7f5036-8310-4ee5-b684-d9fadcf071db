{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=7.3.0", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-pdo": "*", "abraham/twitteroauth": "3.*", "calebporzio/onboard": "v1.7", "camroncade/timezone": "^0.1.0", "caseyamcl/guzzle_retry_middleware": "^2.8", "composer/package-versions-deprecated": "*********", "consoletvs/charts": "6.*", "doctrine/dbal": "^2.9", "donatello-za/rake-php-plus": "^1.0", "facebook/graph-sdk": "^5.4", "fideloper/proxy": "^4.0", "firebase/php-jwt": "^5.2", "google/apiclient": "^2.12.1", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^6.2", "guzzlehttp/oauth-subscriber": "0.3.*", "intervention/image": "^2.5", "jean85/pretty-package-versions": "*", "laravel-validation-rules/phone": "^1.5", "laravel/cashier": "~10.0", "laravel/framework": "6.20", "laravel/helpers": "^1.7", "laravel/horizon": "^3.7", "laravel/passport": "9.3.2", "laravel/socialite": "^4.4", "laravel/tinker": "^1.0", "lcobucci/jwt": "3.3.3", "league/color-extractor": "0.3.*", "league/flysystem-aws-s3-v3": "^1.0", "league/flysystem-cached-adapter": "^1.0", "league/oauth2-client": "^2.6", "liquid/liquid": "^1.4", "maatwebsite/excel": "^3.1", "malahierba-lab/public-id": "dev-main", "mariuzzo/laravel-js-localization": "^1.4", "mattketmo/email-checker": "^1.5", "mgp25/instagram-api": "dev-master", "nesbot/carbon": "~2.0", "nojimage/twitter-text-php": "^3.1", "php-ffmpeg/php-ffmpeg": "^0.16.0", "phplicengine/bitly": "^1.0", "predis/predis": "^1.1", "pusher/pusher-php-server": "~3.0", "ralouphie/mimey": "^2.0", "revolution/laravel-mastodon-api": "^2.4", "sentry/sentry": "4.*", "sentry/sentry-laravel": "4.*", "simplepie/simplepie": "^1.5", "socialiteproviders/bitly": "~4", "socialiteproviders/manager": "~4", "socialiteproviders/reddit": "~4", "spatie/laravel-activitylog": "^3.9.1", "spatie/laravel-sitemap": "^5.2", "symfony/css-selector": "^4.2", "symfony/dom-crawler": "^4.2", "textalk/websocket": "^1.5", "yongfook/bannerbear": "^1.4", "zgabievi/promocodes": "2.3.3", "zoonman/linkedin-api-php-client": "^0.0.14"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.6", "deployer/deployer": "^7.0", "filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "laracasts/generators": "^1.1", "mockery/mockery": "0.9.*", "phpunit/phpunit": "~7.0"}, "repositories": [{"type": "vcs", "url": "*****************:usamaejaz/instagram-api.git"}, {"type": "vcs", "url": "https://github.com/VATSIM-UK/public-id"}], "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/", "InstagramAPI\\": "vendor_override/InstagramAPI", "InstagramAPI\\Request\\": "vendor_override/InstagramAPI/Request", "InstagramAPI\\Media\\Constraints\\": "vendor_override/InstagramAPI/Media/Constraints", "InstagramAPI\\Media\\Constraints\\StoryConstraints\\": "vendor_override/InstagramAPI/Media/Constraints/StoryConstraints", "FFMpeg\\Format\\Video\\": "vendor_override/php-ffmpeg/php-ffmpeg/src/FFMpeg/Format/Video", "Abraham\\TwitterOAuth\\": "vendor_override/abraham/twitteroauth/src"}, "files": ["app/Helpers/functions.php", "app/Helpers/MyBusiness.php"], "exclude-from-classmap": ["vendor/mgp25/instagram-api/src/Constants.php", "vendor/mgp25/instagram-api/src/Request/Internal.php", "vendor/mgp25/instagram-api/src/Media/Constraints/ConstraintsFactory.php", "vendor/mgp25/instagram-api/src/Media/Constraints/StoryConstraints.php", "vendor/php-ffmpeg/php-ffmpeg/src/FFMpeg/Format/Video/WebM.php", "vendor/abraham/twitteroauth/src/TwitterOAuth.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "test": "phpunit", "deploy": "dep deploy", "ide-helper:models": "@php artisan ide-helper:models --write"}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "7.3.13"}, "allow-plugins": {"kylekatarnls/update-helper": true}}, "minimum-stability": "dev", "prefer-stable": true}