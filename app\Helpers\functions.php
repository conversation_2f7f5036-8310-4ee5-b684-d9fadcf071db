<?php

use App\Helpers\EmailListHelper;
use FFMpeg\FFProbe;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

/**
 * Set a flash message in the session.
 *
 * @param  string $message
 * @param string $level
 * @return void
 */
function flash($message, $level = 'message') {
    if($level === 'info') $level = 'message';
    if(!in_array($level, ['success', 'message', 'warning', 'error']))
        $level = 'message';
    session()->flash($level, $message);
}

/*
|--------------------------------------------------------------------------
| Detect Active Route
|--------------------------------------------------------------------------
|
| Compare given route with current route and return output if they match.
| Very useful for navigation, marking if the link is active.
|
*/
function isActiveRoute($route, $roughCheck = false)
{
    if($roughCheck) return strpos(Route::currentRouteName(), $route) !== FALSE;
    return Route::currentRouteName() == $route;
}

/*
|--------------------------------------------------------------------------
| Detect Active Routes
|--------------------------------------------------------------------------
|
| Compare given routes with current route and return output if they match.
| Very useful for navigation, marking if the link is active.
|
*/
function areActiveRoutes(Array $routes)
{
    foreach ($routes as $route)
    {
        if (Route::currentRouteName() == $route) return true;
    }
    return false;
}

/*
|--------------------------------------------------------------------------
| Show Error
|--------------------------------------------------------------------------
|
| Show a page with an error
|
*/
function errorPage(Array $data)
{
    if($data['message'] instanceof \Exception){
        $data['message'] = $data['message']->getMessage();
    }
    return response()->view('errors.error', [
        'title' => $data['title'],
        'message' => $data['message']
    ], 500);
}

/**
 * @param $command
 * @param $bucket
 * @param array $data
 * @return string Pre-signed URL
 */
function getSignedUrl($command, $bucket, array $data = []){
    $adapter = \Storage::cloud()->getAdapter(); // Get the filesystem adapter
    $client = $adapter->getClient(); // Get the aws client
    $commandData = array_merge($data, ['Bucket'=> $bucket, 'ACL' => 'private']);
    
    $cmd = $client->getCommand($command, $commandData);
    $request = $client->createPresignedRequest($cmd, '+30 minutes');
    return (string) $request->getUri();
}

/**
 * Reduce image size below given maxSize
 * @param string $fullPath
 * @param int $maxSize
 * @return bool
 */
function reduce_image_size_if_needed(string $fullPath, int $maxSize){

    $size = filesize($fullPath);

    $img = \Intervention\Image\Facades\Image::make($fullPath);

    $attempt = 0;
    $totalAttempts = 20;
    do {
        $attempt++;
        // is image, check size and make sure it's less than 976kb
        if($size > $maxSize){
            // we optimize the image using Image lib
            // if the dimensions are too big (more than FHD), resize
            if($img->width() > 1920){
                $img->resize(1920, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }

            if ($img->height() > 1080) {
                $img->resize(null, 1080, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }

            $img->save($fullPath, 90 - ( ( ($attempt - 1) / $totalAttempts ) * 30));

            $size = filesize($fullPath);
        }
    } while($size > $maxSize && $attempt <= $totalAttempts);

    return $size <= $maxSize;
}

/**
 * @return \App\User|null
 */
function user(){
    /** @var \App\User $user */
    $user = \Auth::user();
    return $user;
}

function findUser($id_or_email){
    if(is_numeric($id_or_email)) {
        $user = \App\User::find($id_or_email);
    } else {
        $user = \App\User::whereEmail($id_or_email)->first();
    }
    return $user;
}

function generatePromoForPlan($id = 'standard2', $expiresAt = null, $amount = 1, $expiresInDays = 14, $planConfig = []){
    if(!$expiresAt){
        $expiresAt = now()->addMonths(2)->getTimestamp();
    } else {
        if($expiresAt instanceof \Carbon\Carbon){
            $expiresAt = $expiresAt->getTimestamp();
        }
    }
    return Promocodes::createDisposable($amount, null, array_filter([
        'subscription_plan' => [
            'id' => $id,
            'valid_until' => $expiresAt,
        ],
        'plan_config' => $planConfig, // custom plan config
    ]), $expiresInDays);
}

/*
|--------------------------------------------------------------------------
| Get Pricing Details
|--------------------------------------------------------------------------
|
| This will return the pricing details as set in config.
|
*/
function getPlan($type, $user = null){

    $plan = config('plans.' . $type, config('plans.free'));

    if(!$user) {
        $user = user();
    }

    if($user){

        $limitsOrig = $plan['limits'];
        $limitsCustom = [];

        $customQty = $user->getOption('subscription_quantity.' . $type);
        if( $customQty  ){
            // quantity is custom
            $limitsToIncrease = ['accounts', 'monthly_posts',];

            // set new limits
            foreach ($limitsToIncrease as $limit){
                if(isset($limitsOrig[$limit])){
                    $limitsCustom[$limit] = $limitsOrig[$limit] * $customQty;
                }
            }
        }

        if($user->getOption('custom_limits.' . $type))
            $limitsCustom = array_merge($limitsCustom, $user->getOption('custom_limits.' . $type));

        $finalLimits = array_merge($limitsOrig, $limitsCustom);

        $plan['limits'] = $finalLimits;

        if($user->getOption('custom_features.' . $type))
            $plan['features'] = array_merge(isset($plan['features']) ? $plan['features'] : [], $user->getOption('custom_features.' . $type));

    }

    return $plan;

}

/**
 * @param $object
 * @return \App\User|null
 */
function getResourceOwner($object){
    $user = user();
    if ($object instanceof \App\Account) {
        $team = $object->getTeam();
        if ($team) {
            $user = $team->user;
        } else {
            $user = $object->user;
        }
    } else if ($object instanceof \App\Team) {
        $user = $object->user;
    } else if ($object instanceof \App\User) {
        $user = $object;
    } else if ($object instanceof \App\PublishQueue) {
        if($object->team){
            $user = $object->team->user;
        } else {
            $user = $object->user;
        }
    } else if ($object instanceof \App\Automation) {
        if($object->team){
            $user = $object->team->user;
        } else {
            $user = $object->user;
        }
    } else if ($object instanceof \App\Feed) {
        if($object->team){
            $user = $object->team->user;
        } else {
            $user = $object->user;
        }
    } else if($object){
        if(property_exists($object, 'team')){
            $user = $object->team->user;
        } else if(property_exists($object, 'user')) {
            $user = $object->user;
        }
    }
    return $user;
}

/**
 * @param $limitType
 * @param null $object
 * @return array|int
 */
function getUsage($limitType, $object = null){
    $user = null;

    if($object){
        $user = getResourceOwner($object);
    }

    if(!$user){
        $user = user();
    }

    return $user->getUsage($limitType);
}

/**
 * @param $limitType
 * @param null $object
 * @return array|int
 */
function getPlanUsage($limitType, $object = null){
    $user = null;
    if($object){
        $user = getResourceOwner($object);
    }

    if(!$user){
        $user = user();
    }

    return $user->getPlanUsage($limitType);
}

/**
 * @param $featureKey
 * @param null $object
 * @return bool
 */
function planHasFeature($featureKey, $object = null){
    $user = user(); // should be the user that should be checked for limits
    if($object){
        $user = getResourceOwner($object);
    }
    return $user->planHasFeature($featureKey);
}

/*
|--------------------------------------------------------------------------
| Get current timezone to use
|--------------------------------------------------------------------------
|
| This will return the user timezone (if logged in, else from browser header
| else the default
|
*/
function timezone($user = null){

    if(!$user) {
        $user = user();
    }
    if($user){
        return $user->getTimezone();
    }
    //Todo: return from browser headers if possible
    return 'Asia/Karachi';
}

/**
 * Check if string is valid json
 * @param mixed $string
 * @return bool
 */
function isJson($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

function placeholders_extract(string $string){
    preg_match_all(

        '/{{(.*)}}/U',

        // the callback, uses $allowed array of possible variables
        $string,

        // the input string
        $matches
    );
    if(isset($matches[1]))
        return collect($matches[1])->map(function($key){ return trim($key); })->toArray();
    else
        return [];
}

/**
 * Replaces placeholders in string with format: {{ format_here }}
 * @param string $string
 * @param array $data
 * @param bool $json
 * @return string
 */
function placeholders_replace(string $string, array $data, bool $json = false){
    return preg_replace_callback(

        '/{{(.*)}}/U',

        // the callback, uses $allowed array of possible variables
        function( $matches ) use ( $data, $json)
        {

            $original = $matches[0];

            $key = trim( $matches[ 1 ] );

            $replace = '';
            if(Str::contains($key, [
                ':', // for filters
                '|', // for filters
            ])){
                // we should use liquid to parse the string
                $template = new \Liquid\Template();
                $template->parse($original);
                $replace = $template->render($data);
            } else {
                $replace = data_get($data, $key, 'NOT_FOUND_SHIT_037');

                if (is_array($replace)) {
                    $replace = json_encode($replace); // convert to json if its an array
                } else if (is_bool($replace)) {
                    // show true / false for booleans
                    $replace = $replace ? 'true' : 'false';
                } else if($replace === 'NOT_FOUND_SHIT_037'){
                    return $original;
                }
            }

            if ($json){
                $encoded = trim(json_encode($replace));

                // get the last and first character and check if it's "
                if( Str::startsWith($encoded, '"') && Str::endsWith($encoded, '"') ){
                    return substr($encoded, 1, -1);
                }

                return $encoded;
            }

            return $replace;
        },

        // the input string
        $string
    );
}

/**
 * Use Liquid to transform placeholders
 * @param $input mixed
 * @param array $data
 * @return array|mixed|string
 */
function transform_data_placeholders($input, array $data = [])
{
    if(is_string($input)) {
        $template = new \Liquid\Template();
        $template->parse($input);
        return $template->render($data);
    } elseif(is_array($input)) {
        $transformed = [];
        foreach ($input as $key => $value) {
            $transformed[$key] = transform_data_placeholders($value, $data);
        }
        return $transformed;
    } else if(is_object($input)){
        foreach ($input as $key => $value){
            $input->{$key} = transform_data_placeholders($value, $data);
        }
    }

    return $input;
}

/**
 * Countries list
 */
function getCountries(){
    return array (
        'AF' => 'Afghanistan',
        'AX' => 'Åland Islands',
        'AL' => 'Albania',
        'DZ' => 'Algeria',
        'AS' => 'American Samoa',
        'AD' => 'Andorra',
        'AO' => 'Angola',
        'AI' => 'Anguilla',
        'AQ' => 'Antarctica',
        'AG' => 'Antigua & Barbuda',
        'AR' => 'Argentina',
        'AM' => 'Armenia',
        'AW' => 'Aruba',
        'AC' => 'Ascension Island',
        'AU' => 'Australia',
        'AT' => 'Austria',
        'AZ' => 'Azerbaijan',
        'BS' => 'Bahamas',
        'BH' => 'Bahrain',
        'BD' => 'Bangladesh',
        'BB' => 'Barbados',
        'BY' => 'Belarus',
        'BE' => 'Belgium',
        'BZ' => 'Belize',
        'BJ' => 'Benin',
        'BM' => 'Bermuda',
        'BT' => 'Bhutan',
        'BO' => 'Bolivia',
        'BA' => 'Bosnia & Herzegovina',
        'BW' => 'Botswana',
        'BR' => 'Brazil',
        'IO' => 'British Indian Ocean Territory',
        'VG' => 'British Virgin Islands',
        'BN' => 'Brunei',
        'BG' => 'Bulgaria',
        'BF' => 'Burkina Faso',
        'BI' => 'Burundi',
        'KH' => 'Cambodia',
        'CM' => 'Cameroon',
        'CA' => 'Canada',
        'IC' => 'Canary Islands',
        'CV' => 'Cape Verde',
        'BQ' => 'Caribbean Netherlands',
        'KY' => 'Cayman Islands',
        'CF' => 'Central African Republic',
        'EA' => 'Ceuta & Melilla',
        'TD' => 'Chad',
        'CL' => 'Chile',
        'CN' => 'China',
        'CX' => 'Christmas Island',
        'CC' => 'Cocos (Keeling) Islands',
        'CO' => 'Colombia',
        'KM' => 'Comoros',
        'CG' => 'Congo - Brazzaville',
        'CD' => 'Congo - Kinshasa',
        'CK' => 'Cook Islands',
        'CR' => 'Costa Rica',
        'CI' => 'Côte d’Ivoire',
        'HR' => 'Croatia',
        'CU' => 'Cuba',
        'CW' => 'Curaçao',
        'CY' => 'Cyprus',
        'CZ' => 'Czechia',
        'DK' => 'Denmark',
        'DG' => 'Diego Garcia',
        'DJ' => 'Djibouti',
        'DM' => 'Dominica',
        'DO' => 'Dominican Republic',
        'EC' => 'Ecuador',
        'EG' => 'Egypt',
        'SV' => 'El Salvador',
        'GQ' => 'Equatorial Guinea',
        'ER' => 'Eritrea',
        'EE' => 'Estonia',
        'ET' => 'Ethiopia',
        'EZ' => 'Eurozone',
        'FK' => 'Falkland Islands',
        'FO' => 'Faroe Islands',
        'FJ' => 'Fiji',
        'FI' => 'Finland',
        'FR' => 'France',
        'GF' => 'French Guiana',
        'PF' => 'French Polynesia',
        'TF' => 'French Southern Territories',
        'GA' => 'Gabon',
        'GM' => 'Gambia',
        'GE' => 'Georgia',
        'DE' => 'Germany',
        'GH' => 'Ghana',
        'GI' => 'Gibraltar',
        'GR' => 'Greece',
        'GL' => 'Greenland',
        'GD' => 'Grenada',
        'GP' => 'Guadeloupe',
        'GU' => 'Guam',
        'GT' => 'Guatemala',
        'GG' => 'Guernsey',
        'GN' => 'Guinea',
        'GW' => 'Guinea-Bissau',
        'GY' => 'Guyana',
        'HT' => 'Haiti',
        'HN' => 'Honduras',
        'HK' => 'Hong Kong SAR China',
        'HU' => 'Hungary',
        'IS' => 'Iceland',
        'IN' => 'India',
        'ID' => 'Indonesia',
        'IR' => 'Iran',
        'IQ' => 'Iraq',
        'IE' => 'Ireland',
        'IM' => 'Isle of Man',
        'IL' => 'Israel',
        'IT' => 'Italy',
        'JM' => 'Jamaica',
        'JP' => 'Japan',
        'JE' => 'Jersey',
        'JO' => 'Jordan',
        'KZ' => 'Kazakhstan',
        'KE' => 'Kenya',
        'KI' => 'Kiribati',
        'XK' => 'Kosovo',
        'KW' => 'Kuwait',
        'KG' => 'Kyrgyzstan',
        'LA' => 'Laos',
        'LV' => 'Latvia',
        'LB' => 'Lebanon',
        'LS' => 'Lesotho',
        'LR' => 'Liberia',
        'LY' => 'Libya',
        'LI' => 'Liechtenstein',
        'LT' => 'Lithuania',
        'LU' => 'Luxembourg',
        'MO' => 'Macau SAR China',
        'MK' => 'Macedonia',
        'MG' => 'Madagascar',
        'MW' => 'Malawi',
        'MY' => 'Malaysia',
        'MV' => 'Maldives',
        'ML' => 'Mali',
        'MT' => 'Malta',
        'MH' => 'Marshall Islands',
        'MQ' => 'Martinique',
        'MR' => 'Mauritania',
        'MU' => 'Mauritius',
        'YT' => 'Mayotte',
        'MX' => 'Mexico',
        'FM' => 'Micronesia',
        'MD' => 'Moldova',
        'MC' => 'Monaco',
        'MN' => 'Mongolia',
        'ME' => 'Montenegro',
        'MS' => 'Montserrat',
        'MA' => 'Morocco',
        'MZ' => 'Mozambique',
        'MM' => 'Myanmar (Burma)',
        'NA' => 'Namibia',
        'NR' => 'Nauru',
        'NP' => 'Nepal',
        'NL' => 'Netherlands',
        'NC' => 'New Caledonia',
        'NZ' => 'New Zealand',
        'NI' => 'Nicaragua',
        'NE' => 'Niger',
        'NG' => 'Nigeria',
        'NU' => 'Niue',
        'NF' => 'Norfolk Island',
        'KP' => 'North Korea',
        'MP' => 'Northern Mariana Islands',
        'NO' => 'Norway',
        'OM' => 'Oman',
        'PK' => 'Pakistan',
        'PW' => 'Palau',
        'PS' => 'Palestinian Territories',
        'PA' => 'Panama',
        'PG' => 'Papua New Guinea',
        'PY' => 'Paraguay',
        'PE' => 'Peru',
        'PH' => 'Philippines',
        'PN' => 'Pitcairn Islands',
        'PL' => 'Poland',
        'PT' => 'Portugal',
        'PR' => 'Puerto Rico',
        'QA' => 'Qatar',
        'RE' => 'Réunion',
        'RO' => 'Romania',
        'RU' => 'Russia',
        'RW' => 'Rwanda',
        'WS' => 'Samoa',
        'SM' => 'San Marino',
        'ST' => 'São Tomé & Príncipe',
        'SA' => 'Saudi Arabia',
        'SN' => 'Senegal',
        'RS' => 'Serbia',
        'SC' => 'Seychelles',
        'SL' => 'Sierra Leone',
        'SG' => 'Singapore',
        'SX' => 'Sint Maarten',
        'SK' => 'Slovakia',
        'SI' => 'Slovenia',
        'SB' => 'Solomon Islands',
        'SO' => 'Somalia',
        'ZA' => 'South Africa',
        'GS' => 'South Georgia & South Sandwich Islands',
        'KR' => 'South Korea',
        'SS' => 'South Sudan',
        'ES' => 'Spain',
        'LK' => 'Sri Lanka',
        'BL' => 'St. Barthélemy',
        'SH' => 'St. Helena',
        'KN' => 'St. Kitts & Nevis',
        'LC' => 'St. Lucia',
        'MF' => 'St. Martin',
        'PM' => 'St. Pierre & Miquelon',
        'VC' => 'St. Vincent & Grenadines',
        'SD' => 'Sudan',
        'SR' => 'Suriname',
        'SJ' => 'Svalbard & Jan Mayen',
        'SZ' => 'Swaziland',
        'SE' => 'Sweden',
        'CH' => 'Switzerland',
        'SY' => 'Syria',
        'TW' => 'Taiwan',
        'TJ' => 'Tajikistan',
        'TZ' => 'Tanzania',
        'TH' => 'Thailand',
        'TL' => 'Timor-Leste',
        'TG' => 'Togo',
        'TK' => 'Tokelau',
        'TO' => 'Tonga',
        'TT' => 'Trinidad & Tobago',
        'TA' => 'Tristan da Cunha',
        'TN' => 'Tunisia',
        'TR' => 'Turkey',
        'TM' => 'Turkmenistan',
        'TC' => 'Turks & Caicos Islands',
        'TV' => 'Tuvalu',
        'UM' => 'U.S. Outlying Islands',
        'VI' => 'U.S. Virgin Islands',
        'UG' => 'Uganda',
        'UA' => 'Ukraine',
        'AE' => 'United Arab Emirates',
        'GB' => 'United Kingdom',
        'UN' => 'United Nations',
        'US' => 'United States',
        'UY' => 'Uruguay',
        'UZ' => 'Uzbekistan',
        'VU' => 'Vanuatu',
        'VA' => 'Vatican City',
        'VE' => 'Venezuela',
        'VN' => 'Vietnam',
        'WF' => 'Wallis & Futuna',
        'EH' => 'Western Sahara',
        'YE' => 'Yemen',
        'ZM' => 'Zambia',
        'ZW' => 'Zimbabwe',
    );
}

/**
 * Convert array to object
 *
 * @param array $array
 * @return mixed
 */
function array_to_object($array){
    if(is_object($array)) return $array; // already object
    // we type cast to object as an empty $array will result in array from json_decode
    return (object) json_decode(json_encode($array));
}

/**
 * Guzzle Client for User Facing logic
 * @param array $opts
 * @return \GuzzleHttp\Client
 */
function guzzle_client($opts = []){
    return new \GuzzleHttp\Client(array_merge([
        'connect_timeout' => 30,
        'read_timeout' => 30,
        'timeout' => 30,
        'headers' => [
            'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
        ],
        'verify' => false,
    ], $opts));
}

/**
 * @throws Exception
 */
function fetch_html($url, $tries = 1): string
{
    $randomProxy = null;
    if(app()->environment('production')) {
        // set random proxy
        // needed because a lot of requests can result in getting temporarily rate-limited or being blocked
        $randomProxy = array_random(config('app.proxies'));
    }

    $config = [
        'headers' => [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36',
            'Accept' => 'text/html',
            'Accept-Language'=> 'en-US,en;q=0.5',
            'Accept-Encoding'=> 'gzip, deflate, identity',
            'X-SocialBu' => 'true',
        ],
        'timeout' => 30,
        'allow_redirects' => [
            'max' => 10,
        ],
        'proxy' => $randomProxy, // use a random proxy if set
    ];

    $guzzle = guzzle_client($config);
    try {
        $response = $guzzle->get($url);
        $original_body = (string) $response->getBody();

        $str = $original_body;

    } catch (\Exception $exception){
        if($tries >=3){
            throw $exception;
        } else {
            sleep(1);
        }
        return fetch_html($url, $tries + 1);
    }

    return $str;
}

/**
 * @throws Exception
 */
function fetch_html_with_cloudflare($url, $tries = 1): string
{
    $cloudflare_account_id = config('services.cloudflare.account_id');

    $guzzle = guzzle_client([
        'headers' => [
            'Authorization' => 'Bearer ' . config('services.cloudflare.api_token'),
        ],
    ]);

    try {
        $response = $guzzle->post("https://api.cloudflare.com/client/v4/accounts/{$cloudflare_account_id}/browser-rendering/content", [
            'query' => [
                'cacheTTL' => 60 * 60, // 1 hour
            ],
            'json' => [
                'url' => $url,
                'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36',
                'setExtraHTTPHeaders' => [
                    'X-SocialBu' => 'true',
                ],
            ],
        ]);

        $json = json_decode((string)$response->getBody(), true);

        $html = (string) $json['result'] ?? '';

        if(!$html){
            throw new \Exception('No HTML returned from Cloudflare for ' . $url);
        }

        $BAD_RESPONSES = [
            'Access Denied',
            '403 Forbidden',
            'Error 403',
            'Error 404',
            'Not Found',
            'Attention Required!', // Cloudflare error page
        ];

        foreach ($BAD_RESPONSES as $badResponse) {
            if (stripos($html, $badResponse) !== false) {
                throw new \Exception('Cloudflare returned an error for ' . $url . ': ' . $badResponse);
            }
        }

        return $html;

    } catch (\Exception $exception) {
        // we will retry
        if ($tries >= 3) {
            \Log::error('Failed to extract meta tags (using cloudflare) from ' . $url . ' after 3 tries');
            \Log::error($exception->getMessage());
            throw new Exception('Failed to extract meta tags (using cloudflare) from ' . $url . ': ' . $exception->getMessage());
        } else { // retry like 3 times with 1 s delay before failing
            sleep(2);
            return fetch_html_with_cloudflare($url, $tries + 1);
        }
    }
}

/**
 * @param $url
 * @return array
 * @throws Exception
 */
function extract_meta_tags($url): array
{
    //check if the url doesn't start with http:// or https:// append it
    if(!Str::startsWith($url, ['http://', 'https://'])){
        $url = 'https://' . $url;
    }

    if(!app()->environment('production')) {
        // in non-production environments, we use the custom function
        try {
            $str = fetch_html($url);
        } catch (\Exception $exception) {
            \Log::error('Failed to extract meta tags from ' . $url . ': ' . $exception->getMessage());
            throw new Exception('Failed to extract meta tags from ' . $url . ': ' . $exception->getMessage());
        }
    } else {
        try {
            $str = fetch_html_with_cloudflare($url);
        } catch (\Exception $exception) {
            $str = fetch_html($url);
        }
    }

    // parse the meta-tags from the string
    return parse_meta_tags($str);
}

function parse_meta_tags($str): array
{
    $data = [];

    if(trim($str) == '') return $data;

    // also include title, used by ui for preview
    $title = null;
    libxml_use_internal_errors(true);
    $dom = new DOMDocument();

    $dom->loadHTML(mb_convert_encoding($str, 'HTML-ENTITIES', 'UTF-8')); // load the dom

    $titleTags = $dom->getElementsByTagName("title");
    if ($titleTags->length > 0) { // team
        $title = trim($titleTags->item(0)->textContent);
    }

    if($title)
        $data['title'] = $title;

    $metaTags = $dom->getElementsByTagName('meta');
    if($metaTags->length > 0){
        foreach ($metaTags as $metaTag){
            /** @var DOMNode $metaTag */
            $attrs = $metaTag->attributes;

            $name = optional($attrs->getNamedItem('name'))->textContent;
            if(!$name)
                $name = optional($attrs->getNamedItem('property'))->textContent;
            if(!$name)
                $name = optional($attrs->getNamedItem('http-equiv'))->textContent;

            if(!$name || !is_string($name)){
                continue;
            }

            $value = optional($attrs->getNamedItem('content'))->textContent;
            if($value)
                $data[$name] = $value;
        }
    }
    return $data;
}


function email_to_name($email){
    $username = strstr($email, '@', true);
    $username = str_replace([
        '.',
        '_',
    ], [
        ' ',
        ' ',
    ], $username);
    if(!$username || empty($username)) return $email;
    return title_case($username);
}

function is_valid_unix_timestamp($timestamp)
{
    return ((string) (int) $timestamp === $timestamp)
        && ($timestamp <= PHP_INT_MAX)
        && ($timestamp >= ~PHP_INT_MAX);
}

/**
 * @param \Carbon\Carbon $lastRequestTimestamp
 * @param int $currAttempt
 * @param int $secondsToDelay
 * @param float|int $maxDelaySeconds
 * @return bool
 */
function should_poll_linear(\Carbon\Carbon $lastRequestTimestamp, $currAttempt = 1, $secondsToDelay = 60, $maxDelaySeconds = 60 * 60 * 6){
    $now = now();
    $seconds = $secondsToDelay * $currAttempt;
    if($seconds > $maxDelaySeconds)
        $seconds = $maxDelaySeconds;
    $pollAt = $lastRequestTimestamp->addSeconds($seconds);
    if($pollAt <= $now){
        // now is the time to execute the callback
        return true;
    }
    return false;
}

/**
 * @param \Carbon\Carbon $lastRequestTimestamp
 * @param int $currAttempt
 * @param int $secondsToDelay
 * @param float|int $maxDelaySeconds
 * @return bool
 */
function should_poll_exponential(\Carbon\Carbon $lastRequestTimestamp, $currAttempt = 1, $secondsToDelay = 30, $maxDelaySeconds = 60 * 60 * 6){
    $now = now();
    $seconds = pow(2, $currAttempt) * $secondsToDelay;
    if($seconds > $maxDelaySeconds)
        $seconds = $maxDelaySeconds;
    $pollAt = $lastRequestTimestamp->addSeconds($seconds);
    if($pollAt <= $now){
        // now is the time to execute the callback
        return true;
    }
    return false;
}

/**
 * Notifies our chat channel about important updates in realtime
 * @param $msg
 * @param string $channel
 */
function notify_chat($msg, string $channel = 'customer_success'){
    // \Log::info('notify_chat: ' . $msg);
    if(!app()->environment('production')) {
        // dont do anything if not production
        return;
    }
    try {
        $guzzle = guzzle_client([
            'timeout' => 15,
        ]);
        $guzzle->post(config('app.chat_update.url.' . $channel), [
            \GuzzleHttp\RequestOptions::FORM_PARAMS => [
                config('app.chat_update.param') => $msg,
            ],
        ]);
    } catch (\Exception $exception){
        report($exception);
    }
}

function hex_to_rgb($hex, $alpha = false) {
    $hex      = str_replace('#', '', $hex);
    $length   = strlen($hex);
    $rgb = [];
    $rgb['r'] = hexdec($length == 6 ? substr($hex, 0, 2) : ($length == 3 ? str_repeat(substr($hex, 0, 1), 2) : 0));
    $rgb['g'] = hexdec($length == 6 ? substr($hex, 2, 2) : ($length == 3 ? str_repeat(substr($hex, 1, 1), 2) : 0));
    $rgb['b'] = hexdec($length == 6 ? substr($hex, 4, 2) : ($length == 3 ? str_repeat(substr($hex, 2, 1), 2) : 0));
    if ( $alpha ) {
        $rgb['a'] = $alpha;
    }
    return $rgb;
}

function is_active_url($url){

    // check if its active
    if(\Validator::make(['url' => $url,], [
        'url' => 'active_url'
    ])->fails()){
        return false;
    }

    return true;
}

function get_valid_time_units(){
    return  [
        "mins",
        "minutes",
        "minute",
        "min",
        "hr",
        "hour",
        "hours",
        "hrs",
        "day",
        "days",
        "week",
        "weeks",
        "month",
        "months",
        "year",
        "years",
        "yrs",
        "yr"
    ];
}
/**
 * @param \Carbon\Carbon $cb
 * @param $string
 * @return \Carbon\Carbon
 * @throws Exception
 */
function modify_relative_time(\Carbon\Carbon $cb, $string){
    $string = trim($string);
    $validTimeUnits = get_valid_time_units();
    // add
    if(starts_with($string, '+')){
        if(preg_match('/\+(\d+)\s*?(\w+)/', $string, $match)){
            if($match[0] == $string){

                if(in_array($match[2], ['min', 'mins', 'minute', 'minutes',])){
                    return $cb->addMinutes($match[1]);
                } else if(in_array($match[2], ['hr', 'hrs', 'hour', 'hours',])){
                    return $cb->addHours($match[1]);
                } else if(in_array($match[2], ['day', 'days',])){
                    return $cb->addDays($match[1]);
                } else if(in_array($match[2], ['week', 'weeks',])){
                    return $cb->addWeeks($match[1]);
                } else if(in_array($match[2], ['month', 'months',])){
                    return $cb->addMonths($match[1]);
                } else if(in_array($match[2], ['yr', 'yrs', 'year', 'years',])){
                    return $cb->addYears($match[1]);
                }

            }
        }
    }
    throw new Exception('Invalid format or parameters: ' . $string);
}

/**
 * @return array
 */
function get_timezones(){
    // return all timezones with backward compatibility
    return timezone_identifiers_list(\DateTimeZone::ALL_WITH_BC);
}

/**
 * @param $tzid
 * @return bool
 */
function is_valid_timezone($tzid){
    return in_array($tzid, get_timezones());
}

/**
 * Record event in analytics (GA)
 * @param $name string
 * @param $data null|string|array
 * @return void
 */
function record_usage_event(string $name, $data = null){
    $all = session()->get('pending_usage_events', []);
    $all[] = [
        'name' => $name,
        'data' => $data ? $data : '',
    ];
    session()->put('pending_usage_events', $all);
}

function trigger_team_activity(\App\Team $team = null, \App\User $currentUser = null){
    if(!$team){ return; }
    try {
        foreach ($team->getAdmins() as $admin) {
            if (($currentUser && $currentUser->id !== $admin->id) || !$currentUser) {
                EmailListHelper::getInstance()->sendEvent($admin, 'team_activity', $team->name);
            }
        }
    } catch (\Exception $exception){
        report($exception);
    }
}

/**
 * @return \Illuminate\Database\Connection
 */
function get_insights_db(){
    return \DB::connection('mysql_insights');
}

function ensure_utf8($string){
    $encoding = mb_detect_encoding($string, 'UTF-8, ISO-8859-1, WINDOWS-1252, WINDOWS-1251', true);
    if ($encoding !== 'UTF-8') {
        $string = iconv($encoding, 'UTF-8//IGNORE', $string);
    }
    return $string;
}

/**
 * Save a temporary uploaded cloud file to unique/permanent cloud storage and return new path
 * We don't delete temp file as that is taken care by our cron
 * @param $tempMediaKey
 * @return string
 * @throws Exception
 */
function save_temporary_upload($tempMediaKey){

    if(!\Storage::cloud()->exists($tempMediaKey)){
        throw new \Exception('File not found');
    }

    // make sure it starts with temporary/
    if(!Str::startsWith($tempMediaKey, 'temporary/')){
        throw new \Exception('Invalid file path');
    }

    // copy media to new path (should always be unique)
    $newPath = 'attachments/cp_' . Str::random() . '_' . basename($tempMediaKey);

    \Storage::cloud()->copy($tempMediaKey, $newPath);

    return $newPath;
}

/**
 * This method uploads a given file to our cloud and returns the array (just like how our web component does it)
 * @param UploadedFile $file
 * @return array
 */
function upload_file_to_cloud(UploadedFile $file){

    $extension = $file->getClientOriginalExtension();
    $fileName = $file->getClientOriginalName();
    $mimeType = $file->getClientMimeType();
    $size = $file->getSize();

    $width = null;
    $height = null;

    // if it's a video file
    if (Str::contains($mimeType, 'video')){
        while(true){
            try {
                // validate video
                $ffprobe = FFProbe::create();

                if(!$ffprobe->isValid($file->getRealPath())){
                    break;
                }

                $videoStream = $ffprobe->streams($file->getRealPath())->videos()->first();

                if(!$videoStream){
                    break;
                }

                // validate dimensions
                $width = $videoStream->getDimensions()->getWidth();
                $height = $videoStream->getDimensions()->getHeight();

            } catch (\Exception $exception){
                report($exception);
            }
            break;
        }
    } else if(Str::contains($mimeType, 'image')){
        // is image
        $img = \Intervention\Image\Facades\Image::make($file->getRealPath());

        try {
            $width = $img->width();
            $height = $img->height();
        } catch (\Exception $exception){
            report($exception);
        }
    }

    $fileName = time() . '_' . str_random(40) . sha1(time() . $fileName . $mimeType ) . '.' . $extension;
    $path = \Storage::cloud()->putFileAs('temporary', $file, $fileName); // we save the file in cloud storage

    // create the object; this is for all our cloud media files
    return [
        'name' => $fileName,
        'path' => $path,
        'key' => $path,
        'secureKey' => encrypt($path),
        'uploadedMedia' => true,
        'temporary' => true,
        'size' => $size,
        'mimeType' => $mimeType,
        'extension' => $extension,
        '_metaData' => array_filter([
          'width' => $width,
          'height' => $height,
        ]),
        'type' => $extension,
        'ext' => $extension,
        'mime' => $mimeType,
    ];
}

if(! function_exists('base64_url_encode')) {
    function base64_url_encode( $data ) {
        return strtr( base64_encode($data), '+/=', '-_,' );
    }
}

if(! function_exists('base64_url_decode')) {
    function base64_url_decode( $data ) {
        return base64_decode( strtr($data, '-_,', '+/=') );
    }
}

/**
 * @param array $attachments
 * @return array
 */
function transform_attachments(array $attachments = []){
    $mimes = new Mimey\MimeTypes;
    $_attachments = [];
    if(count($attachments)){
        foreach($attachments as $index => $_attachment){
            $_attachment['url'] = app()->environment('production') ? \Storage::temporaryUrl($_attachment['path'], now()->addDays(1)) : \Storage::url($_attachment['path']);

            // bug: when `type` is actually mime instead of extension
            if(Str::contains($_attachment['type'], '/')){
                $_attachment['type'] = explode('/', $_attachment['type'])[1];
            }

            // better name
            if( isset($_attachment['name']) && str_starts_with($_attachment['name'], 'php') ){
                $_attachment['name'] = 'file' . ($index + 1) . '.' . $_attachment['type'];
            }

            // if mime not set, set it (extract from extension)
            if(!isset($_attachment['mime'])){
                $_attachment['mime'] = $mimes->getMimeType($_attachment['type']); // type has extension
            }

            $_attachments[] = $_attachment;
        }
    }
    return $_attachments;
}

/**
 * @param $query \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Relations\Relation
 * @param $page int
 * @param $perPage int
 * @param $transformCallback callable|null
 * @return array
 */
function get_paginated_data($query, int $page = 1, int $perPage = 20, callable $transformCallback = null){

    $paginated = $query->paginate($perPage, ['*'], 'page', $page);

    $data = [];
    foreach ($paginated->items() as $item) {
        $data[] = $transformCallback ? $transformCallback($item) : $item;
    }

    return [
        'items' => $data,
        'currentPage' => $paginated->currentPage(),
        'lastPage' => $paginated->lastPage(),
        'nextPage' => $paginated->hasMorePages() ? $paginated->currentPage() + 1 : null,
        'total' => $paginated->total(),
    ];
}
