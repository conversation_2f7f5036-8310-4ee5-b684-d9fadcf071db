
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W4ZPBDF"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

@if(app()->environment('production'))
    @push('footer_html')
        <!-- Google Tag Manager -->
        <script>
            function initGTM() {
                if (window.gtmDidInit) {
                    return false;
                }
                window.gtmDidInit = true; // flag to ensure script does not get added to DOM more than once.

                // original code
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.defer=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-W4ZPBDF');
            }

            function initGTMOnEvent(event) {
                initGTM();
                event.currentTarget.removeEventListener(event.type, initGTMOnEvent); // remove the event listener that got triggered
            }

            document.addEventListener('DOMContentLoaded', () => {
                /** init gtm after x seconds - this could be adjusted */
                setTimeout(initGTM, 1000);
            });
            document.addEventListener('scroll', initGTMOnEvent);
            document.addEventListener('mousemove', initGTMOnEvent);
            document.addEventListener('touchstart', initGTMOnEvent);
            document.addEventListener('click', initGTMOnEvent);
        </script>
        <!-- End Google Tag Manager -->

        <!-- cookie consent -->
        <script>
            (function(){

                const EXCLUDE_URLS = [
                    'publish/share',
                    'publish/extension_editor',
                ];

                // Check if the current URL contains any of the excluded URLs
                if (EXCLUDE_URLS.some(excludedUrl => window.location.href.includes(excludedUrl))) {
                    return; // Exit if the current URL matches any of the excluded URLs
                }

                const loadCss = function(cssId, href){
                    if (!document.getElementById(cssId))
                    {
                        const head  = document.getElementsByTagName('head')[0];
                        const link  = document.createElement('link');
                        link.id   = cssId;
                        link.rel  = 'stylesheet';
                        link.type = 'text/css';
                        link.href = href;
                        link.media = 'all';
                        head.appendChild(link);
                    }
                };
                const loadJS = function( src, defer, cb ){
                    "use strict";
                    const ref = document.getElementsByTagName( "script" )[ 0 ];
                    const script = document.createElement( "script" );

                    script.src = src;
                    if(defer){
                        script.defer = true;
                    } else {
                        script.async = true;
                    }
                    ref.parentNode.insertBefore( script, ref );

                    if (cb && typeof(cb) === "function") {
                        script.onload = cb;
                    }
                    return script;
                };
                loadCss('cookieconsentCSS', 'https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@3.1.0/dist/cookieconsent.css');
                loadJS("https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@3.1.0/dist/cookieconsent.umd.js", true, function() {
                    window.addEventListener("load", function(){

                        const CAT_NECESSARY = "necessary";
                        const CAT_ANALYTICS = "analytics";
                        const CAT_ADVERTISEMENT = "advertisement";
                        const CAT_FUNCTIONALITY = "functionality";
                        const CAT_SECURITY = "security";

                        const SERVICE_AD_STORAGE = 'ad_storage'
                        const SERVICE_AD_USER_DATA = 'ad_user_data'
                        const SERVICE_AD_PERSONALIZATION = 'ad_personalization'
                        const SERVICE_ANALYTICS_STORAGE = 'analytics_storage'
                        const SERVICE_FUNCTIONALITY_STORAGE = 'functionality_storage'
                        const SERVICE_PERSONALIZATION_STORAGE = 'personalization_storage'
                        const SERVICE_SECURITY_STORAGE = 'security_storage'

                        // Define dataLayer and the gtag function.
                        window.dataLayer = window.dataLayer || [];
                        function updateData(){dataLayer.push(arguments);}

                        // Set default consent to 'denied' (this should happen before changing any other dataLayer)
                        updateData('consent', 'default', {
                            [SERVICE_AD_STORAGE]: 'denied',
                            [SERVICE_AD_USER_DATA]: 'denied',
                            [SERVICE_AD_PERSONALIZATION]: 'denied',
                            [SERVICE_ANALYTICS_STORAGE]: 'denied',
                            [SERVICE_FUNCTIONALITY_STORAGE]: 'denied',
                            [SERVICE_PERSONALIZATION_STORAGE]: 'denied',
                            [SERVICE_SECURITY_STORAGE]: 'denied',
                        });

                        /**
                         * Update gtag consent according to the users choices made in CookieConsent UI
                         */
                        function updateGtagConsent() {
                            updateData('consent', 'update', {
                                [SERVICE_ANALYTICS_STORAGE]: CookieConsent.acceptedService(SERVICE_ANALYTICS_STORAGE, CAT_ANALYTICS) ? 'granted' : 'denied',
                                [SERVICE_AD_STORAGE]: CookieConsent.acceptedService(SERVICE_AD_STORAGE, CAT_ADVERTISEMENT) ? 'granted' : 'denied',
                                [SERVICE_AD_USER_DATA]: CookieConsent.acceptedService(SERVICE_AD_USER_DATA, CAT_ADVERTISEMENT) ? 'granted' : 'denied',
                                [SERVICE_AD_PERSONALIZATION]: CookieConsent.acceptedService(SERVICE_AD_PERSONALIZATION, CAT_ADVERTISEMENT) ? 'granted' : 'denied',
                                [SERVICE_FUNCTIONALITY_STORAGE]: CookieConsent.acceptedService(SERVICE_FUNCTIONALITY_STORAGE, CAT_FUNCTIONALITY) ? 'granted' : 'denied',
                                [SERVICE_PERSONALIZATION_STORAGE]: CookieConsent.acceptedService(SERVICE_PERSONALIZATION_STORAGE, CAT_FUNCTIONALITY) ? 'granted' : 'denied',
                                [SERVICE_SECURITY_STORAGE]: CookieConsent.acceptedService(SERVICE_SECURITY_STORAGE, CAT_SECURITY) ? 'granted' : 'denied',
                            });
                        }

                        CookieConsent.run({
                            // See: https://cookieconsent.orestbida.com/reference/configuration-reference.html#guioptions
                            // ...
                            guiOptions: {
                                consentModal: {
                                    layout: 'box',
                                    position: 'bottom left',
                                    flipButtons: false,
                                    equalWeightButtons: false
                                }
                            },

                            // Trigger consent update when user choices change
                            onFirstConsent: () => {
                                updateGtagConsent();
                            },
                            onConsent: () => {
                                updateGtagConsent();
                            },
                            onChange: () => {
                                updateGtagConsent();
                            },

                            // Configure categories and services
                            categories: {
                                [CAT_NECESSARY]: {
                                    enabled: true,  // this category is enabled by default
                                    readOnly: true,  // this category cannot be disabled
                                },
                                [CAT_ANALYTICS]: {
                                    enabled: true,
                                    readOnly: true,
                                    autoClear: {
                                        cookies: [
                                            {
                                                name: /^_ga/,   // regex: match all cookies starting with '_ga'
                                            },
                                            {
                                                name: '_gid',   // string: exact cookie name
                                            }
                                        ]
                                    },
                                    // See: https://cookieconsent.orestbida.com/reference/configuration-reference.html#category-services
                                    services: {
                                        [SERVICE_ANALYTICS_STORAGE]: {
                                            label: 'Enables storage (such as cookies) related to analytics e.g. visit duration.',
                                        }
                                    }
                                },
                                [CAT_ADVERTISEMENT]: {
                                    enabled: true,
                                    services: {
                                        [SERVICE_AD_STORAGE]: {
                                            label: 'Enables storage (such as cookies) related to advertising.',
                                        },
                                        [SERVICE_AD_USER_DATA]: {
                                            label: 'Sets consent for sending user data related to advertising to Google.',
                                        },
                                        [SERVICE_AD_PERSONALIZATION]: {
                                            label: 'Sets consent for personalized advertising.',
                                        },
                                    }
                                },
                                [CAT_FUNCTIONALITY]: {
                                    enabled: true,
                                    readOnly: true,
                                    services: {
                                        [SERVICE_FUNCTIONALITY_STORAGE]: {
                                            label: 'Enables storage that supports the functionality of the website or app e.g. language settings.',
                                        },
                                        [SERVICE_PERSONALIZATION_STORAGE]: {
                                            label: 'Enables storage related to personalization e.g. video recommendations.',
                                        },
                                    }
                                },
                                [CAT_SECURITY]: {
                                    enabled: true,
                                    readOnly: true,
                                    services: {
                                        [SERVICE_SECURITY_STORAGE]: {
                                            label: 'Enables storage related to security such as authentication functionality, fraud prevention, and other user protection.',
                                        },
                                    }
                                }
                            },

                            language: {
                                default: 'en',
                                translations: {
                                    en: {
                                        // See: https://support.google.com/tagmanager/answer/10718549?hl=en
                                        consentModal: {
                                            title: 'We use cookies',
                                            description: 'This website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only after consent.',
                                            acceptAllBtn: 'Accept all',
                                            acceptNecessaryBtn: 'Reject all',
                                            showPreferencesBtn: 'Manage Individual preferences'
                                        },
                                        preferencesModal: {
                                            title: 'Manage cookie preferences',
                                            acceptAllBtn: 'Accept all',
                                            acceptNecessaryBtn: 'Reject all',
                                            savePreferencesBtn: 'Accept current selection',
                                            closeIconLabel: 'Close modal',
                                            sections: [
                                                {
                                                    title: "Cookie usage",
                                                    description: "We use cookies to ensure the basic functionalities of the website and to enhance your online experience."
                                                },
                                                {
                                                    title: "Strictly necessary cookies",
                                                    description: "These cookies are essential for the proper functioning of the website, for example for user authentication.",
                                                    linkedCategory: CAT_NECESSARY,
                                                },
                                                {
                                                    title: "Analytics",
                                                    description: 'Cookies used for analytics help collect data that allows services to understand how users interact with a particular service. These insights allow services both to improve content and to build better features that improve the user’s experience.',
                                                    linkedCategory: CAT_ANALYTICS,
                                                    cookieTable: {
                                                        headers: {
                                                            name: "Name",
                                                            domain: "Service",
                                                            description: "Description",
                                                            expiration: "Expiration"
                                                        },
                                                        body: [
                                                            {
                                                                name: "_ga",
                                                                domain: "Google Analytics",
                                                                description: "Cookie set by <a href=\"https://business.safety.google/adscookies/\">Google Analytics</a>",
                                                                expiration: "Expires after 12 days"
                                                            },
                                                            {
                                                                name: "_gid",
                                                                domain: "Google Analytics",
                                                                description: "Cookie set by <a href=\"https://business.safety.google/adscookies/\">Google Analytics</a>",
                                                                expiration: "Session"
                                                            }
                                                        ]
                                                    }
                                                },
                                                {
                                                    title: 'Advertising',
                                                    description: 'Google uses cookies for advertising, including serving and rendering ads, personalizing ads (depending on your ad settings at <a href=\"https://g.co/adsettings\">g.co/adsettings</a>), limiting the number of times an ad is shown to a user, muting ads you have chosen to stop seeing, and measuring the effectiveness of ads.',
                                                    linkedCategory: CAT_ADVERTISEMENT,
                                                },
                                                {
                                                    title: 'Functionality',
                                                    description: 'Cookies used for functionality allow users to interact with a service or site to access features that are fundamental to that service. Things considered fundamental to the service include preferences like the user’s choice of language, product optimizations that help maintain and improve a service, and maintaining information relating to a user’s session, such as the content of a shopping cart.',
                                                    linkedCategory: CAT_FUNCTIONALITY,
                                                },
                                                {
                                                    title: 'Security',
                                                    description: 'Cookies used for security authenticate users, prevent fraud, and protect users as they interact with a service.',
                                                    linkedCategory: CAT_SECURITY,
                                                },
                                                {
                                                    title: 'More information',
                                                    description: 'For more details about our policy on cookies and your choices, please <a target="_blank" href="https://socialbu.com/about/cookies">read our cookies policy</a>.'
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        });

                    });
                });

            })();
        </script>

    @endpush
@endif
