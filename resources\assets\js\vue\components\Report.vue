<template>
    <div class="reports" ref="reports">
        <div class="row">
            <div class="col-12 col-md-12 pb-6">
                <div class="d-flex justify-content-between align-items-center position-sticky position-sticky-top-fix bg-white mb-5" style="z-index: 1">

                    <h3 class="mb-0">
                        <span v-if="type === 'complete'"> Complete Performance Report </span>
                        <span v-else-if="type === 'content'"> Content Performance </span>
                        <span v-else-if="type === 'account'"> Account Performance </span>
                        <span v-else-if="type === 'network'"> Network Performance {{filter.network ? '(' + capitalize(filter.network.split('.')[0]) + ')' : ''}}  </span>
                        <span v-else-if="type === 'team'"> Team Performance </span>
                    </h3>

                    <div class="d-flex">
                        <div>
                            <button
                                class="btn btn-sm btn-outline-secondary ml-1"
                                title="Filter report"
                                v-tooltip
                                @click="openFilterForm"
                            >
                            <div class="d-flex align-items-center">
                                <i class="ph ph-funnel ph-md mr-2"></i>
                                Filter
                            </div>
                            </button>
                            <!-- added filter count -->
                            <span class="badge badge-secondary badge-number" v-if="filterCount">
                                {{filterCount}}
                            </span>
                        </div>
                    </div>
                </div>
                <div v-html="spinnerHtml" v-if="loading"></div>
                <div v-else-if="errorCode">
                    <div class="alert alert-warning border border-warning" role="alert">
                        <h4 class="alert-heading">
                            <span v-if="errorCode === 'access_needed'">
                                <i class="ph ph-warning"></i>
                                Access needed
                            </span>
                            <span v-else-if="errorCode === 'upgrade_needed'">
                                <i class="ph ph-lock"></i>
                                Upgrade needed
                            </span>
                        </h4>
                        <p class="lead"
                           v-if="errorCode === 'access_needed'">
                            You don't have access to this report. Please select a team where you have access to see the report.
                        </p>
                        <p class="lead"
                           v-else-if="errorCode === 'upgrade_needed'">
                            You don't have access to this report. Please select a team where you have access to see the report or upgrade your account to see the report for your team or accounts.
                        </p>
                        <a class="alert-link text-uppercase" target="_blank" href="/app/settings#billing"
                           v-if="errorCode === 'upgrade_needed'">
                            Click Here To Upgrade Your Subscription
                        </a>
                    </div>
                </div>
                <div v-else class="row">
                    <div class="col-12 order-1 order-md-0">
                        <ContentPerformance
                            :filter="{...filter}"
                            :accounts="accounts"
                            v-if="type === 'content'"
                            ref="reportComponent"
                        />
                        <AccountPerformance
                            :filter="{...filter}"
                            :accounts="accounts"
                            v-else-if="type === 'account'"
                            ref="reportComponent"
                        />
                        <NetworkPerformance
                            :filter="{...filter}"
                            :accounts="accounts"
                            v-if="type === 'network'"
                            ref="reportComponent"
                        />
                        <TeamPerformance
                            v-else-if="type === 'team'"
                            :filter="{...filter}"
                            :accounts="accounts"
                            ref="reportComponent"
                        />
                    </div>
                </div>

                <!-- filter form modal -->
                <div id="filter_form" class="modal fade" role="dialog" tabindex="-1" aria-hidden="true" >
                    <div class="modal-dialog modal-dialog-slideout custom-modal-width right">
                        <div class="modal-content">
                            <div class="modal-header px-md-5 px-4 pt-5 pb-4">
                                <h5 class="modal-title">
                                    Filter Report
                                </h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <i class="ph ph-x ph-md"></i>
                                </button>
                            </div>
                            <div class="modal-body px-md-5 px-4">

                                <FilterForm
                                    title="Filter Report"
                                    @reset="resetFilter"
                                    @filter="applyFilter"
                                    :fields="filterFormFields"
                                    :filterBy="{...filter}"
                                    :accounts="accounts"
                                    ref="filterForm"
                                />

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Highcharts from "highcharts";
import noDataToDisplayInit from 'highcharts/modules/no-data-to-display';
import exportingInit from 'highcharts/modules/exporting';
import wordcloud from 'highcharts/modules/wordcloud';
import moment from "moment";
import {spinnerHtml, axios} from '../../components'
import ContentPerformance from "./analyze/ContentPerformance.vue";
import AccountPerformance from "./analyze/AccountPerformance.vue";
import NetworkPerformance from "./analyze/NetworkPerformance.vue";
import TeamPerformance from "./analyze/TeamPerformance.vue";
import FilterForm from "./common/FilterForm";
import {getAccounts} from "../../data";
import { capitalize } from "lodash";

exportingInit(Highcharts)
noDataToDisplayInit(Highcharts)
wordcloud(Highcharts)
Highcharts.setOptions({
    credits: {
        enabled: false,
    },
    exporting: {
        buttons: {
            contextButton: {
                menuItems: [
                    "viewFullscreen",
                    "downloadPDF",
                    "downloadPNG",
                    "downloadJPEG",
                    "exitFullscreen",
                ],
            },
        },
    },
    legend: {
        itemStyle: {"color": "#333333", "cursor": "pointer", "fontSize": "12px", "fontWeight": "normal", "textOverflow": "ellipsis"}
    },
    tooltip: {
        dateTimeLabelFormats: {
            day: "%A, %b %e, %Y",
            hour: "%H:%M %P",
            millisecond: "%A, %b %e, %l %P",
            minute: "%A, %b %e, %l %P",
            month: "%B %Y",
            second: "%A, %b %e, %l %P",
            week: "Week from %A, %b %e, %Y",
            year: "%Y",
        },
    },
    xAxis: {
        type: "datetime",
        dateTimeLabelFormats: {
            millisecond: "%l:%M %P",
            second: "%l:%M %P",
            minute: "%l:%M %P",
            hour: "%H:%M %P",
            day: "%e. %b",
            week: "%e. %b",
            month: "%b '%y",
            year: "%Y",
        }
    },
    yAxis: {
        // title: {
        //     text: "Numbers",
        // },
        min: 0,
        allowDecimals: false,
        gridLineWidth: 0,
        lineWidth: 1,
    },
    plotOptions: {
        pie: {
            allowPointSelect: true,
            cursor: "pointer",
            // colors: ['#6699fd','#af97f8','#196c6e','#400c68'],
            dataLabels: {
                enabled: true,
                softConnector: false,
                format: "{point.percentage:.1f}%",
            },
            showInLegend: true,
        },
    },
});

let axiosInterceptor = null;
export default {
    name: "Report",
    components: {
        ContentPerformance,
        AccountPerformance,
        NetworkPerformance,
        TeamPerformance,
        FilterForm,
    },
    data() {
        return {
            accounts: [], // available accounts
            loading: true,
            errorCode: null, // access_needed = when team is selected but user don't have access, upgrade_needed = when user needs upgrade
            type: "content", // type of report
            filter: {
                start: moment().subtract(1, "month").format("YYYY-MM-DD"),
                end: moment().format("YYYY-MM-DD"),
                team: null,
                network: null,
                accounts: []
            }
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        filterFormFields(){
            if(this.type === 'team'){
                const all = [
                    "start",
                    "end",
                    "team",
                ];
                if(this.filter.team){
                    all.push("accounts");
                }
                return all;
            } else if(this.type === 'network'){
                return [
                    "start",
                    "end",
                    "network",
                    "team",
                    "accounts",
                ];
            } else {
                return [
                    "start",
                    "end",
                    "team",
                    "accounts"
                ];
            }
        },
        filterQuery() {
            const filters = [];
            if(this.filter.network){
                filters.push("network=" + encodeURIComponent(this.filter.network));
            }
            if (this.filter.team) {
                filters.push("team=" + encodeURIComponent(this.filter.team));
            }
            if (this.filter.accounts.length) {
                filters.push(...this.filter.accounts.map((id) => "accounts[]=" + encodeURIComponent(id)));
            }
            if(this.filter.start){
                filters.push("start=" + encodeURIComponent(this.filter.start));
            }
            if(this.filter.start){
                filters.push("end=" + encodeURIComponent(this.filter.end));
            }
            return filters.join("&");
        },
        filterCount() {
            let count = 0;
            if (this.filter.network) count++;
            if (this.filter.team) count++;
            if (this.filter.accounts.length) count++;
            if (this.filter.start) count++;
            if (this.filter.end) count++;
            return count;
        },
    },
    methods: {
        capitalize: capitalize,
        async initialize(type) {
            this.type = type;

            let urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has("filter")) {
                try {
                    const filter = JSON.parse(decodeURIComponent(urlParams.get("filter")));
                    Object.assign(this.filter, filter);
                } catch (e) {
                    console.error(e);
                }
            }
            this.accounts = await getAccounts();

            // open or close filter form if needed
            this.$bus.$on('open_filter_form', () => {
                this.openFilterForm();
            });
            this.$bus.$on('close_filter_form', () => {
                this.closeFilterForm();
            });
            this.$bus.$on('apply_filter', filter => {
                this.applyFilter(filter);
            });

            this.loading = false;
        },
        openFilterForm() {
            $("#filter_form").modal("show");
        },
        closeFilterForm(){
            $("#filter_form").modal("hide");
        },
        applyFilter(filter){
            this.errorCode = null; // reset any error code

            // on update, we just set a query string
            this.filter = filter;

            this.updateUrl();

            // then we update the report
            this.$nextTick(() => {
                this.$refs.reportComponent.fetchData();
                this.scrollToTop();
                // also close filter modal
                this.closeFilterForm();
            });
        },
        resetFilter() {
            this.errorCode = null; // reset any error code

            this.filter = {
                start: moment().subtract(1, "month").format("YYYY-MM-DD"),
                end: moment().format("YYYY-MM-DD"),
                team: null,
                accounts: []
            };
            this.updateUrl(true);

            // then we update the report
            this.$nextTick(() => {
                this.$refs.reportComponent.fetchData();
                this.scrollToTop();
                // also close filter modal
                this.closeFilterForm();
            });
        },
        rerenderCharts(){
            // loop all chart refs and call update method
            let reportComponent = this.$refs.reportComponent;
            let foundComponent = false;
            while(!foundComponent){
                // we find the right
                if(reportComponent.$refs.reportComponent){
                    reportComponent = reportComponent.$refs.reportComponent;
                } else {
                    foundComponent = true;
                }
            }
            this.$nextTick(()=>{
                Object.keys(reportComponent.$refs).forEach((key) => {
                    if (reportComponent.$refs[key] && reportComponent.$refs[key].chart) {
                        reportComponent.$refs[key].chart.reflow();
                    }
                });
            });
        },
        updateUrl(remove = false) {
            if(!remove) {
                const url = new URL(window.location.href);

                // remove null or empty values from filter
                const filter = Object.keys(this.filter).reduce((newObj, key) => {
                    if(this.filter[key]) {
                        if(Array.isArray(this.filter[key]) && this.filter[key].length === 0){
                            return newObj;
                        } else {
                            newObj[key] = this.filter[key];
                        }
                    }
                    return newObj;
                }, {});

                url.searchParams.set("filter", encodeURIComponent(JSON.stringify(filter)));
                window.history.replaceState({}, "", url);
            } else {
                const url = new URL(window.location.href);
                url.searchParams.delete("filter");
                window.history.replaceState({}, "", url);
            }
        },
        scrollToTop(){
            $("html, body").animate({ scrollTop: 0 }, "slow");
        }
    },
    mounted() {
        axiosInterceptor = axios.interceptors.response.use(
            (res)=>{
                const { config } = res;
                if (config.url && config.url.includes("api/")) {
                    // if we have a response from the api, we check if we need to redirect user
                    const { data } = res;
                    if (data && data.error_code && !data.success) {
                        this.errorCode = data.error_code;
                        return new Promise(res => {}); // we return a promise that never resolves
                    }
                }
                return res;
            },
            function(error) {
                // Do something with request error
                return Promise.reject(error);
            }
        );
    },
    beforeDestroy() {
        axios.interceptors.response.eject(axiosInterceptor);
    }
};
</script>

<style lang="scss">
</style>

