<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStoredHasResultToPosts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $isSqlite = DB::connection()->getDriverName() === 'sqlite';
        
        if (!$isSqlite){
            
            Schema::table('posts', function (Blueprint $table) {
                // `result` is nullable json column which is not indexed, so we can't search by it
                // `has_result` will be a stored column which will be indexed and will be used for searching
                $table->boolean('has_result')
                    ->storedAs('result IS NOT NULL')
                    ->index()
                    ->after('result');
            });
        } else {
            // For SQLite, we cannot create a stored column directly
            Schema::table('posts', function (Blueprint $table) {
    
                $table->boolean('has_result')
                    ->nullable()
                    ->default(0)
                    ->index()
                    ->after('result');
            });

        }
        
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropColumn('has_result');
        });
    }
}
