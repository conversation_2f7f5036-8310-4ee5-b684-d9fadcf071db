<?php

namespace App\Jobs;

use App\Helpers\EmailListHelper;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\MaxAttemptsExceededException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Str;

class SyncEmailListContact implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $retryAfter = 10;

    /**
     * Determine the time at which the job should timeout.
     *
     * @return \DateTime
     */
    public function retryUntil()
    {
        return now()->addMinutes(10);
    }

    /**
     * Create a new job instance.
     *
     * @param User $user
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        \Redis::funnel('sync_email_list::' . $this->user->id)
            ->releaseAfter(60 * 5) // 5 minutes
            ->limit(1)
            ->then(function () {
                try {
                    EmailListHelper::getInstance()->syncUserContact($this->user, true);

                    if($this->user->getOption('email_list_sync_error')){
                        $this->user->removeOption('email_list_sync_error');
                    }

                } catch (\Exception $exception){
                    if(Str::contains($exception->getMessage(), ['Invalid response', 'Server error', 'timed out', 'create contact', 'not valid', 'Failed', '504', '502',])){

                        $errCount = $this->user->getOption('email_list_sync_error');
                        if(!$errCount) $errCount = 0;

                        if($errCount < 20) {
                            ++$errCount;
                            $this->user->setOption('email_list_sync_error', $errCount);
                            // can be temporary, so delay the job and schedule for later
                            dispatch((new self($this->user))->delay(now()->addSeconds(60 * 2 * $errCount)));
                            return;
                        }
                    }
                    throw $exception;
                }
            }, function () {
                // do nothing: the job is already running, so we don't need to sync it again
            });
    }

    public function failed(\Exception $exception){
        if ($exception instanceof MaxAttemptsExceededException) {
            // mark as done or deleted or whatever
            $this->delete();
        }
    }
}
