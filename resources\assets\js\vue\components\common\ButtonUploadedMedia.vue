<template>
    <div>
        <input type="file" ref="inputEl" class="d-none" 
               :multiple="multiple" @change="onFilesSelected(Array.from($event.target.files))" />
        <draggable class="d-flex align-items-center flex-wrap" v-model="files" @change="updateAttachmentOrder"  ghost-class="ghost">
            <div class="custom-cursor-all-scroll mr-1 mb-1" v-for="(obj, i) in normalizedFiles" :key="i + 'uploaded_file_btn_s'">
                <uploaded-media
                    :media="obj"
                    @uploaded="media => mediaUploaded(i, media)"
                    @removed="removeMedia(i)"
                    @edit="$emit('edit', i)"
                    v-if="obj.key"
                />
                <uploaded-media
                    :file="obj"
                    @uploaded="media => mediaUploaded(i, media)"
                    @removed="removeMedia(i)"
                    @upload-started="uploadStarted()"
                    @edit="$emit('edit', i)"
                    v-else
                />
            </div>
        </draggable>
    </div>
</template>

<script>
import Compressor from "compressorjs";
import size from "filesize";
import { getImageMetadata, getVideoMetadata, alertify } from "../../../components";
import UploadedMedia from "./UploadedMedia";
import {debounce} from "lodash";
import heic2any from "heic2any"
import draggable from 'vuedraggable';
export default {
    name: "ButtonUploadedMedia",
    components: {
        UploadedMedia,
        draggable
    },
    props: ["validator", "max", "multiple", "filesToRender", "compressImage"],
    data() {
        return {
            files: [],
            uploading:false
        };
    },
    computed: {
        maximumFiles() {
            return this.max || 1;
        },
        normalizedFiles(){
            return this.files.map(f => {
                if(f instanceof File){
                    return f;
                }
                // if key is not set and path is set, set key
                // f.path is set for existing attachments of a post
                // f.key is set when a new file is uploaded using our uploader
                if(!f.key && f.path){
                    f.key = f.path;
                    delete f.path;
                }

                if(!f.extension && (f.ext || f.type)){
                    f.extension = f.ext || f.type;
                    delete f.ext;
                    delete f.type;
                }

                if(!f.mimeType && f.mime){
                    f.mimeType = f.mime;
                    delete f.mime;
                }

                return f;
            })
        }
    },
    methods: {
        selectFile() {
            // trigger input click
            this.$refs.inputEl.click();
        },
        updateAttachmentOrder(event) {
            this.$emit("change", this.normalizedFiles.filter(f => f.key).map(f=>{
                // make file backwards compatible; post composer uses these
                f.type = f.extension;
                f.ext = f.extension;
                f.mime = f.mimeType;
                return f;
            }));
        },
        onFilesSelected(files) {
            if (files.length + this.normalizedFiles.length > this.maximumFiles) {
                alertify.error("Too many files. You can only select a maximum of " + this.maximumFiles + " files.");
                this.resetFileInput();
                return;
            }

            const isVideoOrGif = file => {
                const mime = file.mimeType ? file.mimeType : file.type;
                return mime.includes("video") || mime.includes("gif");
            }

            const isDoc = file => {
                const mime = file.mimeType ? file.mimeType : file.type;
                return mime.includes("pdf") || mime.includes("doc") || mime.includes("docx") || mime.includes("ppt") || mime.includes("pptx");
            }

            if (files.length > 1 && files.find(isVideoOrGif)) {
                alertify.error("To attach the video (or a GIF), please select only 1 file.");
                this.resetFileInput();
                return;
            }

            if (files.length > 1 && files.find(isDoc)) {
                alertify.error("Only one file is supported. Please select only 1 file.");
                this.resetFileInput();
                return;
            }
            if (files.length && this.normalizedFiles.find(isDoc)) {
                alertify.error(
                    "Only one file is supported. Please remove the currently selected files and retry"
                );
                this.resetFileInput();
                return;
            }

            if (files.length && this.normalizedFiles.find(isVideoOrGif)) {
                alertify.error(
                    "You have a video (or a GIF) selected. Please remove the currently selected files and retry"
                );
                this.resetFileInput();
                return;
            }
            if (this.normalizedFiles.length && files.find(isVideoOrGif)) {
                alertify.error(
                    "For videos (and GIFs), only 1 file can be selected. Please remove the currently selected files and retry"
                );
                this.resetFileInput();
                return;
            }

            const _this = this;

            files.forEach(async file => {
                // validate file size
                if (file.size > process.env.MIX_MAX_FILE_SIZE) {
                    alertify.error(
                        "The file " +
                            file.name +
                            " is too large. Please try a file smaller than " +
                            size(process.env.MIX_MAX_FILE_SIZE)
                    );
                    this.resetFileInput(); // reset file input
                    return;
                }

                const processFile = (file, extraData = {}) => {
                    const maxSizeLimit = Number(
                        file.type.includes("image") ? process.env.MIX_MAX_IMAGE_SIZE : process.env.MIX_MAX_FILE_SIZE
                    );

                    // validate extension and file size
                    if (file.size > maxSizeLimit) {
                        alertify.error(
                            "The file " +
                                file.name +
                                " is too large. Please try a file smaller than " +
                                size(maxSizeLimit)
                        );
                        this.resetFileInput(); // reset file input
                        return;
                    }
                    file._metaData = extraData; // add data to File object

                    // validate file type / ext
                    if (_this.validator(file.type, true, extraData)) {
                        if (
                            _this.normalizedFiles.find(f => f.name === file.name && f.size === file.size && f.mimeType === file.type)
                        ) {
                            alertify.error("The file " + file.name + " is already selected.");
                            _this.resetFileInput();
                            return;
                        }
                        _this.files.push(file);
                        _this.resetFileInput();
                    } else {
                        _this.resetFileInput();
                    }
                };

                if(file.name.includes('.heic') || file.type.includes("heic")){
                    const name = file.name;
                    const blob = await heic2any({
                        blob: file,
                        toType: "image/png", // or "image/jpeg" or "image/gif"
                    });
                    file = new File(
                        [blob],
                        name,
                        {
                            type: blob.type,
                            lastModified: new Date().getTime()
                        }
                    );
                }

                if (file.type.includes("image")) {
                    // is an image
                    // file size/type check
                    if (!file.type.includes("gif") && (file.size > process.env.MIX_MAX_IMAGE_SIZE || this.compressImage)) {
                        // use compressorjs
                        
                        new Compressor(file, {
                            strict: true,
                            quality: 0.8,
                            checkOrientation: false,
                            maxWidth: 4096,
                            maxHeight: 4096,
                            convertSize: 5000000, // min-size for png to convert to jpg
                            async success(result) {
                                let file = new File([result], result.name, {
                                    type: result.type || ""
                                });
                                const metadata = await getImageMetadata(file);
                                processFile(file, metadata);
                            },
                            error(err) {
                                alertify.error(err.message);
                                _this.resetFileInput(); // reset file input
                            }
                        });
                    } else {
                        const metadata = file._metaData ? file._metaData : await getImageMetadata(file);
                        processFile(file, metadata); // original file
                    }
                } else if (file.type.includes("video")) {
                    // is a video
                    if (["video/mp4", "video/quicktime", "video/x-msvideo"].includes(file.type)) {
                        const metadata = file._metaData ? file._metaData : await getVideoMetadata(file);

                        processFile(file, metadata);
                    } else {
                        alertify.error("We recommend .MP4 video format. Please use an mp4 file.");
                        this.resetFileInput();
                    }
                } else {
                    //possibly some document
                    processFile(file);
                }
            });
        },
        addFiles(files) {
            return this.onFilesSelected(files);
        },
        setFiles(files) {
            this.files = [...files];
        },
        removeMedia(index) {
            this.files.splice(index, 1);
            this.uploading = false;
            this.notifyParent();
            this.$emit('resetMediaOptions', index);
        },
        mediaUploaded(index, data) {
            this.files.splice(index, 1, data);
            this.uploading = false;
            this.notifyParent();
        },
        uploadStarted(){
            this.uploading = true;
        },
        /**
         * @type {Function|DebouncedFunc}
         */
        notifyParent: debounce(function(){
            // only send non-file objects to parent
            this.$emit("change", this.normalizedFiles.filter(f => f.key).map(f=>{
                // make file backwards compatible; post composer uses these
                f.type = f.extension;
                f.ext = f.extension;
                f.mime = f.mimeType;
                return f;
            }));
        }, 250),
        resetFileInput() {
            try {
                this.$refs.inputEl.value = null;
            } catch (e) {}
        },
        removeAll(){
            this.resetFileInput();
            this.files = [];
            this.notifyParent();
            this.uploading = false;
        },
    },
    mounted(){
        if(this.filesToRender && this.filesToRender.length){
            this.files = this.filesToRender;
        }
    }
};
</script>

<style>
.custom-cursor-all-scroll{
    cursor: all-scroll;
}
</style>
