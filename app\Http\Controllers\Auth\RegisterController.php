<?php

namespace App\Http\Controllers\Auth;

use App\User;
use <PERSON>ail<PERSON><PERSON><PERSON>\EmailChecker;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Validator;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\RegistersUsers;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after login / registration.
     *
     * @var string
     */
    protected $redirectTo = '/app';

    public static $spamDomains = [
        'qq.com',
        'mail.ru',
        'inbox.ru',
        'support.ru',
        'bk.ru',
        'tmpbox.net',
        'knmcadibav.com',
        'growthx.me',
        'minduls.com',
        'gimpmail.com',
        'jarars.com',
        'btcours.com',
        'envoes.com',
        'bitflirt.com',
        'kytstore.com',
        'lxheir.com',
        'getnada.com',
        'wywnxa.com',
        'lassora.com',
        'jomspar.com',
        'tempmailto.org',
        'uz8.net',
        'zohomail.eu',
        'givmail.com',
        'smvmail.com',
    ];

    public static $spamNames = [
        'https:',
        'http:',
    ];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|max:255',
            'email' => 'required|email:rfc,dns|max:191|unique:users',
            'password' => 'required|min:8|confirmed|max:1000',
            'g-recaptcha-response' => 'required|string', // important: to prevent spam
        ]);
    }

    public static function verifyEmailIsGood($email = '')
    {

        if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
            return false;
        }

        if( Str::contains($email, self::$spamDomains) ){
            return false;
        }

        $checker = new EmailChecker();

        if(!$checker->isValid($email)){
            return false;
        }

        // check if email is good using api
        try {
            $emailVerifyRes = guzzle_client()->get('https://verifymail.io/api/' . urlencode($email) . '?key=57b6a8c73f9345d78232b37ed4c985c5');
            $emailVerifyData = json_decode($emailVerifyRes->getBody()->getContents(), true);
            if ($emailVerifyData['block'] || !$emailVerifyData['email_address']) {
                return false;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return true;
    }

    /**
     * Handle a registration request for the application.
     *
     * @param \Illuminate\Http\Request $request
     * @param bool $skip_verification
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     * @throws ValidationException
     */
    public function register(Request $request, bool $skip_verification = false)
    {
        $this->validator($request->all())->validate();

        if( Str::contains($request->input('name', ''), self::$spamNames) ){
            flash('Invalid email', 'error');
            return back();
        }

        if(!self::verifyEmailIsGood($request->input('email'))){
            flash('Invalid email', 'error');
            return back();
        }

        // check re-captcha
        $recaptcha = new \ReCaptcha\ReCaptcha(config('services.recaptcha.secret'));
        try {
            $resp = $recaptcha->verify($request->input('g-recaptcha-response'));
            if (!$resp->isSuccess()) {
                $errors = $resp->getErrorCodes();
                throw ValidationException::withMessages([
                    'name' => 'Invalid request: ' . implode(', ', $errors),
                ]);
            }
        } catch (\Exception $e) {
            report($e);
            // something failed. probably re-captcha server error
        }

        event(new Registered($user = $this->create($request->all())));

        if($skip_verification){
            // mark as verified and log the user in
            $user->confirmEmail();

            $this->guard()->login($user);

            return $this->registered($request, $user)
                ?: redirect($this->redirectPath());
        } else {

            flash(trans('auth.registered_verification'), 'success');

            return $this->registered($request, $user)
                ?: redirect()->route('login');
        }

    }

    /**
     * Create a new user instance after a valid registration.
     * This method is also used by team invite code
     *
     * @param  array $data
     * @return User
     */
    public function create(array $data)
    {
        $user = User::withTrashed()->where('email', $data['email'])->first();

        $new_user = null;
        if ($user && $user->trashed()) {
            $user->restore();
            $new_user = $user;
        }

        if(!$new_user) {
            $new_user = new User([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => $data['password'] === 'no_password' ? $data['password'] : bcrypt($data['password']),
            ]);
            $new_user->save();
        }

        return $new_user;
    }

    public function showRegistrationForm(){
        return view('guest.register');
    }
}
