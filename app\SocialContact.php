<?php

namespace App;

use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;

/**
 * App\SocialContact
 *
 * @property int $id
 * @property string $name
 * @property string $external_id
 * @property string|null $profile_pic
 * @property string $type
 * @property string $status
 * @property int $account_id
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact query()
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereProfilePic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SocialContact whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SocialContact extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
    */
    protected $fillable =[
        'name',
        'account_id',
        'external_id',
        'profile_pic',
        'type',
        'status'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
    */
    protected $casts = [
        'options' => 'array'
    ];

    /**
     * The attributes that are timestamps
     *
     * @var array
    */
    protected $dates = [
        'created_at',
        'updated_at'
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function account(){
        return $this->belongsTo(Account::class);
    }

    public static function transform(self $contact)
    {
        return collect([
            'id' => $contact->id,
            'name' => $contact->name,
            'external_id' => $contact->external_id,
            'profile_pic' => $contact->profile_pic,
            'type' => $contact->type,
            'status' => $contact->status,
        ]);
    }

    /**
     * Find a contact by external id and type
     *
     * @param string $type
     * @param string $externalId
     * @param int $accountId
     * @return self|null
     */
    public static function findContact(string $type, string $externalId, int $accountId): ?SocialContact
    {
        return self::whereAccountId($accountId)
            ->whereExternalId($externalId)
            ->whereType($type)
            ->first();
    }

    /**
     * Validate the contact data
     * @param array{ external_id: string, name: string, type: string, profile_pic?: string, options?: object } $data
     * @throws \Exception
     */
    public static function validateContact(array $data)
    {
        try {
            \Validator::make($data, [
                'external_id' => 'required|string', // is external id of the sender
                'name' => 'required|string',
                'type' => 'required|string', // user, page, group, organization, etc
                'profile_pic' => 'nullable|string', // the sender of the change (id*, name*, type*, profile_pic)
            ])->validate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw new \Exception('Invalid sender data: ' . array_first($e->errors()));
        }
    }

    /**
     * Get the contact from the sender data (if it doesn't exist, create it)
     * @param Account $account
     * @param array{ external_id: string, name: string, type: string, profile_pic?: string, options?: array } $sender
     * @return SocialContact
     * @throws \Exception
     */
    public static function findOrCreateContact(Account $account, array $sender): SocialContact
    {
        self::validateContact($sender);

        if($sender['external_id'] === $account->account_id){
            throw new \Exception('Cannot create contact for own social account');
        }

        // get the contact from the sender data
        $contact = self::findContact($sender['type'], $sender['external_id'], $account->id);

        if(!$contact){
            $contact = new SocialContact();
            $contact->account_id = $account->id;
            $contact->external_id = $sender['external_id'];
            $contact->name = $sender['name'];
            $contact->type = $sender['type'];
            $contact->profile_pic = $sender['profile_pic'] ?? null;

            if(isset($sender['options']) && is_array($sender['options'])){
                $contact->options = $sender['options'];
            }

            $contact->save();
        } else {
            // check if we need to update name or profile pic
            if($contact->name !== $sender['name'] || $contact->profile_pic !== $sender['profile_pic']){
                $contact->name = $sender['name'];
                $contact->profile_pic = $sender['profile_pic'] ?? null;

                if(isset($sender['options']) && is_array($sender['options'])){
                    $contact->options = $sender['options'];
                }

                $contact->save();
            }
        }

        return $contact;
    }

    public static function shouldIgnore($type, $externalId, $accountId): bool
    {
        $contact = self::findContact($type, $externalId, $accountId);

        if($contact && ($contact->isBlocked() || $contact->isSpam())){
            return true;
        }

        return false;
    }

    public function isBlocked(): bool
    {
        // if the sender is blocked, we don't create the item
        if($this->status == 'blocked'){
            return true;
        }

        return false;
    }

    public function isSpam(): bool
    {
        // if the sender is blocked, we don't create the item
        if($this->status == 'spam'){
            return true;
        }

        return false;
    }


    private function getOptions()
    {
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }
}
