<nav id="navbar" class="navbar navbar-light bg-light flex-nowrap p-0">

    <div class="container-fluid px-md-6 py-md-0 p-2">

        <div class="navbar-left">
            <!-- Menu btn for mob -->
            <i class="ph ph-list ph-lg cursor-pointer d-md-none text-sb px-2 border-right border-left border-secondary" id="sidebar_toggle"></i>

            <!-- Branding Image -->
            <a class="navbar-brand col-sm-3 col-md-2 mr-0 pl-0" href="<?php echo e(url('/app')); ?>">
                <img class="d-none d-sm-block lozad" src="/images/redesign/logo.svg" alt="<?php echo e(config('app.name', 'Laravel')); ?>" title="<?php echo e(config('app.name', 'Laravel')); ?>" />

            </a>
        </div>

        <section class="ml-auto">

            <nav class="nav nav-navbar pr-0">
                <div class="nav-item d-flex align-items-center">
                    <button class="btn btn-primary btn-sm d-flex align-items-center mr-4" type="button" id="menu_create_post_btn">
                        <i class="ph-bold ph-plus mr-2"></i> <span class="d-none d-md-inline-block">New Post</span> <span class="d-md-none">Post</span>
                    </button>
                </div>
                
                <div class="nav-item d-flex align-items-center mx-2 text-dark" data-toggle="modal" data-target="#notificationsModal" data-tour="id=navigation_notifications;title=Notifications;text=You will see your SocialBu account notifications here. You may also receive the same notifications via email">
                    <span class="rounded d-flex align-items-center cursor-pointer" title="Notifications" id="notificationsIcon">
                        <i class="ph ph-bell ph-lg"></i>
                    </span>
                </div>
                
                <div class="nav-item ml-2 text-dark">
                    <span class="rounded dropdown d-flex align-items-center hover-nav-menu cursor-pointer h-40px" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Settings, accounts, teams, and so on" data-tour="id=navigation_user_menu;title=User Menu;position=left;text=This is the user menu. You can access your social media accounts, teams, settings, and so on here.">
                        <img id="user_avatar" alt="avatar" class="avatar avatar-xs lozad" src="<?php echo e(user()->getPic()); ?>" />
                        <span class="d-none d-md-block">&nbsp; <?php echo e(user()->getFirstName()); ?></span>  <i class="ph ph-caret-down ml-1"></i>
                    </span>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a class="dropdown-item" href="<?php echo e(route('accounts.index')); ?>">
                            Social Accounts
                        </a>
                        <a class="dropdown-item" href="<?php echo e(route('teams.index')); ?>">
                            Teams
                        </a>
                        <a class="dropdown-item" href="<?php echo e(route('link_shorteners.index')); ?>">
                            Link Shorteners
                        </a>
                        <a class="dropdown-item" href="<?php echo e(route('settings')); ?>">
                            <?php echo app('translator')->get('generic.settings'); ?>
                        </a>
                        <a class="dropdown-item" href="/help" target="_blank">
                            Help
                        </a>
                        <?php if(\Auth::user()->getOption('is_affiliate')): ?>
                            <a class="dropdown-item" href="<?php echo e(route('referrals.index')); ?>">
                                Affiliates
                            </a>
                        <?php endif; ?>
                        <a class="dropdown-item" href="#" onclick="event.preventDefault();document.getElementById('logout-form').submit();">
                            Logout
                        </a>
                        <form id="logout-form" action="<?php echo e(url('/auth/logout')); ?>" method="POST" style="display: none;">
                            <?php echo e(csrf_field()); ?>

                        </form>
                    </div>
                </div>

            </nav>
        </section>

    </div>
</nav>

<?php $__env->startPush('footer_html'); ?>
<script>
    window.__loadScript("create_content");
    window.__loadComponent("notifications", "#notificationsContainer");
    window.__loadScript("chat_bu");
</script>
<?php $__env->stopPush(); ?>


<!-- modal for creating post -->
<div id="create_post__editor_container" class="d-none"></div>

<!-- modal for notifications -->
<div id="notificationsContainer" class="d-none"></div>

<!-- modal for ChatBu -->
<div id="chat_bu_container" class="d-none"></div>
<?php /**PATH C:\xampp\htdocs\socialtool\resources\views/layout/partials/user/navigation.blade.php ENDPATH**/ ?>