<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class ViralHookGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'topic',
                'label' => 'Topic',
                'placeholder' => '',
                'type' => 'text',
                'rules' => 'required|string|max:400',
                'class' => 'col-md-12',
            ],
            [
                'id' => 'emotion',
                'label' => 'Desired Emotion',
                'type' => 'select',
                'options' => [
                    ['value' => 'funny', 'label' => 'Funny','selected' => false],
                    ['value' => 'aesthetic', 'label' => 'Aesthetic', 'selected' => false],
                    ['value' => 'cool', 'label' => 'Cool', 'selected' => false],
                    ['value' => 'professional', 'label' => 'Professional', 'selected' => false],
                ],
                'rules' => 'required|in:funny,aesthetic,cool,professional',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'platform',
                'label' => 'Platform',
                'type' => 'select',
                'options' => [
                    ['value' => 'instagram', 'label' => 'Instagram', 'selected' => false],
                    ['value' => 'snapchat', 'label' => 'Snapchat', 'selected' => false],
                    ['value' => 'tiktok', 'label' => 'TikTok', 'selected' => false],
                    ['value' => 'facebook', 'label' => 'Facebook', 'selected' => false],
                    ['value' => 'linkedin', 'label' => 'LinkedIn', 'selected' => false],
                    ['value' => 'x', 'label' => 'X (Twitter)', 'selected' => false],
                    ['value' => 'threads', 'label' => 'Threads', 'selected' => false],
                    ['value' => 'youtube', 'label' => 'YouTube', 'selected' => false],
                    ['value' => 'reddit', 'label' => 'Reddit', 'selected' => false],
                    ['value' => 'bluesky', 'label' => 'Bluesky', 'selected' => false],
                    ['value' => 'pinterest', 'label' => 'Pinterest', 'selected' => false],
                    ['value' => 'mastodon', 'label' => 'Mastodon', 'selected' => false],
                    ['value' => 'google-business-profile', 'label' => 'Google Business Profile', 'selected' => false],
                ],
                'rules' => 'in:instagram,snapchat,tiktok,facebook,youtube,linkedin,x,threads,reddit,bluesky,pinterest,mastodon,google-business-profile',
                'class' => 'col-md-6',
            ],
        ];
    }


    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'Post',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json', // can be json, form, or multipart
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "You are an expert social media strategist and viral content creator. Your primary goal is to generate extremely concise, attention-grabbing hooks for social media posts that immediately stop users from scrolling. Strictly avoid all formatting within the hook text, including bolding, italics, or any other text enhancements. The hooks must be short, punchy, and sound like they were written by a human who understands internet culture and platform-specific virality. Focus on hooks that are text-only and designed for maximum impact within the first few words.

                                    For each request, provide three distinct hook options. Each option must be highly optimized for the given platform and emotion, prioritizing brevity and viral potential. Ensure each hook is on a separate line, without hashtags, quotes, or any introductory/concluding remarks.

                                    Also, do not use em dashes (—) or en dashes (–) in any of the hook options text.

                                    Here are the inputs:

                                    Topic: {{form.topic}}
                                    Desired Emotion: {{form.emotion}}
                                    Target Platform: {{form.platform}}{% if form.platform == 'x' %} (twitter){% endif %}",

                                    'Separate each hook option with "---END_HOOK---" except for the very last one.',

                                    "Here's how I communicate: I aim for directness and clarity, often using bolding to highlight key points. I want to cut to the chase and get effective results without a lot of extra words. My instructions are usually precise and I appreciate when responses reflect that same level of conciseness and focus on the core request."
                                ]))
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Posts:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ])
                ],
            ],

        ];
    }


    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];

    }

    public function outputComponents(): array
    {
        return [];
    }
}
